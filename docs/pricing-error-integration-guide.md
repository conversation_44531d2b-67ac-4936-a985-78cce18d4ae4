# Pricing Error Message Integration Guide

## Overview

This guide shows how to integrate the pricing navigation system with existing error message components to make error messages clickable and enable navigation to the corresponding input fields.

## Integration Methods

### Method 1: Enhanced Error Display Component (Recommended)

Replace existing error display templates with the enhanced component:

```typescript
// Before
@Component({
  template: `
    <rkt-alert-enterprise variant="warn">
      <ul>
        @for (error of errors(); track error) {
          <li>{{ error.path | formPathLabel:error.stateName }} {{ error.message }}</li>
        }
      </ul>
    </rkt-alert-enterprise>
  `
})

// After
@Component({
  imports: [EnhancedErrorDisplayComponent],
  template: `<app-enhanced-error-display [errors]="errors()" />`
})
```

### Method 2: Auto Pricing Links Directive

Add the directive to existing error containers:

```html
<!-- Before -->
<ul>
  @for (error of errors(); track error) {
    <li>{{ error.message }}</li>
  }
</ul>

<!-- After -->
<ul appAutoPricingLinks>
  @for (error of errors(); track error) {
    <li>{{ error.message }}</li>
  }
</ul>
```

### Method 3: Manual Field Link Directive

Add specific navigation to individual error messages:

```html
<!-- Before -->
<li>Purchase Price is required</li>

<!-- After -->
<li appPricingFieldLink="purchasePrice" linkText="Purchase Price">
  Purchase Price is required
</li>
```

### Method 4: Programmatic Integration

Use the service directly in component logic:

```typescript
import { PricingErrorNavigationService } from './services/pricing-navigation/pricing-error-navigation.service';

@Component({...})
export class MyErrorComponent {
  constructor(private errorNavService: PricingErrorNavigationService) {}

  onErrorClick(fieldPath: string) {
    this.errorNavService.navigateToFieldFromError(fieldPath, {
      focus: true,
      highlight: true
    });
  }

  isNavigable(fieldPath: string): boolean {
    return this.errorNavService.canNavigateToField(fieldPath);
  }
}
```

## Field Path Mappings

The system automatically maps common form control paths to navigation field paths:

| Form Control Path | Navigation Field Path | Display Label |
|-------------------|----------------------|---------------|
| `purchasePrice` | `purchasePrice` | Purchase Price |
| `baseLoanAmount` | `baseLoanAmount` | Base Loan Amount |
| `credits.manualLenderPaidCredit` | `credits.manualLenderPaidCredit` | Lender Paid Credit |
| `credits.realtorCredits` | `credits.realtorCredits` | Realtor Credits |
| `credits.sellerConcessions` | `credits.sellerConcessions` | Seller Concessions |
| `qualificationDetails.ltv` | `qualificationDetails.ltv` | LTV |
| `ltv` | `qualificationDetails.ltv` | LTV |
| `insuranceAmount` | `insuranceAmount` | Insurance Amount |
| `escrowWaiver` | `escrowWaiver` | Escrow Waiver |
| `anticipatedClosingDate` | `anticipatedClosingDate` | Anticipated Closing Date |
| `commitmentPeriodInDays` | `commitmentPeriodInDays` | Commitment Period |

## Text Pattern Recognition

The Auto Pricing Links directive recognizes these text patterns:

- "Purchase Price" → navigates to purchase price field
- "LTV" → navigates to LTV field
- "Seller Concessions" → navigates to seller concessions field
- "Anticipated Closing Date" → navigates to closing date field
- And many more...

## Implementation Examples

### Example 1: Validation Error Provider Integration

```typescript
// Enhanced validation error service
@Injectable()
export class EnhancedValidationErrorService extends ValidationErrorProviderService {
  private pricingErrorNav = inject(PricingErrorNavigationService);

  // Override to add navigation capabilities
  getEnhancedErrors() {
    return this.errors().map(error => ({
      ...error,
      canNavigate: this.pricingErrorNav.canNavigateToField(error.path),
      navigate: () => this.pricingErrorNav.navigateToFieldFromError(error.path)
    }));
  }
}
```

### Example 2: Message Center Integration

```typescript
// Add navigation to message center errors
@Component({
  template: `
    @for (error of errors(); track error) {
      <div appAutoPricingLinks>
        {{ error.message }}
      </div>
    }
  `
})
export class MessageCenterErrorsComponent {
  // Existing implementation with auto-enhancement
}
```

### Example 3: Custom Error Component

```typescript
@Component({
  selector: 'app-custom-pricing-error',
  template: `
    <div class="error-container">
      @for (error of pricingErrors(); track error.path) {
        <div class="error-item">
          @if (error.canNavigate) {
            <button 
              class="error-link"
              (click)="navigateToField(error.path)">
              {{ error.fieldLabel }}
            </button>
            <span>{{ error.message }}</span>
          } @else {
            <span>{{ error.fieldLabel }} {{ error.message }}</span>
          }
        </div>
      }
    </div>
  `
})
export class CustomPricingErrorComponent {
  private errorNavService = inject(PricingErrorNavigationService);

  pricingErrors = computed(() => {
    return this.errors().map(error => ({
      ...error,
      canNavigate: this.errorNavService.canNavigateToField(error.path),
      fieldLabel: this.errorNavService.getFieldLabel(error.path) || error.path
    }));
  });

  navigateToField(fieldPath: string) {
    this.errorNavService.navigateToFieldFromError(fieldPath, {
      focus: true,
      highlight: true
    });
  }
}
```

## Styling

Add CSS for enhanced error links:

```scss
// Global styles for pricing field links
.pricing-field-link {
  color: #007bff !important;
  font-weight: 500;
  
  &:hover {
    color: #0056b3 !important;
    background-color: rgba(0, 123, 255, 0.1);
  }
  
  &:focus {
    outline: 2px solid #007bff;
    outline-offset: 2px;
  }
}

.pricing-field-link-full {
  &:hover {
    background-color: rgba(0, 123, 255, 0.1);
  }
}
```

## Testing Integration

```typescript
// Test navigation functionality
describe('Pricing Error Integration', () => {
  let component: MyErrorComponent;
  let pricingNavService: PricingErrorNavigationService;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [MyErrorComponent],
      providers: [PricingErrorNavigationService]
    });
    
    component = TestBed.createComponent(MyErrorComponent).componentInstance;
    pricingNavService = TestBed.inject(PricingErrorNavigationService);
  });

  it('should navigate to pricing field on error click', () => {
    spyOn(pricingNavService, 'navigateToFieldFromError').and.returnValue(true);
    
    component.onErrorClick('purchasePrice');
    
    expect(pricingNavService.navigateToFieldFromError).toHaveBeenCalledWith(
      'purchasePrice',
      { focus: true, highlight: true }
    );
  });
});
```

## Best Practices

1. **Use Enhanced Error Display** for new components
2. **Add Auto Pricing Links** to existing error containers
3. **Test navigation** in different UI states (collapsed sections, modals)
4. **Provide fallbacks** for non-navigable fields
5. **Maintain accessibility** with proper ARIA labels and focus management
6. **Style consistently** with existing design system

## Troubleshooting

### Common Issues

1. **Links not working**: Ensure `PricingGlobalNavigationService.initialize()` is called
2. **Field not found**: Check that anchor IDs are present in the DOM
3. **Navigation fails**: Verify field path mappings in the service
4. **Styling issues**: Ensure CSS classes are properly applied

### Debug Tools

```typescript
// Check if field can be navigated to
console.log(this.errorNavService.canNavigateToField('purchasePrice'));

// Get field information
console.log(this.errorNavService.getFieldLabel('purchasePrice'));

// Test navigation
this.errorNavService.navigateToFieldFromError('purchasePrice');
```
