# Pricing Section Anchor Point System

## Overview

The Pricing Section Anchor Point System enables navigation from error message links to their corresponding input fields within the Pricing section of the application. This system provides a seamless user experience by allowing users to click on error messages and be automatically taken to the problematic field.

## Architecture

### Core Components

1. **PricingNavigationService** - Main service for field navigation
2. **PricingErrorNavigationService** - Helper service for error message integration
3. **PricingFieldLinkDirective** - Directive to enhance error messages with clickable links
4. **Anchor IDs** - Unique identifiers added to all pricing input fields

### Naming Convention

All anchor IDs follow the pattern: `pricing-{section}-{field-name}`

Examples:
- `pricing-purchase-info-purchase-price`
- `pricing-credits-lender-paid-credit`
- `pricing-qualifying-info-ltv`
- `pricing-escrow-insurance-amount`
- `pricing-closing-details-anticipated-closing-date`

## Implementation Details

### Supported Fields

#### Purchase Info Section
- Purchase Price: `pricing-purchase-info-purchase-price`
- Base Loan Amount: `pricing-purchase-info-base-loan-amount`
- Rate/Base Points: `pricing-purchase-info-rate-base-points`
- Buydown Options: `pricing-purchase-info-buydown-options`
- Buydown Sources: `pricing-purchase-info-buydown-sources`
- Buydown Opt-Out Reason: `pricing-purchase-info-buydown-opt-out-reason`
- Collected Discount Points: `pricing-purchase-info-collected-discount-points`
- Down Payment: `pricing-purchase-info-down-payment`

#### Credits Section
- Lender Paid Credit: `pricing-credits-lender-paid-credit`
- Realtor Credits: `pricing-credits-realtor-credits`
- Seller Concessions: `pricing-credits-seller-concessions`

#### Qualifying Info Section
- LTV: `pricing-qualifying-info-ltv`

#### Escrow Section
- Insurance Amount: `pricing-escrow-insurance-amount`
- Escrow Waiver: `pricing-escrow-waiver`

#### Closing Details Section
- Anticipated Closing Date: `pricing-closing-details-anticipated-closing-date`
- Commitment Period: `pricing-closing-details-commitment-period`

## Usage

### Basic Navigation

```typescript
import { PricingNavigationService } from './services/pricing-navigation/pricing-navigation.service';

// Inject the service
constructor(private pricingNav: PricingNavigationService) {}

// Navigate to a field
navigateToField() {
  this.pricingNav.navigateToField('purchasePrice', {
    focus: true,
    highlight: true,
    scrollBehavior: 'smooth'
  });
}
```

### Error Message Integration

#### Using the Directive

```html
<!-- Make specific text clickable -->
<span appPricingFieldLink="purchasePrice" linkText="Purchase Price">
  Purchase Price is required
</span>

<!-- Make entire message clickable -->
<li appPricingFieldLink="credits.manualLenderPaidCredit">
  Lender Paid Credit must be positive
</li>
```

#### Programmatic Error Navigation

```typescript
import { PricingErrorNavigationService } from './services/pricing-navigation/pricing-error-navigation.service';

// Navigate from error message
handleErrorClick(fieldPath: string) {
  this.errorNavService.navigateToFieldFromError(fieldPath, {
    focus: true,
    highlight: true
  });
}
```

### Advanced Features

#### Custom Field Mappings

```typescript
// Add custom field mapping
this.errorNavService.addFieldMapping('customField', 'purchasePrice');
```

#### Check Field Availability

```typescript
// Check if field can be navigated to
const canNavigate = this.errorNavService.canNavigateToField('purchasePrice');
```

#### Get Field Information

```typescript
// Get field label and anchor info
const fieldLabel = this.errorNavService.getFieldLabel('purchasePrice');
const anchorInfo = this.pricingNav.getAnchorInfo('purchasePrice');
```

## Edge Cases Handled

1. **Collapsed Sections** - Automatically expands collapsed sections before navigation
2. **Hidden Elements** - Handles elements in inactive tabs or hidden containers
3. **Missing Elements** - Gracefully handles cases where target elements don't exist
4. **Focus Management** - Finds the appropriate focusable element within containers
5. **Accessibility** - Maintains proper focus management and screen reader compatibility

## Testing

### Unit Tests

Run the test suite to verify functionality:

```bash
ng test --include="**/pricing-navigation.service.spec.ts"
```

### Demo Component

Use the demo component for manual testing:

```typescript
import { PricingNavigationDemoComponent } from './components/pricing-navigation-demo/pricing-navigation-demo.component';
```

## Integration with Existing Error Systems

### ValidationErrorProviderService Integration

The system integrates with the existing validation error system by mapping form control paths to navigation field paths:

```typescript
// Form control path -> Navigation field path
'credits.manualLenderPaidCredit' -> 'credits.manualLenderPaidCredit'
'qualificationDetails.ltv' -> 'qualificationDetails.ltv'
'purchasePrice' -> 'purchasePrice'
```

### Error Message Enhancement

Error messages can be enhanced in several ways:

1. **Directive-based** - Add `appPricingFieldLink` directive to error elements
2. **Programmatic** - Use `createClickableErrorMessage()` method
3. **Service-based** - Use `navigateToFieldFromError()` for custom implementations

## Browser Compatibility

- Modern browsers with ES2017+ support
- Smooth scrolling behavior (fallback to instant scroll on older browsers)
- Focus management compatible with all major browsers

## Performance Considerations

- Lazy loading of navigation mappings
- Efficient DOM queries using getElementById
- Minimal impact on page load time
- Event delegation for click handlers

## Future Enhancements

1. **Animation Support** - Add custom animations for field highlighting
2. **Keyboard Navigation** - Support keyboard shortcuts for field navigation
3. **History Integration** - Track navigation history for back/forward functionality
4. **Analytics Integration** - Track field navigation usage for UX insights
