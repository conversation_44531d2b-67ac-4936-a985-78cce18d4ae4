import { Overlay, OverlayContainer, OverlayModule } from '@angular/cdk/overlay';
import { ComponentType } from '@angular/cdk/portal';
import { Component, OnDestroy, computed, inject } from '@angular/core';
import { MatCardModule } from '@angular/material/card';
import { LeftPanelOverlayService } from './left-panel-overlay.service';

@Component({
  selector: 'app-left-panel',
  templateUrl: './left-panel.component.html',
  styleUrls: ['./left-panel.component.scss'],
  standalone: true,
  imports: [OverlayModule, MatCardModule],
  providers: [LeftPanelOverlayService, Overlay, OverlayContainer],
})
export class LeftPanelComponent<T> implements OnDestroy {
  private readonly overlay = inject(Overlay);
  private readonly leftPanelOverlayService = inject(LeftPanelOverlayService);

  readonly popoutContent = computed(() => this.leftPanelOverlayService.popoutContent());

  public togglePopout(
    component: ComponentType<T>,
    triggeringElement: EventTarget | null,
    leftPanelPlacementRef?: Element,
  ): void {
    this.leftPanelOverlayService.cancelHide();
    const positionStrategy = this.buildOverlayStraetgy(triggeringElement);
    this.leftPanelOverlayService.togglePopout(component, positionStrategy, leftPanelPlacementRef);
  }

  public toggleEditPopout(
    component: ComponentType<T>,
    triggeringElement: EventTarget | null,
    leftPanelPlacementRef?: Element,
  ): void {
    this.leftPanelOverlayService.cancelHide();
    const positionStrategy = this.buildOverlayStraetgy(triggeringElement);
    this.leftPanelOverlayService.toggleEditPopout(
      component,
      positionStrategy,
      leftPanelPlacementRef,
    );
  }

  public togglePopoutFromButton(
    component: ComponentType<T>,
    triggeringElement: EventTarget | null,
    leftPanelPlacementRef?: Element,
  ): void {
    this.leftPanelOverlayService.cancelHide();
    const positionStrategy = this.buildOverlayStraetgy(triggeringElement);
    this.leftPanelOverlayService.togglePopoutFromButton(
      component,
      positionStrategy,
      leftPanelPlacementRef,
    );
  }

  public togglePopoutWithFallbackPosition(
    component: ComponentType<T>,
    triggeringElement: EventTarget | null,
    leftPanelPlacementRef?: Element,
  ): void {
    const positionStrategy = this.overlay
      .position()
      .flexibleConnectedTo(<Element>triggeringElement)
      .withPositions([
        {
          originX: 'end',
          originY: 'top',
          overlayX: 'start',
          overlayY: 'top',
          offsetY: -8,
        },
        {
          originX: 'end',
          originY: 'bottom',
          overlayX: 'start',
          overlayY: 'bottom',
          offsetY: 8,
        },
      ])
      .withDefaultOffsetX(-4)
      .withDefaultOffsetY(0)
      .withGrowAfterOpen(false)
      .withLockedPosition(false);

    this.leftPanelOverlayService.togglePopout(component, positionStrategy, leftPanelPlacementRef);
  }

  private buildOverlayStraetgy(triggeringElement: EventTarget | null) {
    return this.overlay
      .position()
      .flexibleConnectedTo(<Element>triggeringElement)
      .withPositions([
        {
          originX: 'end',
          originY: 'top',
          overlayX: 'start',
          overlayY: 'top',
        },
        {
          originX: 'end',
          originY: 'top',
          overlayX: 'start',
          overlayY: 'top',
          offsetY: -4,
        },
      ])
      .withDefaultOffsetX(-4)
      .withDefaultOffsetY(0)
      .withGrowAfterOpen(false)
      .withLockedPosition(false);
  }

  ngOnDestroy() {
    this.leftPanelOverlayService.detachOverlay();
  }
}
