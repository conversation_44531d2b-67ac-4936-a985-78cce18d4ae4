:host {
  border-radius: 12px;
  box-shadow: 0px 0px 12px 0px rgba(black, 0.16);
  --left-panel-max-height: calc(100vh - 1rem - 50px - 8px);
}

:host-context(.rkt-DarkMode) {
  box-shadow: 0px 0px 12px 0px rgba(white, 0.16);
}

mat-card {
  height: auto;
}

.rkt-Card {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  padding: 0;
  padding-left: 10px;
  overflow-y: auto;
  max-height: var(--left-panel-max-height);
}

@keyframes slideInFromLeft {
  from {
    transform: translateX(-10%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideOutToLeft {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(-10%);
    opacity: 0;
  }
}

:host {
  animation: slideInFromLeft 0.1s ease-out forwards;
}

:host.hidden {
  animation: slideOutToLeft 0.1s ease-out forwards;
}
