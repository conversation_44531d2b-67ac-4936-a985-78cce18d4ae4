import { CdkPortalOutlet, ComponentPortal, ComponentType } from '@angular/cdk/portal';
import { Component, computed, inject } from '@angular/core';
import { MatCardModule } from '@angular/material/card';
import { LoanSearchComponent } from '../../../loan-search/loan-search.component';
import { SettingsComponent } from '../../../settings/settings.component';
import { LeftPanelEditStateListenerService } from '../edit-state-listener.service';
import { LeftPanelOverlayService } from '../left-panel-overlay.service';

@Component({
  selector: 'app-left-panel-popout',
  standalone: true,
  imports: [MatCardModule, CdkPortalOutlet],
  templateUrl: './left-panel-popout.component.html',
  styleUrl: './left-panel-popout.component.scss',
})
export class LeftPanelPopoutComponent<T> {
  portal!: ComponentPortal<T>;

  LoanSearchComponent = LoanSearchComponent;
  SettingsComponent = SettingsComponent;

  readonly leftPanelOverlayService = inject(LeftPanelOverlayService);
  readonly leftPanelEditStateListenerService = inject(LeftPanelEditStateListenerService);

  isEditState = computed(() => this.leftPanelEditStateListenerService.isEditState());
  popoutComponent = computed(
    () => this.leftPanelOverlayService.popoutContent().component || undefined,
  );

  attachComponent(component: ComponentType<T>): ComponentPortal<T> {
    this.portal = new ComponentPortal<T>(component);
    return this.portal;
  }

  handleHideComponent(component?: ComponentType<any>): void {
    if (
      this.isEditState() ||
      component === LoanSearchComponent ||
      component === SettingsComponent
    ) {
      return;
    }
    this.leftPanelOverlayService.hidePopout();
  }
}
