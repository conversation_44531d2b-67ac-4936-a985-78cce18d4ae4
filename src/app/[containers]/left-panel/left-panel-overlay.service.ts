import { Overlay, OverlayContainer, OverlayRef, PositionStrategy } from '@angular/cdk/overlay';
import { ComponentPortal, ComponentType } from '@angular/cdk/portal';
import {
  ComponentRef,
  Injectable,
  Injector,
  Renderer2,
  computed,
  effect,
  inject,
  signal,
  untracked,
} from '@angular/core';
import { LoanSearchComponent } from '../../loan-search/loan-search.component';
import { SettingsComponent } from '../../settings/settings.component';
import { LeftPanelEditStateListenerService } from './edit-state-listener.service';
import { LeftPanelPopoutComponent } from './left-panel-popout/left-panel-popout.component';

@Injectable()
export class LeftPanelOverlayService {
  overlay = inject(Overlay);
  readonly #injector = inject(Injector);
  private readonly overlayContainer = inject(OverlayContainer);
  private readonly renderer = inject(Renderer2);
  readonly leftPanelEditStateListenerService = inject(LeftPanelEditStateListenerService);

  static readonly OVERLAY_CONTAINER_CLASS = 'left-panel-overlay-container';

  LoanSearchComponent = LoanSearchComponent;
  SettingsComponent = SettingsComponent;

  private hideTimeout: any;

  isEditState = computed(() => this.leftPanelEditStateListenerService.isEditState());
  popoutContent = signal<{
    component: ComponentType<any> | null;
    positionStrategy: PositionStrategy | null;
    editOverlayPlacementRef?: Element | null;
    setEditState?: boolean | null;
  }>({
    component: null,
    positionStrategy: null,
    editOverlayPlacementRef: null,
    setEditState: null,
  });
  activeOverlays: OverlayRef[] = [];
  currentOverlayRef: OverlayRef | null = null;
  containerRef: ComponentRef<LeftPanelPopoutComponent<any>> | null = null;
  componentRef: ComponentPortal<any> | null = null;

  overlayEffect = effect(() => {
    const { component, positionStrategy } = this.popoutContent();

    if (component && positionStrategy) {
      // Close all active overlays
      this.closeAllOverlays();

      // Create and configure a new OverlayRef
      this.currentOverlayRef = this.overlay.create({
        hasBackdrop: true,
        backdropClass: 'cdk-overlay-transparent-backdrop',
        positionStrategy: positionStrategy,
      });

      // Listen for backdrop clicks to close the overlay
      this.currentOverlayRef.backdropClick().subscribe(() => this.hidePopout());

      // Track the overlay in the active overlays array
      this.activeOverlays.push(this.currentOverlayRef);

      // Attach the container and dynamic component
      this.containerRef = this.currentOverlayRef.attach(
        new ComponentPortal<LeftPanelPopoutComponent<any>>(
          LeftPanelPopoutComponent,
          null,
          this.#injector,
        ),
      );
      this.componentRef = this.containerRef?.instance.attachComponent(component) ?? null;

      // Add a class to the overlay container to lower the z-index
      this.setOverlayContainerClass();

      if (this.popoutContent().setEditState) {
        untracked(() => this.leftPanelEditStateListenerService.isEditState.set(true));
        this.updateEditPositioning();
      }
    } else {
      // Detach the overlay if no component is set
      this.detachOverlay();
    }
  });

  public hidePopout(): void {
    this.hideTimeout = setTimeout(() => {
      this.popoutContent.set({
        component: null,
        positionStrategy: null,
        editOverlayPlacementRef: null,
      });
    }, 100);
  }

  cancelHide() {
    clearTimeout(this.hideTimeout);
  }

  public showPopout(
    component: ComponentType<any>,
    positionStrategy: PositionStrategy,
    editOverlayPlacementRef?: Element,
    setEditState?: boolean,
  ): void {
    this.popoutContent.set({ component, positionStrategy, editOverlayPlacementRef, setEditState });
  }

  public togglePopoutFromButton(
    component: ComponentType<any>,
    positionStrategy: PositionStrategy,
    editOverlayPlacementRef?: Element,
  ): void {
    if (this.isEditState()) {
      return;
    } else if (this.popoutContent().component === component) {
      this.hidePopout();
    } else {
      this.showPopout(component, positionStrategy, editOverlayPlacementRef);
    }
  }

  public togglePopout(
    component: ComponentType<any>,
    positionStrategy: PositionStrategy,
    editOverlayPlacementRef?: Element,
  ): void {
    if (
      (this.popoutContent().component === component || this.isEditState()) &&
      component !== LoanSearchComponent &&
      component !== SettingsComponent
    ) {
      return;
    } else {
      this.showPopout(component, positionStrategy, editOverlayPlacementRef);
    }
  }

  public toggleEditPopout(
    component: ComponentType<any>,
    positionStrategy: PositionStrategy,
    editOverlayPlacementRef?: Element,
  ): void {
    this.showPopout(component, positionStrategy, editOverlayPlacementRef, true);
  }

  public detachOverlay() {
    this.currentOverlayRef?.detach();
    this.currentOverlayRef = null;
    this.removeOverlayContainerClass();
  }

  public updateEditPositioning() {
    const editPlacement = this.popoutContent().editOverlayPlacementRef;
    if (editPlacement) {
      const positionStrategy = this.overlay
        .position()
        .flexibleConnectedTo(editPlacement)
        .withPositions([
          {
            originX: 'end',
            originY: 'top',
            overlayX: 'start',
            overlayY: 'top',
          },
        ])
        .withDefaultOffsetX(-8)
        .withGrowAfterOpen(false)
        .withLockedPosition(true);

      this.currentOverlayRef?.updatePositionStrategy(positionStrategy);
    }
  }

  private setOverlayContainerClass() {
    const element = this.overlayContainer.getContainerElement();
    this.renderer.addClass(element, LeftPanelOverlayService.OVERLAY_CONTAINER_CLASS);
  }

  private removeOverlayContainerClass() {
    const element = this.overlayContainer.getContainerElement();
    this.renderer.removeClass(element, LeftPanelOverlayService.OVERLAY_CONTAINER_CLASS);
  }

  private closeAllOverlays(): void {
    this.activeOverlays.forEach((overlayRef) => overlayRef.detach());
    this.activeOverlays = [];
  }
}
