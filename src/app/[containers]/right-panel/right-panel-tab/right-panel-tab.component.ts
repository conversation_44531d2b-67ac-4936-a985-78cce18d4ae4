import { TemplatePortal } from '@angular/cdk/portal';
import {
  Component,
  computed,
  contentChild,
  inject,
  InjectionToken,
  input,
  TemplateRef,
  viewChild,
  ViewContainerRef,
} from '@angular/core';
import { SidenavScreen } from '../../../services/active-sidenav-screen/active-sidenav-screen.service';
import { RightPanelTabActionDirective } from './right-panel-tab-action.directive';
import { RightPanelTabIconDirective } from './right-panel-tab-icon.directive';

export const RIGHT_PANEL_TAB = new InjectionToken<RightPanelTabComponent>('RIGHT_PANEL_TAB');

@Component({
  selector: 'app-right-panel-tab',
  standalone: true,
  imports: [],
  templateUrl: './right-panel-tab.component.html',
  styleUrl: './right-panel-tab.component.scss',
  providers: [{ provide: RIGHT_PANEL_TAB, useExisting: RightPanelTabComponent }],
})
export class RightPanelTabComponent {
  private readonly viewContainerRef = inject(ViewContainerRef);

  readonly title = input.required<string>();
  readonly icon = input<string>('radio_button_unchecked-outlined');
  readonly screen = input.required<SidenavScreen>();
  readonly badge = input<number>(0);

  readonly content = computed(
    () => new TemplatePortal(this.contentTemplate(), this.viewContainerRef),
  );
  readonly contentTemplate = viewChild.required(TemplateRef);

  readonly iconTemplate = contentChild(RightPanelTabIconDirective);
  readonly actionTemplate = contentChild(RightPanelTabActionDirective);
}
