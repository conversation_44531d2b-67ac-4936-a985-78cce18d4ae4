<mat-card
  class="p-0 rkt-Card rkt-Card--full-width rkt-Card--tall h-full flex flex-col rkt-Elevation-3"
  [class.collapsed]="isCollapsed()"
>
  <div
    class="flex justify-between align-center button-container"
    [class.flex-col]="isCollapsed()"
    [class.button-container--is-collapsed]="isCollapsed()"
    [class.gap-4]="isCollapsed()"
  >
    @for (tab of tabs(); track $index) {
      <button
        [id]="$index"
        mat-icon-button
        [matBadge]="tab.badge()"
        [matBadgeHidden]="!tab.badge()"
        matBadgeSize="large"
        [matTooltip]="tab.title()"
        [matTooltipDisabled]="activeTab() === tab"
        class="rkt-ButtonIcon flex justify-self-center header-action rkt-Badge"
        [class.active]="activeTab() === tab"
        (click)="toggle(tab.screen())"
      >
        @if (tab.iconTemplate(); as iconTemplate) {
          <ng-template [cdkPortalOutlet]="iconTemplate" />
        } @else {
          <mat-icon class="rkt-Icon" [svgIcon]="tab.icon()" />
        }
      </button>
    }
  </div>
  @if (!isCollapsed()) {
    <button
      color="secondary"
      mat-mini-fab
      class="collapse-button"
      matTooltip="Collapse"
      (click)="collapse()"
    >
      <mat-icon svgIcon="navigate_next-outlined"></mat-icon>
    </button>
    <mat-divider class="rkt-HorizontalDivider" />
  }
  @if (!isCollapsed()) {
    <div class="flex justify-between items-center pr-4">
      @if (title(); as title) {
        <header class="rkt-Label-16 rkt-FontWeight--700">{{ title }}</header>
      }
      @if (actionTemplate(); as actionTemplate) {
        <ng-template [cdkPortalOutlet]="actionTemplate" />
      }
    </div>
    <div class="flex-1 overflow-y-auto">
      <ng-template [cdkPortalOutlet]="activeTab()?.content()" />
    </div>
  }
</mat-card>
