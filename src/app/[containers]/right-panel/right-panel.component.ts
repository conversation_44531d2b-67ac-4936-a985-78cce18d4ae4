import { CdkPortalOutlet } from '@angular/cdk/portal';
import { Component, computed, contentChildren, inject } from '@angular/core';
import { MatBadgeModule } from '@angular/material/badge';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatDividerModule } from '@angular/material/divider';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import {
  ActiveSidenavScreenService,
  SidenavScreen,
} from '../../services/active-sidenav-screen/active-sidenav-screen.service';
import { RightPanelTabComponent } from './right-panel-tab/right-panel-tab.component';

@Component({
  selector: 'app-right-panel',
  standalone: true,
  imports: [
    MatCardModule,
    CdkPortalOutlet,
    MatDividerModule,
    MatButtonModule,
    MatIconModule,
    MatTooltipModule,
    MatBadgeModule,
  ],
  templateUrl: './right-panel.component.html',
  styleUrl: './right-panel.component.scss',
})
export class RightPanelComponent {
  protected readonly activeSidenavScreenService = inject(ActiveSidenavScreenService);

  tabs = contentChildren(RightPanelTabComponent);
  protected readonly title = computed(() => this.activeTab()?.title() ?? '');
  protected readonly actionTemplate = computed(() => this.activeTab()?.actionTemplate());
  protected readonly activeTab = computed(() =>
    this.tabs().find((tab) => tab.screen() === this.activeSidenavScreenService.activeScreen()),
  );
  protected readonly isCollapsed = computed(() => this.activeTab() == null);

  toggle(screen: SidenavScreen) {
    this.activeSidenavScreenService.toggle(screen);
  }
  collapse() {
    this.activeSidenavScreenService.clear();
  }
}
