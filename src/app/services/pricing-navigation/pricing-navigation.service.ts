import { Injectable } from '@angular/core';
import { environment } from '../../../environments/environment';

export interface PricingFieldAnchor {
  id: string;
  section: string;
  fieldName: string;
  label: string;
}

@Injectable({
  providedIn: 'root'
})
export class PricingNavigationService {

  private readonly fieldAnchors: Map<string, PricingFieldAnchor> = new Map([
    // Purchase Info fields
    ['purchasePrice', {
      id: 'pricing-purchase-info-purchase-price',
      section: 'purchase-info',
      fieldName: 'purchasePrice',
      label: 'Purchase Price'
    }],
    ['baseLoanAmount', {
      id: 'pricing-purchase-info-base-loan-amount',
      section: 'purchase-info',
      fieldName: 'baseLoanAmount',
      label: 'Base Loan Amount'
    }],
    ['baseRate', {
      id: 'pricing-purchase-info-rate-base-points',
      section: 'purchase-info',
      fieldName: 'baseRate',
      label: 'Rate/Base Points'
    }],
    ['buydown', {
      id: 'pricing-purchase-info-buydown-options',
      section: 'purchase-info',
      fieldName: 'buydown',
      label: 'Buydown Options'
    }],
    ['buydownSources', {
      id: 'pricing-purchase-info-buydown-sources',
      section: 'purchase-info',
      fieldName: 'buydownSources',
      label: 'Buydown Sources'
    }],
    ['lenderPaidBuydownOptOutReason', {
      id: 'pricing-purchase-info-buydown-opt-out-reason',
      section: 'purchase-info',
      fieldName: 'lenderPaidBuydownOptOutReason',
      label: 'Buydown Opt-Out Reason'
    }],
    ['collectedDiscountPoints', {
      id: 'pricing-purchase-info-collected-discount-points',
      section: 'purchase-info',
      fieldName: 'collectedDiscountPoints',
      label: 'Collected Discount Points'
    }],
    ['downPayment', {
      id: 'pricing-purchase-info-down-payment',
      section: 'purchase-info',
      fieldName: 'downPayment',
      label: 'Down Payment'
    }],

    // Credits fields
    ['credits.manualLenderPaidCredit', {
      id: 'pricing-credits-lender-paid-credit',
      section: 'credits',
      fieldName: 'manualLenderPaidCredit',
      label: 'Lender Paid Credit'
    }],
    ['credits.realtorCredits', {
      id: 'pricing-credits-realtor-credits',
      section: 'credits',
      fieldName: 'realtorCredits',
      label: 'Realtor Credits'
    }],
    ['credits.sellerConcessions', {
      id: 'pricing-credits-seller-concessions',
      section: 'credits',
      fieldName: 'sellerConcessions',
      label: 'Seller Concessions'
    }],

    // Qualifying Info fields
    ['qualificationDetails.ltv', {
      id: 'pricing-qualifying-info-ltv',
      section: 'qualifying-info',
      fieldName: 'ltv',
      label: 'LTV'
    }],

    // Escrow fields
    ['insuranceAmount', {
      id: 'pricing-escrow-insurance-amount',
      section: 'escrow',
      fieldName: 'insuranceAmount',
      label: 'Insurance Amount'
    }],
    ['escrowWaiver', {
      id: 'pricing-escrow-waiver',
      section: 'escrow',
      fieldName: 'escrowWaiver',
      label: 'Escrow Waiver'
    }],

    // Closing Details fields
    ['anticipatedClosingDate', {
      id: 'pricing-closing-details-anticipated-closing-date',
      section: 'closing-details',
      fieldName: 'anticipatedClosingDate',
      label: 'Anticipated Closing Date'
    }],
    ['commitmentPeriodInDays', {
      id: 'pricing-closing-details-commitment-period',
      section: 'closing-details',
      fieldName: 'commitmentPeriodInDays',
      label: 'Commitment Period'
    }]
  ]);

  /**
   * Navigate to a specific pricing field by its form path
   * @param fieldPath - The form control path (e.g., 'credits.manualLenderPaidCredit')
   * @param options - Navigation options
   */
  navigateToField(fieldPath: string, options: {
    focus?: boolean;
    highlight?: boolean;
    scrollBehavior?: ScrollBehavior;
  } = {}): boolean {
    const anchor = this.fieldAnchors.get(fieldPath);
    if (!anchor) {
      console.warn(`No anchor found for field path: ${fieldPath}`);
      return false;
    }

    return this.navigateToAnchor(anchor.id, options);
  }

  /**
   * Navigate to a specific pricing field by its anchor ID
   * @param anchorId - The anchor ID (e.g., 'pricing-credits-lender-paid-credit')
   * @param options - Navigation options
   */
  navigateToAnchor(anchorId: string, options: {
    focus?: boolean;
    highlight?: boolean;
    scrollBehavior?: ScrollBehavior;
  } = {}): boolean {
    const element = document.getElementById(anchorId);
    if (!element) {
      console.warn(`Element with ID '${anchorId}' not found`);
      return false;
    }

    // Handle elements in collapsed sections or inactive tabs
    this.ensureElementVisible(element);

    // Scroll to element
    const scrollBehavior = options.scrollBehavior || environment.scrollBehavior || 'smooth';
    element.scrollIntoView({
      behavior: scrollBehavior,
      block: 'center',
      inline: 'nearest'
    });

    // Focus the element if requested
    if (options.focus !== false) {
      setTimeout(() => {
        const focusableElement = this.findFocusableElement(element);
        if (focusableElement) {
          focusableElement.focus();
        }
      }, 100);
    }

    // Highlight the element if requested
    if (options.highlight) {
      this.highlightElement(element);
    }

    return true;
  }

  /**
   * Get anchor information for a field path
   */
  getAnchorInfo(fieldPath: string): PricingFieldAnchor | undefined {
    return this.fieldAnchors.get(fieldPath);
  }

  /**
   * Get all available field anchors
   */
  getAllAnchors(): PricingFieldAnchor[] {
    return Array.from(this.fieldAnchors.values());
  }

  /**
   * Ensure an element is visible by expanding collapsed sections or activating tabs
   */
  private ensureElementVisible(element: HTMLElement): void {
    // Check if element is in a collapsed section
    let parent = element.parentElement;
    while (parent) {
      // Handle mat-expansion-panel
      if (parent.classList.contains('mat-expansion-panel') && !parent.classList.contains('mat-expanded')) {
        const header = parent.querySelector('.mat-expansion-panel-header') as HTMLElement;
        if (header) {
          header.click();
        }
      }

      // Handle other collapsible sections
      if (parent.hasAttribute('aria-expanded') && parent.getAttribute('aria-expanded') === 'false') {
        (parent as HTMLElement).click();
      }

      parent = parent.parentElement;
    }
  }

  /**
   * Find the focusable element within a container
   */
  private findFocusableElement(container: HTMLElement): HTMLElement | null {
    const focusableSelectors = [
      'input:not([disabled])',
      'select:not([disabled])',
      'textarea:not([disabled])',
      'button:not([disabled])',
      '[tabindex]:not([tabindex="-1"])'
    ];

    // First try to find a focusable element within the container
    for (const selector of focusableSelectors) {
      const element = container.querySelector(selector) as HTMLElement;
      if (element) {
        return element;
      }
    }

    // If the container itself is focusable, return it
    if (focusableSelectors.some(selector => container.matches(selector))) {
      return container;
    }

    return null;
  }

  /**
   * Highlight an element temporarily
   */
  private highlightElement(element: HTMLElement): void {
    const originalStyle = element.style.cssText;

    // Add highlight styles
    element.style.cssText += `
      outline: 2px solid #007bff !important;
      outline-offset: 2px !important;
      transition: outline 0.3s ease !important;
    `;

    // Remove highlight after 3 seconds
    setTimeout(() => {
      element.style.cssText = originalStyle;
    }, 3000);
  }
}
