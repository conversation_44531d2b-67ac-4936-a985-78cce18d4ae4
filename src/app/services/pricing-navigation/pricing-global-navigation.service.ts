import { Injectable, inject } from '@angular/core';
import { PricingErrorNavigationService } from './pricing-error-navigation.service';

/**
 * Service to set up global navigation functions for pricing fields
 * This enables navigation from dynamically generated HTML content
 */
@Injectable({
  providedIn: 'root'
})
export class PricingGlobalNavigationService {
  private readonly errorNavService = inject(PricingErrorNavigationService);
  private isInitialized = false;

  /**
   * Initialize global navigation functions
   * Should be called once during app initialization
   */
  initialize(): void {
    if (this.isInitialized) {
      return;
    }

    // Set up global navigation function
    (window as any).navigateToPricingField = async (fieldPath: string) => {
      try {
        await this.errorNavService.navigateToFieldFromError(fieldPath, {
          focus: true,
          highlight: true,
          scrollBehavior: 'smooth'
        });
      } catch (error) {
        console.error('Failed to navigate to pricing field:', error);
      }
    };

    this.isInitialized = true;
  }

  /**
   * Clean up global functions (for testing or cleanup)
   */
  cleanup(): void {
    if ((window as any).navigateToPricingField) {
      delete (window as any).navigateToPricingField;
    }
    this.isInitialized = false;
  }
}
