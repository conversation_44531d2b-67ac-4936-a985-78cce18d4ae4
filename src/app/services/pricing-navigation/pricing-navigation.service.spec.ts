import { TestBed } from '@angular/core/testing';
import { PricingNavigationService } from './pricing-navigation.service';

describe('PricingNavigationService', () => {
  let service: PricingNavigationService;

  beforeEach(() => {
    TestBed.configureTestingModule({});
    service = TestBed.inject(PricingNavigationService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('getAnchorInfo', () => {
    it('should return anchor info for valid field paths', () => {
      const purchasePriceAnchor = service.getAnchorInfo('purchasePrice');
      expect(purchasePriceAnchor).toBeDefined();
      expect(purchasePriceAnchor?.id).toBe('pricing-purchase-info-purchase-price');
      expect(purchasePriceAnchor?.label).toBe('Purchase Price');
    });

    it('should return undefined for invalid field paths', () => {
      const invalidAnchor = service.getAnchorInfo('invalidField');
      expect(invalidAnchor).toBeUndefined();
    });
  });

  describe('getAllAnchors', () => {
    it('should return all available anchors', () => {
      const anchors = service.getAllAnchors();
      expect(anchors.length).toBeGreaterThan(0);
      
      // Check that we have anchors for each section
      const sections = anchors.map(a => a.section);
      expect(sections).toContain('purchase-info');
      expect(sections).toContain('credits');
      expect(sections).toContain('qualifying-info');
      expect(sections).toContain('escrow');
      expect(sections).toContain('closing-details');
    });
  });

  describe('navigateToField', () => {
    beforeEach(() => {
      // Mock DOM elements for testing
      const mockElement = document.createElement('div');
      mockElement.id = 'pricing-purchase-info-purchase-price';
      mockElement.scrollIntoView = jasmine.createSpy('scrollIntoView');
      document.body.appendChild(mockElement);
    });

    afterEach(() => {
      // Clean up mock elements
      const mockElement = document.getElementById('pricing-purchase-info-purchase-price');
      if (mockElement) {
        document.body.removeChild(mockElement);
      }
    });

    it('should return true for valid field paths with existing elements', () => {
      const result = service.navigateToField('purchasePrice');
      expect(result).toBe(true);
    });

    it('should return false for valid field paths with missing elements', () => {
      const result = service.navigateToField('buydown'); // Element doesn't exist in test
      expect(result).toBe(false);
    });

    it('should return false for invalid field paths', () => {
      const result = service.navigateToField('invalidField');
      expect(result).toBe(false);
    });
  });

  describe('navigateToAnchor', () => {
    beforeEach(() => {
      const mockElement = document.createElement('input');
      mockElement.id = 'test-anchor';
      mockElement.focus = jasmine.createSpy('focus');
      mockElement.scrollIntoView = jasmine.createSpy('scrollIntoView');
      document.body.appendChild(mockElement);
    });

    afterEach(() => {
      const mockElement = document.getElementById('test-anchor');
      if (mockElement) {
        document.body.removeChild(mockElement);
      }
    });

    it('should scroll to and focus element when found', () => {
      const result = service.navigateToAnchor('test-anchor', { focus: true });
      expect(result).toBe(true);
      
      const element = document.getElementById('test-anchor') as HTMLInputElement;
      expect(element.scrollIntoView).toHaveBeenCalled();
      
      // Focus is called asynchronously, so we need to wait
      setTimeout(() => {
        expect(element.focus).toHaveBeenCalled();
      }, 150);
    });

    it('should return false when element not found', () => {
      const result = service.navigateToAnchor('non-existent-anchor');
      expect(result).toBe(false);
    });
  });

  describe('field mappings', () => {
    it('should have correct anchor IDs for all field types', () => {
      const expectedMappings = [
        { field: 'purchasePrice', id: 'pricing-purchase-info-purchase-price' },
        { field: 'credits.manualLenderPaidCredit', id: 'pricing-credits-lender-paid-credit' },
        { field: 'qualificationDetails.ltv', id: 'pricing-qualifying-info-ltv' },
        { field: 'insuranceAmount', id: 'pricing-escrow-insurance-amount' },
        { field: 'anticipatedClosingDate', id: 'pricing-closing-details-anticipated-closing-date' }
      ];

      expectedMappings.forEach(mapping => {
        const anchor = service.getAnchorInfo(mapping.field);
        expect(anchor?.id).toBe(mapping.id);
      });
    });
  });
});
