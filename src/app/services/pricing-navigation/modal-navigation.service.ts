import { Injectable } from '@angular/core';

/**
 * Service to handle navigation to fields that are in modals or popovers
 */
@Injectable({
  providedIn: 'root'
})
export class ModalNavigationService {

  /**
   * Field mappings for modal/popover fields
   */
  private readonly modalFieldMappings = new Map<string, {
    modalType: 'client-popover' | 'other-modal';
    tabIndex?: number;
    fieldId: string;
    helpMessage: string;
  }>([
    ['firstName', {
      modalType: 'client-popover',
      tabIndex: 0, // Client Info tab
      fieldId: 'first-name',
      helpMessage: 'Please open the Client Information panel to access the First Name field.'
    }],
    ['lastName', {
      modalType: 'client-popover',
      tabIndex: 0,
      fieldId: 'last-name',
      helpMessage: 'Please open the Client Information panel to access the Last Name field.'
    }],
    ['middleName', {
      modalType: 'client-popover',
      tabIndex: 0,
      fieldId: 'middle-name',
      helpMessage: 'Please open the Client Information panel to access the Middle Name field.'
    }],
    ['personalInformation.firstName', {
      modalType: 'client-popover',
      tabIndex: 0,
      fieldId: 'first-name',
      helpMessage: 'Please open the Client Information panel to access the First Name field.'
    }],
    ['personalInformation.lastName', {
      modalType: 'client-popover',
      tabIndex: 0,
      fieldId: 'last-name',
      helpMessage: 'Please open the Client Information panel to access the Last Name field.'
    }],
    ['personalInformation.middleName', {
      modalType: 'client-popover',
      tabIndex: 0,
      fieldId: 'middle-name',
      helpMessage: 'Please open the Client Information panel to access the Middle Name field.'
    }]
  ]);

  /**
   * Check if a field is in a modal/popover
   */
  isModalField(fieldPath: string): boolean {
    const isModal = this.modalFieldMappings.has(fieldPath);
    console.log(`ModalNavigationService.isModalField(${fieldPath}):`, isModal);
    return isModal;
  }

  /**
   * Navigate to a field that's in a modal/popover
   */
  async navigateToModalField(fieldPath: string, options: {
    focus?: boolean;
    highlight?: boolean;
    scrollBehavior?: ScrollBehavior;
  } = {}): Promise<boolean> {
    const mapping = this.modalFieldMappings.get(fieldPath);
    if (!mapping) {
      return false;
    }

    // Try to find the field in the current DOM (in case modal is already open)
    const fieldFound = this.navigateToFieldInModal(mapping.fieldId, options);
    if (fieldFound) {
      return true;
    }

    // Field not found - show helpful message
    console.warn(mapping.helpMessage);

    // Try to show a user-visible notification if possible
    this.showUserNotification(mapping.helpMessage);

    return false;
  }

  /**
   * Show a user notification (simple console warning for now)
   */
  private showUserNotification(message: string): void {
    // For now, just log to console
    // In a real implementation, you might want to show a toast notification
    console.info('Navigation Help:', message);
  }

  /**
   * Navigate to a field within the modal
   */
  private navigateToFieldInModal(fieldId: string, options: {
    focus?: boolean;
    highlight?: boolean;
    scrollBehavior?: ScrollBehavior;
  }): boolean {
    const element = document.getElementById(fieldId);
    if (!element) {
      console.warn(`Modal field element with ID '${fieldId}' not found`);
      return false;
    }

    // Scroll to element
    const scrollBehavior = options.scrollBehavior || 'smooth';
    element.scrollIntoView({
      behavior: scrollBehavior,
      block: 'center',
      inline: 'nearest'
    });

    // Focus the element if requested
    if (options.focus !== false) {
      setTimeout(() => {
        const focusableElement = this.findFocusableElement(element);
        if (focusableElement) {
          focusableElement.focus();
        }
      }, 100);
    }

    // Highlight the element if requested
    if (options.highlight) {
      this.highlightElement(element);
    }

    return true;
  }

  /**
   * Find the focusable element within a container
   */
  private findFocusableElement(container: HTMLElement): HTMLElement | null {
    const focusableSelectors = [
      'input:not([disabled])',
      'select:not([disabled])',
      'textarea:not([disabled])',
      'button:not([disabled])',
      '[tabindex]:not([tabindex="-1"])'
    ];

    // First try to find a focusable element within the container
    for (const selector of focusableSelectors) {
      const element = container.querySelector(selector) as HTMLElement;
      if (element) {
        return element;
      }
    }

    // If the container itself is focusable, return it
    if (focusableSelectors.some(selector => container.matches(selector))) {
      return container;
    }

    return null;
  }

  /**
   * Highlight an element temporarily
   */
  private highlightElement(element: HTMLElement): void {
    const originalStyle = element.style.cssText;

    // Add highlight styles
    element.style.cssText += `
      outline: 2px solid #007bff !important;
      outline-offset: 2px !important;
      transition: outline 0.3s ease !important;
    `;

    // Remove highlight after 3 seconds
    setTimeout(() => {
      element.style.cssText = originalStyle;
    }, 3000);
  }

  /**
   * Get all modal field mappings
   */
  getAllModalFields(): string[] {
    return Array.from(this.modalFieldMappings.keys());
  }
}
