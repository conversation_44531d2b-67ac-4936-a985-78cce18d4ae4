import { Injectable, inject } from '@angular/core';
import { LeftPanelOverlayService } from '../../[containers]/left-panel/left-panel-overlay.service';
import { Overlay } from '@angular/cdk/overlay';
import { EditClientBaseComponent } from '../../left-nav-content/client/edit-client-base/edit-client-base.component';

/**
 * Service to handle navigation to fields that are in modals or popovers
 */
@Injectable({
  providedIn: 'root'
})
export class ModalNavigationService {
  private readonly leftPanelOverlayService = inject(LeftPanelOverlayService);
  private readonly overlay = inject(Overlay);

  /**
   * Field mappings for modal/popover fields
   */
  private readonly modalFieldMappings = new Map<string, {
    modalType: 'client-popover' | 'other-modal';
    tabIndex?: number;
    fieldId: string;
    openModal: () => Promise<void>;
  }>([
    ['firstName', {
      modalType: 'client-popover',
      tabIndex: 0, // Client Info tab
      fieldId: 'first-name',
      openModal: () => this.openClientPopover()
    }],
    ['lastName', {
      modalType: 'client-popover',
      tabIndex: 0,
      fieldId: 'last-name',
      openModal: () => this.openClientPopover()
    }],
    ['middleName', {
      modalType: 'client-popover',
      tabIndex: 0,
      fieldId: 'middle-name',
      openModal: () => this.openClientPopover()
    }],
    ['personalInformation.firstName', {
      modalType: 'client-popover',
      tabIndex: 0,
      fieldId: 'first-name',
      openModal: () => this.openClientPopover()
    }],
    ['personalInformation.lastName', {
      modalType: 'client-popover',
      tabIndex: 0,
      fieldId: 'last-name',
      openModal: () => this.openClientPopover()
    }],
    ['personalInformation.middleName', {
      modalType: 'client-popover',
      tabIndex: 0,
      fieldId: 'middle-name',
      openModal: () => this.openClientPopover()
    }]
  ]);

  /**
   * Check if a field is in a modal/popover
   */
  isModalField(fieldPath: string): boolean {
    return this.modalFieldMappings.has(fieldPath);
  }

  /**
   * Navigate to a field that's in a modal/popover
   */
  async navigateToModalField(fieldPath: string, options: {
    focus?: boolean;
    highlight?: boolean;
    scrollBehavior?: ScrollBehavior;
  } = {}): Promise<boolean> {
    const mapping = this.modalFieldMappings.get(fieldPath);
    if (!mapping) {
      return false;
    }

    try {
      // Open the modal/popover
      await mapping.openModal();

      // Wait for the modal to be rendered
      await this.waitForModalToRender();

      // Switch to the correct tab if needed
      if (mapping.tabIndex !== undefined) {
        await this.switchToTab(mapping.tabIndex);
      }

      // Navigate to the specific field
      return this.navigateToFieldInModal(mapping.fieldId, options);
    } catch (error) {
      console.error('Failed to navigate to modal field:', error);
      return false;
    }
  }

  /**
   * Open the client popover
   */
  private async openClientPopover(): Promise<void> {
    // Find a suitable trigger element (could be the client tile or any element)
    const triggerElement = document.querySelector('[data-synthetic-monitor-id*="client"]') || 
                          document.querySelector('.rkt-Button') || 
                          document.body;

    if (!triggerElement) {
      throw new Error('Could not find trigger element for client popover');
    }

    // Create position strategy
    const positionStrategy = this.overlay
      .position()
      .flexibleConnectedTo(triggerElement)
      .withPositions([
        {
          originX: 'end',
          originY: 'top',
          overlayX: 'start',
          overlayY: 'top',
        },
      ])
      .withDefaultOffsetX(-8)
      .withGrowAfterOpen(false)
      .withLockedPosition(true);

    // Show the client popover
    this.leftPanelOverlayService.showPopout(
      EditClientBaseComponent,
      positionStrategy,
      triggerElement as Element,
      true // setEditState
    );
  }

  /**
   * Wait for modal to be rendered in the DOM
   */
  private async waitForModalToRender(): Promise<void> {
    return new Promise((resolve) => {
      const checkForModal = () => {
        const modal = document.querySelector('app-edit-client-base') || 
                     document.querySelector('.left-panel-overlay-container');
        if (modal) {
          // Wait a bit more for content to render
          setTimeout(resolve, 200);
        } else {
          setTimeout(checkForModal, 50);
        }
      };
      checkForModal();
    });
  }

  /**
   * Switch to a specific tab in the modal
   */
  private async switchToTab(tabIndex: number): Promise<void> {
    return new Promise((resolve) => {
      const switchTab = () => {
        const tabGroup = document.querySelector('mat-tab-group');
        const tabHeaders = document.querySelectorAll('.mat-tab-label');
        
        if (tabHeaders.length > tabIndex) {
          const targetTab = tabHeaders[tabIndex] as HTMLElement;
          targetTab.click();
          setTimeout(resolve, 100); // Wait for tab switch animation
        } else {
          setTimeout(switchTab, 50);
        }
      };
      switchTab();
    });
  }

  /**
   * Navigate to a field within the modal
   */
  private navigateToFieldInModal(fieldId: string, options: {
    focus?: boolean;
    highlight?: boolean;
    scrollBehavior?: ScrollBehavior;
  }): boolean {
    const element = document.getElementById(fieldId);
    if (!element) {
      console.warn(`Modal field element with ID '${fieldId}' not found`);
      return false;
    }

    // Scroll to element
    const scrollBehavior = options.scrollBehavior || 'smooth';
    element.scrollIntoView({ 
      behavior: scrollBehavior, 
      block: 'center',
      inline: 'nearest'
    });

    // Focus the element if requested
    if (options.focus !== false) {
      setTimeout(() => {
        const focusableElement = this.findFocusableElement(element);
        if (focusableElement) {
          focusableElement.focus();
        }
      }, 100);
    }

    // Highlight the element if requested
    if (options.highlight) {
      this.highlightElement(element);
    }

    return true;
  }

  /**
   * Find the focusable element within a container
   */
  private findFocusableElement(container: HTMLElement): HTMLElement | null {
    const focusableSelectors = [
      'input:not([disabled])',
      'select:not([disabled])',
      'textarea:not([disabled])',
      'button:not([disabled])',
      '[tabindex]:not([tabindex="-1"])'
    ];
    
    // First try to find a focusable element within the container
    for (const selector of focusableSelectors) {
      const element = container.querySelector(selector) as HTMLElement;
      if (element) {
        return element;
      }
    }
    
    // If the container itself is focusable, return it
    if (focusableSelectors.some(selector => container.matches(selector))) {
      return container;
    }
    
    return null;
  }

  /**
   * Highlight an element temporarily
   */
  private highlightElement(element: HTMLElement): void {
    const originalStyle = element.style.cssText;
    
    // Add highlight styles
    element.style.cssText += `
      outline: 2px solid #007bff !important;
      outline-offset: 2px !important;
      transition: outline 0.3s ease !important;
    `;
    
    // Remove highlight after 3 seconds
    setTimeout(() => {
      element.style.cssText = originalStyle;
    }, 3000);
  }

  /**
   * Get all modal field mappings
   */
  getAllModalFields(): string[] {
    return Array.from(this.modalFieldMappings.keys());
  }
}
