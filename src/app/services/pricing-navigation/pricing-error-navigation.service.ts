import { Injectable, inject } from '@angular/core';
import { PricingNavigationService } from './pricing-navigation.service';

/**
 * Service to help integrate pricing field navigation with error messages
 */
@Injectable({
  providedIn: 'root'
})
export class PricingErrorNavigationService {
  private readonly pricingNavService = inject(PricingNavigationService);

  /**
   * Field path mappings for common form control paths to navigation field paths
   */
  private readonly fieldPathMappings: Map<string, string> = new Map([
    // Purchase Info mappings
    ['purchasePrice', 'purchasePrice'],
    ['baseLoanAmount', 'baseLoanAmount'],
    ['baseRate', 'baseRate'],
    ['buydown', 'buydown'],
    ['buydownSources', 'buydownSources'],
    ['lenderPaidBuydownOptOutReason', 'lenderPaidBuydownOptOutReason'],
    ['collectedDiscountPoints', 'collectedDiscountPoints'],
    ['downPayment', 'downPayment'],

    // Credits mappings
    ['credits.manualLenderPaidCredit', 'credits.manualLenderPaidCredit'],
    ['credits.realtorCredits', 'credits.realtorCredits'],
    ['credits.sellerConcessions', 'credits.sellerConcessions'],

    // Qualifying Info mappings
    ['qualificationDetails.ltv', 'qualificationDetails.ltv'],
    ['ltv', 'qualificationDetails.ltv'],

    // Escrow mappings
    ['insuranceAmount', 'insuranceAmount'],
    ['escrowWaiver', 'escrowWaiver'],

    // Closing Details mappings
    ['anticipatedClosingDate', 'anticipatedClosingDate'],
    ['commitmentPeriodInDays', 'commitmentPeriodInDays'],
    ['closingDetails.anticipatedClosingDate', 'anticipatedClosingDate'],
    ['closingDetails.commitmentPeriodInDays', 'commitmentPeriodInDays'],

    // Client field mappings
    ['firstName', 'firstName'],
    ['lastName', 'lastName'],
    ['middleName', 'middleName'],
    ['personalInformation.firstName', 'personalInformation.firstName'],
    ['personalInformation.lastName', 'personalInformation.lastName'],
    ['personalInformation.middleName', 'personalInformation.middleName'],
  ]);

  /**
   * Navigate to a field based on a form control path or field name
   * @param fieldPath - The form control path or field name
   * @param options - Navigation options
   */
  async navigateToFieldFromError(fieldPath: string, options?: {
    focus?: boolean;
    highlight?: boolean;
    scrollBehavior?: ScrollBehavior;
  }): Promise<boolean> {
    // Try direct mapping first
    const mappedPath = this.fieldPathMappings.get(fieldPath);
    if (mappedPath) {
      return await this.pricingNavService.navigateToField(mappedPath, options);
    }

    // Try the path as-is
    return await this.pricingNavService.navigateToField(fieldPath, options);
  }

  /**
   * Check if a field path can be navigated to
   * @param fieldPath - The form control path or field name
   */
  canNavigateToField(fieldPath: string): boolean {
    const mappedPath = this.fieldPathMappings.get(fieldPath);
    if (mappedPath) {
      return !!this.pricingNavService.getAnchorInfo(mappedPath);
    }
    return !!this.pricingNavService.getAnchorInfo(fieldPath);
  }

  /**
   * Get the display label for a field
   * @param fieldPath - The form control path or field name
   */
  getFieldLabel(fieldPath: string): string | undefined {
    const mappedPath = this.fieldPathMappings.get(fieldPath);
    const anchor = this.pricingNavService.getAnchorInfo(mappedPath || fieldPath);
    return anchor?.label;
  }

  /**
   * Create a clickable error message element
   * @param errorText - The error message text
   * @param fieldPath - The field path to navigate to
   * @param linkText - Optional specific text to make clickable (defaults to field label)
   */
  createClickableErrorMessage(
    errorText: string,
    fieldPath: string,
    linkText?: string
  ): HTMLElement {
    const container = document.createElement('span');
    container.innerHTML = errorText;

    if (!this.canNavigateToField(fieldPath)) {
      return container;
    }

    const fieldLabel = this.getFieldLabel(fieldPath);
    const textToLink = linkText || fieldLabel;

    if (textToLink && errorText.includes(textToLink)) {
      // Replace the field name with a clickable link
      const linkElement = document.createElement('button');
      linkElement.textContent = textToLink;
      linkElement.className = 'pricing-field-link rkt-Link--inline-12';
      linkElement.style.cssText = `
        background: none;
        border: none;
        padding: 0;
        color: inherit;
        text-decoration: underline;
        cursor: pointer;
        font: inherit;
      `;

      linkElement.addEventListener('click', (event) => {
        event.preventDefault();
        event.stopPropagation();
        this.navigateToFieldFromError(fieldPath, {
          focus: true,
          highlight: true,
          scrollBehavior: 'smooth'
        });
      });

      container.innerHTML = errorText.replace(textToLink, linkElement.outerHTML);
    } else {
      // Make the entire message clickable
      container.style.cursor = 'pointer';
      container.style.textDecoration = 'underline';
      container.title = `Click to navigate to ${fieldLabel || fieldPath}`;

      container.addEventListener('click', (event) => {
        event.preventDefault();
        event.stopPropagation();
        this.navigateToFieldFromError(fieldPath, {
          focus: true,
          highlight: true,
          scrollBehavior: 'smooth'
        });
      });
    }

    return container;
  }

  /**
   * Add field path mapping for custom form controls
   * @param formPath - The form control path
   * @param navigationPath - The navigation field path
   */
  addFieldMapping(formPath: string, navigationPath: string): void {
    this.fieldPathMappings.set(formPath, navigationPath);
  }

  /**
   * Get all available field mappings
   */
  getAllFieldMappings(): Map<string, string> {
    return new Map(this.fieldPathMappings);
  }
}
