@mixin support-container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  flex: 1 0 0;
  align-self: stretch;
  overflow-y: auto;
}

@mixin support-accordion {
  border-radius: 5px;
  border: var(--rlxp-accordion-left-nav-border, 1px solid #b1b1b0);
}

@mixin support-form-grid {
  display: grid;
  grid-template-columns: repeat(9, 1fr);
  gap: 16px;
}

@mixin empty-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 16px;
  align-self: stretch;
}

@mixin support-button {
  display: flex;
  height: 32px;
  padding: 6px 16px;
  justify-content: center;
  align-items: center;
  gap: 8px;
}

@mixin add-button {
  @include support-button;
  border-radius: 8px;
  border: var(--rlxp-add-button-left-nav-border);
  background: var(--rlxp-add-button-left-nav-button);
}

@mixin bottom-row-flex {
  display: flex;
  gap: 25px;
}
