@use '../../shared/styles/left-nav-details.component.scss';

:host {
  height: var(--left-panel-max-height);
  min-width: 800px;
  display: block;
  ::ng-deep {
    p {
      margin: 0;
    }
  }
}

footer {
  align-items: center;
  gap: 0;
}

mat-tab-group {
  ::ng-deep {
    .mat-mdc-tab-body-wrapper {
      flex: 1 1 0;
    }

    .mat-mdc-tab-label-container {
      margin-right: 110px;
    }
  }
}

.debts-container {
  display: flex;
  padding: 16px;
  flex-direction: column;
  align-items: flex-start;
  gap: 18px;
  padding: 16px;
  flex: 1 1 0;
}

.alternate-row {
  background-color: var(--rlxp-alternative-table-row-color);
}

table {
  border-radius: 5px;
  border: 1px solid #b1b1b0;
  background: #fff;
}

.chip-container {
  display: flex;
  align-items: center;
  gap: 8px;
  align-self: stretch;
}

.multi-line-cell-container {
  display: flex;
  flex-direction: column;
  gap: 2px;
  line-height: 1.2;
}

.exclusion-container {
  display: flex;
  align-items: center;
  width: 288px;
  gap: 10px;
  align-self: stretch;
}

.excluded-row-text {
  color: #949494;
}

#account-number.excluded-row-text {
  color: #949494;
}

.excluded-row-text-line-through {
  color: #949494;
  text-decoration: line-through;
}

#past-due-amount.past-due-amount-text {
  color: var(--rlxp-table-sublabel-warning-text-color);
}

#past-due-amount.excluded-row-text-line-through {
  color: #949494;
}

.table-footer-section {
  background-color: var(--rlxp-table-footer-color);
}

.label-container {
  display: flex;
  gap: 8px;
  align-items: center;
}

.error-icon {
  color: var(--rlxp-red-700);
}

.header-container {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 16px;
  z-index: 2;
}
