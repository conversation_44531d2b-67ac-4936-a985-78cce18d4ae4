import { <PERSON><PERSON><PERSON>cyPipe } from '@angular/common';
import { Component, computed, inject } from '@angular/core';
import { toObservable, toSignal } from '@angular/core/rxjs-interop';
import { ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatChipsModule } from '@angular/material/chips';
import { MatAccordion, MatExpansionModule } from '@angular/material/expansion';
import { MatFormFieldModule, MatLabel } from '@angular/material/form-field';
import { MatIcon } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { LiabilityType } from '@rocket-logic/rl-xp-bff-models';
import { map, merge, of, startWith, switchMap } from 'rxjs';
import { ClientSelectComponent } from '../../../../assets/client-select/client-select.component';
import { FormattedNumberInputComponent } from '../../../../question-input/formatted-number-input/formatted-number-input.component';
import { EntityStateName } from '../../../../services/entity-state/abstract-entity-state.service';
import { AllLiabilityGroup } from '../../../../services/entity-state/liability-state/form-types';
import { LiabilityFormService } from '../../../../services/entity-state/liability-state/liability-form.service';
import { LiabilityStateService } from '../../../../services/entity-state/liability-state/liability-state.service';
import { LoanIdService } from '../../../../services/loan-id/loan-id.service';
import { getCreditQualifierUrl } from '../../../../util/get-url';
import { AppLeftNavErrorsComponent } from '../../../shared/form-errors/left-nav-errors.component';

@Component({
  selector: 'app-alimony-details',
  standalone: true,
  imports: [
    MatChipsModule,
    MatAccordion,
    MatExpansionModule,
    MatFormFieldModule,
    MatLabel,
    ReactiveFormsModule,
    MatInputModule,
    FormattedNumberInputComponent,
    ClientSelectComponent,
    MatSlideToggleModule,
    CurrencyPipe,
    MatIcon,
    AppLeftNavErrorsComponent,
    MatButtonModule,
  ],
  templateUrl: './alimony-details.component.html',
  styleUrl: './alimony-details.component.scss',
})
export class AlimonyDetailsComponent {
  LiabilityType = LiabilityType;

  private liabilityFormService = inject(LiabilityFormService);
  private liabilityStateService = inject(LiabilityStateService);
  readonly loanIdService = inject(LoanIdService);

  readonly entityName = [EntityStateName.Liability];

  readonly liabilityStateValues = this.liabilityStateService.stateValues;
  readonly loanId = computed(() => this.loanIdService.loanId() ?? '');

  alimonyStateValues = computed(() => {
    return this.liabilityStateValues().filter((liability) => {
      return liability.liabilityType === LiabilityType.Alimony;
    });
  });

  alimonyForms = computed(() =>
    this.liabilityFormService
      .formMapValues()
      .filter((value) => value.controls.liabilityType.value === LiabilityType.Alimony),
  );

  alimonyEntries = computed(() =>
    this.liabilityFormService
      .formMapEntries()
      .filter(([_, liability]) => liability.value.liabilityType === LiabilityType.Alimony),
  );

  alimonyFormIds = computed(() =>
    this.liabilityFormService
      .formMapEntries()
      .filter(([_, formGroup]) => formGroup.controls.liabilityType.value === LiabilityType.Alimony)
      .map(([id, _]) => {
        return id;
      }),
  );

  totalAlimony = toSignal(
    toObservable(this.alimonyForms).pipe(
      switchMap((forms) => {
        const valueChangesObservables = forms
          .filter((form) => form?.controls?.monthlyPaymentAmount)
          .map((form) => form.controls.monthlyPaymentAmount.valueChanges);

        if (valueChangesObservables.length === 0) {
          return of(
            forms.reduce(
              (total, alimony) => total + (alimony?.value?.monthlyPaymentAmount ?? 0),
              0,
            ),
          );
        }

        return merge(...valueChangesObservables).pipe(
          startWith(null),
          map(() =>
            forms.reduce(
              (total, alimony) => total + (alimony?.value?.monthlyPaymentAmount ?? 0),
              0,
            ),
          ),
        );
      }),
    ),
    { initialValue: 0 },
  );

  toggleIncludeInRatios(entry: AllLiabilityGroup) {
    if (entry.controls.debtToIncomeRatioDetail.controls.includePaymentInDebtToIncomeRatio) {
      entry.controls.isPayingOffUnpaidBalanceAsPartOfTransaction.markAsDirty();
      entry.controls.isPayingOffUnpaidBalanceAsPartOfTransaction.setValue(false);
    }
  }

  addAlimony() {
    this.liabilityFormService.addLiability(LiabilityType.Alimony);
  }

  deleteAlimony(liabilityKey: string) {
    this.liabilityFormService.deleteLiability(liabilityKey);
  }

  openCq() {
    window.open(getCreditQualifierUrl(this.loanId()), '_blank');
  }
}
