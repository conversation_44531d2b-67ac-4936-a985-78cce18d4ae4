@use '../../../../../styling/accordion-panel-no-hover.scss';
@use '../../../shared/styles/left-nav-details.component.scss';
@use '../credit-details-shared' as *;

:host {
  height: 100%;
  display: flex;
  flex-direction: column;

  ::ng-deep {
    .rkt-AccordionPanel .mat-expansion-panel-body {
      padding: 0 24px 0 24px;
    }
  }
}

.alimony-container {
  @include support-container;
}

.alimony-accordian {
  @include support-accordion;
}

.alimony-support-form {
  @include support-form-grid;
}

.bottom-row-flex {
  @include bottom-row-flex;
}

.no-alimony-container {
  @include empty-container;
}

.default-add-alimony-button,
.add-alimony-button {
  @include add-button;
}

.go-to-credit-qualifier-button {
  @include support-button;
}

app-left-nav-errors {
  width: 100%;
}
