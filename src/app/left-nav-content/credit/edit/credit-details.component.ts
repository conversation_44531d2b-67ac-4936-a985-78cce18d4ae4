import { <PERSON><PERSON><PERSON>cyPipe } from '@angular/common';
import {
  AfterViewInit,
  Component,
  computed,
  effect,
  inject,
  signal,
  ViewChild,
} from '@angular/core';
import { FormControl, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatChipsModule } from '@angular/material/chips';
import { MatIconModule } from '@angular/material/icon';
import { MatFormField, MatLabel, MatOption, MatSelect } from '@angular/material/select';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatSort, MatSortModule } from '@angular/material/sort';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { MatTabsModule } from '@angular/material/tabs';
import { DebtToIncomeExclusionReason, LiabilityType } from '@rocket-logic/rl-xp-bff-models';
import { LeftPanelOverlayService } from '../../../[containers]/left-panel/left-panel-overlay.service';
import { SaveStatusComponent } from '../../../application-island/save-status/save-status.component';
import { ClientStateService } from '../../../services/entity-state/client-state/client-state.service';
import { LiabilityFormService } from '../../../services/entity-state/liability-state/liability-form.service';
import { LiabilityStateService } from '../../../services/entity-state/liability-state/liability-state.service';
import { LiabilityUpdateHandlerService } from '../../../services/entity-state/liability-state/liability-update-handler.service';
import { ValidationErrorProviderService } from '../../../services/error/validation-error-provider.service';
import { LoanIdService } from '../../../services/loan-id/loan-id.service';
import { createLiabilityTotalSignal } from '../../../util/create-liability-total-signal';
import { InvertSlideToggleDirective } from '../../../util/directives/invert-slide-toggle.directive';
import { getClientName } from '../../../util/get-client-name';
import { getEcoaCode } from '../../../util/get-ecoa-code';
import { getCreditQualifierUrl } from '../../../util/get-url';
import { PascalCaseSplitPipe } from '../../../util/pascal-case-split.pipe';
import { AlimonyDetailsComponent } from './alimony-details/alimony-details.component';
import { ChildSupportDetailsComponent } from './child-support-details/child-support-details.component';

interface CreditorAndAccountNumber {
  creditor: string;
  accountNumber: string;
}

interface MonthlyPaymentDetails {
  monthlyPayment: number;
  pastDueAmount: number;
}

interface ExclusionDetails {
  exclude: FormControl<boolean | null | undefined>;
  exclusionReason: FormControl<string | null | undefined>;
}

interface DebtTableRow {
  ecoa: string;
  creditorAndAccountNumber: CreditorAndAccountNumber;
  accountOwners: string | string[];
  liabilityType: string;
  balance: number;
  monthlyPaymentDetails: MonthlyPaymentDetails;
  paymentsRemaining: number;
  payoff: FormControl<boolean | null | undefined>;
  exclusionDetails: ExclusionDetails;
}

const AUTO_EXCLUSION_REASONS = [
  DebtToIncomeExclusionReason.PayOffWithNewFirst,
  DebtToIncomeExclusionReason.AccountOwnershipTerminated,
  DebtToIncomeExclusionReason.Open30DayChargeWithNoLatePayments,
  DebtToIncomeExclusionReason.TenPaymentsOrLessRemaining,
  DebtToIncomeExclusionReason.NinePaymentsOrLessRemaining,
];

@Component({
  selector: 'app-credit-details',
  standalone: true,
  imports: [
    CurrencyPipe,
    MatTabsModule,
    MatTableModule,
    MatSlideToggleModule,
    FormsModule,
    ReactiveFormsModule,
    MatSelect,
    MatOption,
    MatLabel,
    MatFormField,
    PascalCaseSplitPipe,
    MatChipsModule,
    MatButtonModule,
    MatIconModule,
    ChildSupportDetailsComponent,
    AlimonyDetailsComponent,
    InvertSlideToggleDirective,
    SaveStatusComponent,
    MatSortModule,
  ],
  templateUrl: './credit-details.component.html',
  styleUrl: './credit-details.component.scss',
})
export class CreditDetailsComponent implements AfterViewInit {
  @ViewChild(MatSort) sort?: MatSort;

  readonly LiabilityType = LiabilityType;
  readonly DebtToIncomeExclusionReason = Object.values(DebtToIncomeExclusionReason)
    .map((reason) => ({
      value: reason,
      disabled: AUTO_EXCLUSION_REASONS.includes(reason),
    }))
    .sort((a, b) => {
      if (a.disabled && !b.disabled) return 1;
      if (!a.disabled && b.disabled) return -1;
      if (a.value < b.value) return -1;
      if (a.value > b.value) return 1;
      return 0;
    });
  readonly loanIdService = inject(LoanIdService);
  readonly leftPanelOverlayService = inject(LeftPanelOverlayService);

  protected readonly liabilityFormService = inject(LiabilityFormService);
  readonly liabilityStateService = inject(LiabilityStateService);
  readonly liabilityUpdateHandler = inject(LiabilityUpdateHandlerService);
  protected readonly clientStateService = inject(ClientStateService);
  protected readonly validationErrorService = inject(ValidationErrorProviderService);

  readonly liabilityStateValues = this.liabilityStateService.stateValues;
  readonly filteredType = signal<LiabilityType | null | undefined>(null);
  readonly loanId = computed(() => this.loanIdService.loanId() ?? '');

  readonly clients = this.clientStateService.stateValues;
  readonly clientNameMap = computed(() => {
    const clientMap = new Map<string, string>();

    this.clients().forEach((client) => {
      clientMap.set(client.id!, getClientName(client));
    });

    return clientMap;
  });

  readonly displayedColumns = [
    'ECOA',
    'CreditorAndAccountNumber',
    'AccountOwners',
    'LiabilityType',
    'Balance',
    'MonthlyPaymentDetails',
    'PaymentRemaining',
    'Payoff',
    'ExclusionDetails',
  ];

  readonly dataSource = new MatTableDataSource<DebtTableRow>();

  revolvingForms = computed(() => {
    return this.liabilityFormService
      .formMapValues()
      .filter((value) => value.controls.liabilityType.value === LiabilityType.Revolving);
  });

  installmentForms = computed(() => {
    return this.liabilityFormService
      .formMapValues()
      .filter(
        (value) => value.controls.liabilityType.value === LiabilityType.MiscellaneousInstallment,
      );
  });

  currentLiabilityForms = computed(() => {
    return this.filteredType()
      ? this.liabilityFormService.formMapValues().filter((liability) => {
          return (
            !liability.controls.isSecuredByLienAgainstProperty.value &&
            liability.controls.debtToIncomeRatioDetail.controls.includePaymentInDebtToIncomeRatio
              .value &&
            !liability.controls.isPayingOffUnpaidBalanceAsPartOfTransaction.value &&
            !(liability.value.liabilityType === LiabilityType.ChildSupport) &&
            !(liability.value.liabilityType === LiabilityType.Alimony) &&
            liability.controls.liabilityType.value === this.filteredType()
          );
        })
      : this.liabilityFormService.formMapValues().filter((liability) => {
          return (
            !liability.controls.isSecuredByLienAgainstProperty.value &&
            !(liability.value.liabilityType === LiabilityType.ChildSupport) &&
            !(liability.value.liabilityType === LiabilityType.Alimony)
          );
        });
  });

  alimonyFormIds = computed(() =>
    this.liabilityFormService
      .formMapEntries()
      .filter(([_, formGroup]) => formGroup.controls.liabilityType.value === LiabilityType.Alimony)
      .map(([id, _]) => {
        return id;
      }),
  );

  childSupportFormIds = computed(() =>
    this.liabilityFormService
      .formMapEntries()
      .filter(
        ([_, formGroup]) => formGroup.controls.liabilityType.value === LiabilityType.ChildSupport,
      )
      .map(([id, _]) => {
        return id;
      }),
  );

  hasAlimonyErrors = computed(() =>
    this.validationErrorService
      .errors()
      .some((error) => (error.formId ? this.alimonyFormIds().includes(error.formId) : false)),
  );

  hasChildSupportErrors = computed(() =>
    this.validationErrorService
      .errors()
      .some((error) => (error.formId ? this.childSupportFormIds().includes(error.formId) : false)),
  );

  totalRevolvingBalance = createLiabilityTotalSignal(this.revolvingForms, 'unpaidBalance');

  totalInstallmentBalance = createLiabilityTotalSignal(this.installmentForms, 'unpaidBalance');

  totalBalance = createLiabilityTotalSignal(
    this.currentLiabilityForms,
    'unpaidBalance',
    (form) => !this.filteredType() || form.controls.liabilityType.value === this.filteredType(),
  );

  totalMonthlyPayment = createLiabilityTotalSignal(
    this.currentLiabilityForms,
    'monthlyPaymentAmount',
    (form) => !this.filteredType() || form.controls.liabilityType.value === this.filteredType(),
  );

  private readonly tableData = computed(() => {
    const debtsTable: DebtTableRow[] = [];

    this.currentLiabilityForms().forEach((liability) => {
      debtsTable.push({
        ecoa: getEcoaCode(
          liability.controls.clientIds.value ?? [],
          liability.controls.accountOwnershipType.value ?? null,
          this.clients(),
        ),
        creditorAndAccountNumber: {
          creditor: liability.controls.creditorName.value ?? '-',
          accountNumber: liability.controls.accountId.value?.slice(-4) ?? '',
        },
        accountOwners: this.getClientsFromLiability(liability.controls.clientIds?.value ?? []),
        liabilityType: liability.controls.liabilityType.value ?? '-',
        balance: liability.controls.unpaidBalance.value ?? 0,
        monthlyPaymentDetails: {
          monthlyPayment: liability.controls.monthlyPaymentAmount.value ?? 0,
          pastDueAmount: liability.controls.pastDueAmount.value ?? 0,
        },
        paymentsRemaining: liability.controls.repaymentTermInMonths.value ?? 0,
        payoff: liability.controls.isPayingOffUnpaidBalanceAsPartOfTransaction,
        exclusionDetails: {
          exclude:
            liability.controls.debtToIncomeRatioDetail.controls.includePaymentInDebtToIncomeRatio,
          exclusionReason:
            liability.controls.debtToIncomeRatioDetail.controls
              .reasonForExcludingPaymentFromDebtToIncomeRatio,
        },
      });
    });

    return debtsTable;
  });

  resetTable() {
    this.filteredType.set(null);
  }

  getClientsFromLiability(clientIds: string[]) {
    return clientIds.map((clientId) => this.clientNameMap().get(clientId) ?? '').join(', ');
  }

  openCq() {
    window.open(getCreditQualifierUrl(this.loanId()), '_blank');
  }

  constructor() {
    effect(() => {
      this.dataSource.data = this.tableData();
      if (this.sort) {
        this.dataSource.sort = this.sort;
      }
    });
  }

  ngAfterViewInit() {
    if (this.sort) {
      this.dataSource.sort = this.sort;
    }

    this.dataSource.sortingDataAccessor = (data: DebtTableRow, sortHeaderId: string) => {
      switch (sortHeaderId) {
        case 'ECOA':
          return data.ecoa;
        case 'CreditorAndAccountNumber':
          return data.creditorAndAccountNumber.creditor;
        case 'AccountOwners':
          return data.accountOwners;
        case 'LiabilityType':
          return data.liabilityType;
        case 'Balance':
          return data.balance;
        case 'MonthlyPaymentDetails':
          return data.monthlyPaymentDetails.monthlyPayment;
        case 'PaymentRemaining':
          return data.paymentsRemaining;
        default:
          return (data as any)[sortHeaderId];
      }
    };
  }
}
