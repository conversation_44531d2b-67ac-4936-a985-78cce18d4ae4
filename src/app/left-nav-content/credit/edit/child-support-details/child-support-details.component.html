<div class="child-support-container" [class.justify-center]="childSupportForms().length === 0">
  @if (childSupportForms().length > 0) {
    <div class="flex-1 flex flex-col gap-4 p-4">
      <app-left-nav-errors [entityName]="entityName" [formIds]="childSupportFormIds()" />
      <mat-chip-set>
        <mat-chip class="rkt-Chip"
          >Total Child Support: {{ totalChildSupport() | currency }}</mat-chip
        >
      </mat-chip-set>
      @for (entry of childSupportEntries(); track entry; let i = $index) {
        <mat-accordion class="child-support-accordian">
          <mat-expansion-panel
            class="rkt-AccordionPanel rkt-AccordionPanel--enterprise"
            [expanded]="true"
          >
            <mat-expansion-panel-header>
              <mat-panel-title class="rkt-AccordionPanel__header-title center-aligned">
                <p class="rkt-Label-14">Child Support {{ i + 1 }}</p>
              </mat-panel-title>
              <div class="flex items-center mr-4">
                <button
                  mat-icon-button
                  class="rkt-IconButton rkt-IconButton--small mr-4"
                  color="accent"
                  (click)="deleteChildSupport(entry[0]); $event.stopPropagation()"
                >
                  <mat-icon class="rkt-Icon" svgIcon="delete-outlined" />
                </button>
              </div>
            </mat-expansion-panel-header>
            <div class="child-support-form">
              <mat-form-field class="rkt-FormField col-span-3">
                <mat-label>Paid to</mat-label>
                <input matInput class="rkt-Input" [formControl]="entry[1].controls.creditorName" />
              </mat-form-field>
              <app-formatted-number-input
                class="col-span-3"
                label="Obligated Monthly Payment"
                prefix="$"
                [control]="entry[1].controls.monthlyPaymentAmount"
                [allowNegative]="false"
              />
              <app-formatted-number-input
                class="col-span-3"
                label="Months Remaining"
                [control]="entry[1].controls.repaymentTermInMonths"
                [allowNegative]="false"
              />
              <div class="col-span-9 bottom-row-flex">
                <app-client-select
                  class="col-span-4"
                  label="Debt Owner"
                  [control]="entry[1].controls.clientIds"
                >
                </app-client-select>
                <mat-slide-toggle
                  class="rkt-SlideToggle col-span-5"
                  color="accent"
                  [formControl]="entry[1].controls.arePaymentsCourtOrdered"
                >
                  <span class="rkt-SlideToggle__label rkt-Spacing--ml8"
                    >Payments Court Ordered</span
                  >
                </mat-slide-toggle>
                <mat-slide-toggle
                  class="rkt-SlideToggle col-span-3"
                  color="accent"
                  [formControl]="
                    entry[1].controls.debtToIncomeRatioDetail.controls
                      .includePaymentInDebtToIncomeRatio
                  "
                  (toggleChange)="toggleIncludeInRatios(entry[1])"
                >
                  <span class="rkt-SlideToggle__label rkt-Spacing--ml8">Include in Ratios</span>
                </mat-slide-toggle>
              </div>
            </div>
          </mat-expansion-panel>
        </mat-accordion>
      }
    </div>
    <footer>
      <button
        mat-button
        class="go-to-credit-qualifier-button rkt-Button rkt-Button--tertiary rkt-Button--has-icon"
        (click)="openCq()"
      >
        Credit Qualifier
        <mat-icon svgIcon="open_in_new-outlined" color="accent" class="rkt-Icon" />
      </button>
      <button
        mat-button
        class="add-child-support-button rkt-Button rkt-Button--secondary rkt-Button--has-icon"
        color="accent"
        (click)="addChildSupport()"
      >
        <mat-icon class="rkt-Icon" svgIcon="add-outlined" />
        Add Child Support
      </button>
    </footer>
  } @else {
    <div class="no-child-support-container">
      <p class="rkt-Label-14">No child support added</p>
      <button
        mat-button
        class="default-add-child-support-button rkt-Button rkt-Button--secondary rkt-Button--has-icon"
        color="accent"
        (click)="addChildSupport()"
      >
        <mat-icon class="rkt-Icon" svgIcon="add-outlined" />
        Add Child Support
      </button>
      <button
        mat-button
        class="go-to-credit-qualifier-button rkt-Button rkt-Button--tertiary rkt-Button--has-icon"
        color="accent"
        (click)="openCq()"
      >
        <mat-icon class="rkt-Icon" svgIcon="open_in_new-outlined" />
        Credit Qualifier
      </button>
    </div>
  }
</div>
