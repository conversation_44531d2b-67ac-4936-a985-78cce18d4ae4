@use '../../../../../styling/accordion-panel-no-hover.scss';
@use '../../../shared/styles/left-nav-details.component.scss';
@use '../credit-details-shared' as *;

:host {
  height: 100%;
  display: flex;
  flex-direction: column;

  ::ng-deep {
    .rkt-AccordionPanel .mat-expansion-panel-body {
      padding: 0 24px 0 24px;
    }
  }
}

.child-support-container {
  @include support-container;
}

.child-support-accordian {
  @include support-accordion;
}

.child-support-form {
  @include support-form-grid;
}

.bottom-row-flex {
  @include bottom-row-flex;
}

.no-child-support-container {
  @include empty-container;
}

.default-add-child-support-button,
.add-child-support-button {
  @include add-button;
}

.go-to-credit-qualifier-button {
  @include support-button;
}

app-left-nav-errors {
  width: 100%;
}
