<div class="header-container absolute top-3 right-4">
  <app-save-status
    [updateHandlers]="[liabilityUpdateHandler]"
    [stateServices]="[liabilityStateService]"
  />
  <button
    mat-icon-button
    class="rkt-ButtonIcon"
    aria-label="Close"
    (click)="leftPanelOverlayService.hidePopout()"
  >
    <mat-icon class="rkt-Icon">close</mat-icon>
  </button>
</div>

<mat-tab-group
  mat-stretch-tabs="false"
  disablePagination="true"
  animationDuration="0"
  class="rkt-Tabs h-full"
  [disableRipple]="true"
>
  <mat-tab label="Edit Debts">
    <div class="flex flex-col h-full">
      <div class="debts-container">
        <div class="chip-container">
          <mat-chip-set>
            <mat-chip
              [class]="
                filteredType() === LiabilityType.Revolving
                  ? 'rkt-Chip rkt-Chip--secondary'
                  : 'rkt-Chip rkt-Chip--light'
              "
              (click)="filteredType.set(LiabilityType.Revolving)"
              >Total Revolving: {{ totalRevolvingBalance() | currency }}</mat-chip
            >
            <mat-chip
              [class]="
                filteredType() === LiabilityType.MiscellaneousInstallment
                  ? 'rkt-Chip rkt-Chip--secondary'
                  : 'rkt-Chip rkt-Chip--light'
              "
              (click)="filteredType.set(LiabilityType.MiscellaneousInstallment)"
              >Total Installment: {{ totalInstallmentBalance() | currency }}</mat-chip
            >
          </mat-chip-set>
          @if (filteredType()) {
            <button
              mat-button
              class="rkt-Button rkt-Button--tertiary rkt-Button--has-icon"
              (click)="resetTable()"
            >
              <mat-icon svgIcon="refresh-outlined" color="accent" class="rkt-Icon" />
              Reset
            </button>
          }
        </div>
        <div class="rkt-TableContainer">
          <table
            mat-table
            class="rkt-Table"
            matSort
            matSortActive="MonthlyPaymentDetails"
            matSortDirection="desc"
            [dataSource]="dataSource"
          >
            <ng-container matColumnDef="ECOA">
              <th *matHeaderCellDef class="rkt-Table__header" mat-header-cell mat-sort-header>
                ECOA
              </th>
              <td *matCellDef="let element" class="rkt-Table__cell" mat-cell>
                <span
                  [class.excluded-row-text]="
                    !element.exclusionDetails.exclude.value || element.payoff.value
                  "
                  >{{ element.ecoa }}</span
                >
              </td>
              <td *matFooterCellDef mat-footer-cell></td>
            </ng-container>

            <ng-container matColumnDef="CreditorAndAccountNumber">
              <th *matHeaderCellDef class="rkt-Table__header" mat-header-cell mat-sort-header>
                Creditor
              </th>
              <td *matCellDef="let element" class="rkt-Table__cell" mat-cell>
                <div class="multi-line-cell-container">
                  <span
                    [class.excluded-row-text]="
                      !element.exclusionDetails.exclude.value || element.payoff.value
                    "
                    >{{ element.creditorAndAccountNumber.creditor }}</span
                  >
                  @if (element.creditorAndAccountNumber.accountNumber) {
                    <span
                      id="account-number"
                      [class]="
                        !element.exclusionDetails.exclude.value || element.payoff.value
                          ? 'excluded-row-text rkt-Caption-10'
                          : 'rkt-Caption-10'
                      "
                      >ending {{ element.creditorAndAccountNumber.accountNumber }}</span
                    >
                  }
                </div>
              </td>
              <td *matFooterCellDef mat-footer-cell></td>
            </ng-container>

            <ng-container matColumnDef="AccountOwners">
              <th *matHeaderCellDef class="rkt-Table__header" mat-header-cell mat-sort-header>
                Account Owner(s)
              </th>
              <td *matCellDef="let element" class="rkt-Table__cell" mat-cell>
                <span
                  [class.excluded-row-text]="
                    !element.exclusionDetails.exclude.value || element.payoff.value
                  "
                  >{{ element.accountOwners }}</span
                >
              </td>
              <td *matFooterCellDef mat-footer-cell></td>
            </ng-container>

            <ng-container matColumnDef="LiabilityType">
              <th *matHeaderCellDef class="rkt-Table__header" mat-header-cell mat-sort-header>
                Liability Type
              </th>
              <td *matCellDef="let element" class="rkt-Table__cell" mat-cell>
                <span
                  [class.excluded-row-text]="
                    !element.exclusionDetails.exclude.value || element.payoff.value
                  "
                  >{{ element.liabilityType | pascalCaseSplit }}</span
                >
              </td>
              <td *matFooterCellDef mat-footer-cell></td>
            </ng-container>

            <ng-container matColumnDef="Balance">
              <th *matHeaderCellDef class="rkt-Table__header" mat-header-cell mat-sort-header>
                Balance
              </th>
              <td *matCellDef="let element" class="rkt-Table__cell" mat-cell>
                <span
                  [class.excluded-row-text-line-through]="
                    !element.exclusionDetails.exclude.value || element.payoff.value
                  "
                  >{{ element.balance | currency }}</span
                >
              </td>
              <td *matFooterCellDef mat-footer-cell>{{ totalBalance() | currency }}</td>
            </ng-container>

            <ng-container matColumnDef="MonthlyPaymentDetails">
              <th *matHeaderCellDef class="rkt-Table__header" mat-header-cell mat-sort-header>
                Monthly Payment
              </th>
              <td *matCellDef="let element" class="rkt-Table__cell" mat-cell>
                <div class="multi-line-cell-container">
                  <span
                    [class.excluded-row-text-line-through]="
                      !element.exclusionDetails.exclude.value || element.payoff.value
                    "
                    >{{ element.monthlyPaymentDetails.monthlyPayment | currency }}</span
                  >
                  @if (element.monthlyPaymentDetails.pastDueAmount) {
                    <span
                      id="past-due-amount"
                      [class]="
                        !element.exclusionDetails.exclude.value || element.payoff.value
                          ? 'excluded-row-text-line-through rkt-Caption-10'
                          : 'past-due-amount-text rkt-Caption-10'
                      "
                      >Past Due: {{ element.monthlyPaymentDetails.pastDueAmount | currency }}</span
                    >
                  }
                </div>
              </td>
              <td *matFooterCellDef mat-footer-cell>{{ totalMonthlyPayment() | currency }}</td>
            </ng-container>

            <ng-container matColumnDef="PaymentRemaining">
              <th *matHeaderCellDef class="rkt-Table__header" mat-header-cell mat-sort-header>
                Remaining Payment(s)
              </th>
              <td *matCellDef="let element" class="rkt-Table__cell" mat-cell>
                <span
                  [class.excluded-row-text]="
                    !element.exclusionDetails.exclude.value || element.payoff.value
                  "
                  >{{ element.paymentsRemaining }}</span
                >
              </td>
              <td *matFooterCellDef mat-footer-cell></td>
            </ng-container>

            <ng-container matColumnDef="Payoff">
              <th *matHeaderCellDef class="rkt-Table__header" mat-header-cell>Payoff</th>
              <td *matCellDef="let element" class="rkt-Table__cell" mat-cell>
                <mat-slide-toggle class="rkt-SlideToggle" [formControl]="element.payoff" />
              </td>
              <td *matFooterCellDef mat-footer-cell></td>
            </ng-container>

            <ng-container matColumnDef="ExclusionDetails">
              <th *matHeaderCellDef class="rkt-Table__header" mat-header-cell>Exclude</th>
              <td *matCellDef="let element" class="rkt-Table__cell" mat-cell>
                <div [class.exclusion-container]="!element.exclusionDetails.exclude.value">
                  <mat-slide-toggle
                    class="rkt-SlideToggle"
                    appInvertSlideToggle
                    [formControl]="element.exclusionDetails.exclude"
                  />
                  @if (!element.exclusionDetails.exclude.value) {
                    <mat-form-field subscriptSizing="dynamic">
                      <mat-label>Select Reason</mat-label>
                      <mat-select
                        class="rkt-Input"
                        [formControl]="element.exclusionDetails.exclusionReason"
                      >
                        <mat-option [value]="null">None</mat-option>
                        @for (option of DebtToIncomeExclusionReason; track option.value) {
                          <mat-option [disabled]="option.disabled" [value]="option.value">{{
                            option.value | pascalCaseSplit
                          }}</mat-option>
                        }
                      </mat-select>
                    </mat-form-field>
                  }
                </div>
              </td>
              <td *matFooterCellDef mat-footer-cell></td>
            </ng-container>

            <tr
              *matHeaderRowDef="displayedColumns; sticky: true"
              class="rkt-Table__header-row"
              mat-header-row
            ></tr>
            <tr
              *matRowDef="let row; columns: displayedColumns; let i = index"
              mat-row
              [class]="{ 'alternate-row': i % 2 === 0 }"
            ></tr>
            <tr
              *matFooterRowDef="displayedColumns"
              class="table-footer-section"
              mat-footer-row
            ></tr>
          </table>
        </div>
      </div>
      <footer>
        <p class="rkt-Label-14 rkt-FontWeight--400">To add or edit debts go to,</p>
        <button
          mat-button
          class="rkt-Button rkt-Button--tertiary rkt-Button--has-icon"
          (click)="openCq()"
        >
          Credit Qualifier
          <mat-icon svgIcon="open_in_new-outlined" color="accent" class="rkt-Icon" />
        </button>
      </footer>
    </div>
  </mat-tab>

  <mat-tab>
    <ng-template mat-tab-label>
      <div class="label-container">
        Add/Edit Alimony
        @if (hasAlimonyErrors()) {
          <mat-icon class="rkt-Icon error-icon" svgIcon="error-outlined" />
        }
      </div>
    </ng-template>
    <ng-template matTabContent>
      <app-alimony-details />
    </ng-template>
  </mat-tab>

  <mat-tab>
    <ng-template mat-tab-label>
      <div class="label-container">
        Add/Edit Child Support
        @if (hasChildSupportErrors()) {
          <mat-icon class="rkt-Icon error-icon" svgIcon="error-outlined" />
        }
      </div>
    </ng-template>
    <ng-template matTabContent>
      <app-child-support-details />
    </ng-template>
  </mat-tab>
</mat-tab-group>
