<h2 class="rkt-Tracked-12">Credit</h2>

<div class="tile-section">
  <app-tile
    label="Qualifying Credit Score"
    [isLoading]="isCreditServiceLoading() ?? true"
    [icon]="creditReportError() ? 'error-outlined' : ''"
    [variant]="creditReportError() ? 'warn' : 'default'"
  >
    @if (creditReportError()) {
      <strong>Sorry, unavailable now</strong>
    } @else {
      <strong>{{ qualifyingFicoScore() }}</strong>
    }
  </app-tile>

  <app-tile label="Monthly Debt" [isLoading]="isLiabilityStateServiceLoading()">
    <strong>{{ monthlyDebt() }}</strong>
  </app-tile>

  <app-tile label="Pay Off With This Loan" [isLoading]="isLiabilityStateServiceLoading()">
    <strong>{{ totalPayoffWithLoan() | currency }}</strong>
  </app-tile>
</div>
