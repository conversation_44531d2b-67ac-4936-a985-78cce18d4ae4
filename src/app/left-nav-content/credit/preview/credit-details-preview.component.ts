import { CurrencyPipe, formatCurrency } from '@angular/common';
import { Component, computed, inject } from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import {
  DebtToIncomeExclusionReason,
  isLiabilitySecuredByLien,
} from '@rocket-logic/rl-xp-bff-models';
import { distinctUntilChanged, map } from 'rxjs';
import { CreditService } from '../../../services/credit/credit.service';
import { LiabilityFormService } from '../../../services/entity-state/liability-state/liability-form.service';
import { LiabilityStateService } from '../../../services/entity-state/liability-state/liability-state.service';
import { LoanDetailsChangedService } from '../../../services/loan-details-changed/loan-details-changed.service';
import { TileComponent } from '../../../tile/tile.component';

@Component({
  selector: 'app-credit-details-preview',
  standalone: true,
  imports: [TileComponent, CurrencyPipe],
  templateUrl: './credit-details-preview.component.html',
  styleUrl: './credit-details-preview.component.scss',
})
export class CreditDetailsPreviewComponent {
  creditService = inject(CreditService);
  loanSummary = inject(LoanDetailsChangedService).loanSummary;
  liabilityStateService = inject(LiabilityStateService);
  liabilityFormService = inject(LiabilityFormService);

  readonly isLiabilityStateServiceLoading = computed(
    () => this.liabilityStateService.state()?.fetching ?? false,
  );
  readonly isCreditServiceLoading = toSignal(
    this.creditService.creditReports$.pipe(
      map((report) => report.fetching),
      distinctUntilChanged(),
    ),
  );

  creditReportError = toSignal(
    this.creditService.creditReports$.pipe(
      map((report) => report.error),
      distinctUntilChanged(),
    ),
  );

  liabilityStateValues = this.liabilityStateService.stateValues;

  totalPayoffWithLoan = computed(() =>
    this.liabilityFormService
      .formMapValues()
      .filter(
        (liability) =>
          !liability.controls.isSecuredByLienAgainstProperty.value &&
          liability.controls.debtToIncomeRatioDetail.controls
            .reasonForExcludingPaymentFromDebtToIncomeRatio.value ===
            DebtToIncomeExclusionReason.PayOffWithNewFirst,
      )
      .reduce((total, liability) => total + (liability.value.unpaidBalance ?? 0), 0),
  );

  qualifyingFicoScore = computed(() => {
    const activeReports = this.creditService.activeCreditReports();

    const ficoScores = activeReports.reduce((scores, report) => {
      if (report.creditReport.qualifyingScore) {
        scores.push(`FICO ${report.creditReport.qualifyingScore}`);
      }
      return scores;
    }, [] as string[]);
    return ficoScores.length > 0 ? ficoScores : '--';
  });

  monthlyDebt = computed(() => {
    const totalPayment = this.liabilityStateValues()
      .filter(
        (liability) =>
          !(
            isLiabilitySecuredByLien(liability) && liability.isSecuredByLienAgainstProperty === true
          ),
      )
      .reduce((payment, liability) => payment + (liability?.monthlyPaymentAmount ?? 0), 0);

    return totalPayment > 0 ? formatCurrency(totalPayment, 'en-US', '$', 'USD') : '$0';
  });
}
