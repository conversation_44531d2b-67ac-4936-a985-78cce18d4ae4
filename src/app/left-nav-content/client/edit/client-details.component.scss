@use '../../shared/styles/left-nav-details.component.scss';

:host {
  mat-tab-group {
    gap: 24px;
  }

  ::ng-deep app-loan-milestone-progress button {
    white-space: nowrap;

    span.mdc-button__label {
      width: fit-content;
    }
  }
}

header {
  padding: 24px 0 24px 16px;
}

.body {
  padding: 0 16px 16px 16px;
}

.container {
  display: flex;
  flex-direction: column;
}

.button-container {
  display: flex;
  justify-content: flex-start;
  gap: 8px;
}

mat-card {
  border: 1px solid var(--rlxp-gray-200, #d3d3d3);
}

.social-security-container {
  padding-top: 24px;
  gap: 12px;
}
