import { ChangeDetectionStrategy, Component, computed, inject, input } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatDividerModule } from '@angular/material/divider';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { RktTagEnterpriseModule } from '@rocketcentral/rocket-design-system-enterprise-angular';
import { AppIslandAction } from '../../../application-island/app-island-action-button/app-island-action-button.component';
import { LoanMilestoneProgressComponent } from '../../../application-island/loan-milestone-progress/loan-milestone-progress.component';
import { AddClientButtonComponent } from '../../../clients-info/add-client-button/add-client-button.component';
import { ClientInfoComponent } from '../../../clients-info/client-info/client-info.component';
import { ClientSkeletonComponent } from '../../../clients-info/client-skeleton/client-skeleton.component';
import { SocialSecurityDobDisplayComponent } from '../../../credit-info/social-security-dob-display/social-security-dob-display.component';
import { ClientCategorizationFacadeService } from '../../../declarations-demographics/services/client-categorization.service';
import { EntityStateName } from '../../../services/entity-state/abstract-entity-state.service';
import { ClientActionsHandlerService } from '../../../services/entity-state/client-state/client-actions-handler.component';
import { ClientFormService } from '../../../services/entity-state/client-state/client-form.service';
import { ClientStateService } from '../../../services/entity-state/client-state/client-state.service';
import { ClientControls } from '../../../services/entity-state/client-state/form-types';
import { LoanEditingState } from '../../../services/entity-state/loan-state/loan-editing-state.service';
import { FormNavInputService } from '../../../services/form-nav/form-nav-input.service';
import { LoanIdService } from '../../../services/loan-id/loan-id.service';
import { getMortgageClientUrl } from '../../../util/get-url';
import { ClientTileDisplayComponent } from '../../shared/client-tile-display/client-tile-display.component';
import { AppLeftNavErrorsComponent } from '../../shared/form-errors/left-nav-errors.component';

@Component({
  selector: 'app-client-details',
  standalone: true,
  imports: [
    RktTagEnterpriseModule,
    ClientInfoComponent,
    ClientSkeletonComponent,
    MatCardModule,
    MatFormFieldModule,
    MatDividerModule,
    SocialSecurityDobDisplayComponent,
    ClientTileDisplayComponent,
    MatButtonModule,
    MatIconModule,
    AddClientButtonComponent,
    AppLeftNavErrorsComponent,
    LoanMilestoneProgressComponent,
  ],
  providers: [FormNavInputService],
  templateUrl: './client-details.component.html',
  styleUrl: './client-details.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ClientDetailsComponent {
  selectedTabIndex = input<number>(0);

  readonly activeActionOverride = AppIslandAction.CreditRequest;

  private readonly clientStateService = inject(ClientStateService);
  private readonly clientCategorizationFacade = inject(ClientCategorizationFacadeService);
  readonly loanIdService = inject(LoanIdService);
  readonly clientActionsHandlerService = inject(ClientActionsHandlerService);
  readonly clientFormService = inject(ClientFormService);

  readonly loanId = computed(() => this.loanIdService.loanId());
  readonly entityName = [EntityStateName.Client];

  isFetching = this.clientStateService.isFetching;
  isLoanEditingDisabled = inject(LoanEditingState).isLoanEditingDisabled;
  clients = computed(() => this.clientStateService.stateValues() ?? []);

  selectedClientFormEntry = computed(() => {
    const foundForm = this.clientFormService
      .sortedEntityForms()
      ?.find((clientForm) => clientForm[0] === this.clientCategorizationFacade.selectedFormId());
    return foundForm;
  });

  selectedClientFormId = computed(() => this.selectedClientFormEntry()?.[0]);
  selectedClientForm = computed(() => this.selectedClientFormEntry()?.[1]);

  navigateToMC() {
    if (this.loanId()) {
      window.location.href = getMortgageClientUrl(this.loanId()!);
    }
  }

  handleAddClient(client: FormGroup<ClientControls>) {
    const clientFormEntry = this.clientFormService
      .entityFormapEntries()
      ?.find((entry) => entry[1] === client);

    if (clientFormEntry) {
      this.clientCategorizationFacade.selectClient(clientFormEntry[0]);
    }
  }
}
