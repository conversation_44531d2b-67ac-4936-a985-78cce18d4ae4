<div class="container">
  <header>
    <div class="button-container">
      <app-client-tile-display [hasCustomButton]="true" />
      <app-add-client-button (addClient)="handleAddClient($event)" [isIconOnlyView]="true" />
    </div>
  </header>
  <div class="body">
    @if (!isFetching()) {
      <app-left-nav-errors [entityName]="entityName" [formIds]="[selectedClientFormId()!]" />
      <mat-card class="rkt-Card rkt-Card--enterprise">
        <app-loan-milestone-progress class="mb-2" [activeActionOverride]="activeActionOverride" />
        @if (selectedClientForm(); as clientForm) {
          <app-client-info
            [isLoanEditingDisabled]="isLoanEditingDisabled()"
            [clientForms]="clientFormService.entityValues()"
            [clientForm]="clientForm"
            [clients]="clients()"
            [selectedClientFormId]="selectedClientFormId()!"
            (addPhone)="clientActionsHandlerService.onAddPhone(selectedClientFormId()!)"
            (addPreviousAddress)="
              clientActionsHandlerService.onAddPreviousAddress(selectedClientFormId()!)
            "
          />
          <mat-divider class="rkt-HorizontalDivider" />
          <div class="social-security-container">
            <p class="rkt-Label-14 rkt-FontWeight--500">Social Security</p>
            <app-social-security-dob-display [clientForm]="clientForm" />
          </div>
        }
      </mat-card>
    } @else {
      <app-client-skeleton />
    }
  </div>
  <footer>
    <button
      [disabled]="isFetching()"
      mat-button
      class="rkt-Button rkt-Button--tertiary rkt-Button--has-icon"
      [class.rkt-Button--is-disabled]="isFetching()"
      color="accent"
      (click)="navigateToMC()"
    >
      <mat-icon class="rkt-Icon" color="secondary" svgIcon="open_in_new-outlined" />
      Additional Client Info
    </button>
  </footer>
</div>
