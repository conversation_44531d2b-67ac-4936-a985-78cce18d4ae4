import { Component, inject, input } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTabsModule } from '@angular/material/tabs';
import { RktTagEnterpriseModule } from '@rocketcentral/rocket-design-system-enterprise-angular';
import { LeftPanelOverlayService } from '../../../[containers]/left-panel/left-panel-overlay.service';
import { SaveStatusComponent } from '../../../application-island/save-status/save-status.component';
import { DeclarationsDemographicsComponent } from '../../../declarations-demographics/declarations-demographics.component';
import { ClientStateService } from '../../../services/entity-state/client-state/client-state.service';
import { ClientUpdateHandlerService } from '../../../services/entity-state/client-state/client-update-handler.service';

import { LiabilityUpdateHandlerService } from '../../../services/entity-state/liability-state/liability-update-handler.service';
import { ClientDetailsComponent } from '../edit/client-details.component';

@Component({
  selector: 'app-edit-client-base',
  standalone: true,
  imports: [
    MatTabsModule,
    RktTagEnterpriseModule,
    DeclarationsDemographicsComponent,
    ClientDetailsComponent,
    SaveStatusComponent,
    MatIconModule,
    MatButtonModule,
  ],

  templateUrl: './edit-client-base.component.html',
  styleUrl: './edit-client-base.component.scss',
})
export class EditClientBaseComponent {
  protected readonly clientStateService = inject(ClientStateService, { optional: true });
  protected readonly clientUpdateHandler = inject(ClientUpdateHandlerService, { optional: true });
  protected readonly liabilityUpdateHandlerService = inject(LiabilityUpdateHandlerService, { optional: true });
  protected readonly leftPanelOverlayService = inject(LeftPanelOverlayService);

  selectedTabIndex = input<number>(0);
}
