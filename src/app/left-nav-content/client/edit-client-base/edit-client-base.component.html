<div class="header-container absolute top-3 right-4">
  @if (clientStateService && clientUpdateHandler && liabilityUpdateHandlerService) {
    <app-save-status
      [updateHandlers]="[clientUpdate<PERSON>and<PERSON>, liabilityUpdateHandlerService]"
      [stateServices]="[clientStateService]"
    />
  }

  <button
    mat-icon-button
    class="rkt-ButtonIcon"
    aria-label="Close"
    (click)="leftPanelOverlayService.hidePopout()"
  >
    <mat-icon class="rkt-Icon">close</mat-icon>
  </button>
</div>

<mat-tab-group
  mat-stretch-tabs="false"
  disablePagination="true"
  animationDuration="0"
  [selectedIndex]="selectedTabIndex()"
  class="rkt-Tabs h-full"
  [disableRipple]="true"
>
  <mat-tab label="Client Info">
    <app-client-details />
  </mat-tab>

  <mat-tab label="Declarations & Demographics">
    <app-declarations-demographics />
  </mat-tab>
</mat-tab-group>
