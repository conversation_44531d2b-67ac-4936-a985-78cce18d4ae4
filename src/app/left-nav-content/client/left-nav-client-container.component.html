@if (isEditScreen()) {
  <app-edit-client-base [selectedTabIndex]="selectedTabIndex" />
} @else {
  <div>
    <app-client-details-preview />
    <div class="button-section">
      <!-- TOD<PERSON> will need to create a handler to show Clients or Decs -->
      <button
        class="rkt-Button rkt-Button--secondary"
        mat-stroked-button
        color="accent"
        (click)="editClickHandler(0)"
      >
        Edit Client(s)
      </button>

      <button
        class="rkt-Button rkt-Button--secondary"
        mat-stroked-button
        color="accent"
        (click)="editClickHandler(1)"
      >
        Edit Declarations
      </button>
    </div>
  </div>
}
