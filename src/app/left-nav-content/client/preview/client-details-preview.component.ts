import { Component, computed, inject } from '@angular/core';
import { MatDivider } from '@angular/material/divider';
import { MatTabsModule } from '@angular/material/tabs';
import { RktTagEnterpriseModule } from '@rocketcentral/rocket-design-system-enterprise-angular';
import { ClientStateService } from '../../../services/entity-state/client-state/client-state.service';

import { TileComponent } from '../../../tile/tile.component';
import { AddressPipe } from '../../../util/address.pipe';
import { ClientNamePipe } from '../../../util/client-name.pipe';

@Component({
  selector: 'app-client-details-preview',
  standalone: true,
  imports: [
    MatTabsModule,
    RktTagEnterpriseModule,
    TileComponent,
    ClientNamePipe,
    AddressPipe,
    MatDivider,
  ],

  templateUrl: './client-details-preview.component.html',
  styleUrl: './client-details-preview.component.scss',
})
export class ClientDetailsPreviewComponent {
  clientStateService = inject(ClientStateService, { optional: true });
  clients = computed(() => this.clientStateService?.stateValues() ?? []);

  readonly isLoading = computed(() => this.clientStateService?.state()?.fetching ?? false);
}
