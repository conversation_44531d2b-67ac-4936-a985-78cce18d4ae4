<h2 class="rkt-Tracked-12">Client(s) Info</h2>

<div class="tile-section">
  @for (client of clients(); track client) {
    @if (client.isPrimaryBorrower) {
      <app-tile
        [isLoading]="isLoading()"
        [icon]="client.military?.hasMilitaryService ? 'military-outlined' : ''"
        [tooltip]="client.military?.hasMilitaryService ? 'Veteran' : ''"
        label="Primary Client"
      >
        <strong>{{ client | clientName }}</strong>
      </app-tile>
      <app-tile [isLoading]="isLoading()" label="Marital Status">
        <strong>{{ client.personalInformation?.maritalStatus ?? '--' }}</strong>
      </app-tile>
      <app-tile [isLoading]="isLoading()" label="Current Address">
        <strong>{{
          client.residenceInformation?.currentResidence?.address
            ? (client.residenceInformation?.currentResidence?.address | address)
            : '--'
        }}</strong>
      </app-tile>
    }
  }
  @if (clients().length > 1) {
    <mat-divider class="rkt-HorizontalDivider" />
  }
  @for (client of clients(); track client; let i = $index) {
    @if (!client.isPrimaryBorrower) {
      <app-tile
        [isLoading]="isLoading()"
        [icon]="client.military?.hasMilitaryService ? 'military-outlined' : ''"
        [tooltip]="client.military?.hasMilitaryService ? 'Veteran' : ''"
        label="Co-Client {{ i }}"
      >
        <strong>{{ client | clientName }}</strong>
      </app-tile>
    }
  }
</div>
