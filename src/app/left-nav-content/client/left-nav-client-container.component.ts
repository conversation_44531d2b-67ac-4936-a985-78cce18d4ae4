import { Component } from '@angular/core';
import { LeftNavContainerComponent } from '../shared/left-nav-container.component';
import { EditClientBaseComponent } from './edit-client-base/edit-client-base.component';
import { ClientDetailsPreviewComponent } from './preview/client-details-preview.component';

@Component({
  selector: 'app-left-nav-client-container',
  standalone: true,
  imports: [ClientDetailsPreviewComponent, EditClientBaseComponent],
  templateUrl: './left-nav-client-container.component.html',
  styleUrl: './left-nav-client-container.component.scss',
})
export class LeftNavClientContainerComponent extends LeftNavContainerComponent {
  selectedTabIndex = 0;

  editClickHandler(index: number) {
    this.selectedTabIndex = index;
    this.setEditState();
  }
}
