import { CurrencyPipe } from '@angular/common';
import { Component, DestroyRef, computed, inject } from '@angular/core';
import { takeUntilDestroyed, toSignal } from '@angular/core/rxjs-interop';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { RktTagEnterpriseModule } from '@rocketcentral/rocket-design-system-enterprise-angular';
import { map, startWith } from 'rxjs';
import { LeftPanelOverlayService } from '../../../[containers]/left-panel/left-panel-overlay.service';
import { SaveStatusComponent } from '../../../application-island/save-status/save-status.component';
import { AssetPilotService } from '../../../assets/asset-pilot.service';
import { AssetsFormDisplayComponent } from '../../../assets/assets-form-display/assets-form-display.component';
import { AssetsSkeletonComponent } from '../../../assets/assets-skeleton/assets-skeleton.component';
import { CreateAssetButtonComponent } from '../../../assets/create-asset-button/create-asset-button.component';
import { EarnestMoneyDepositComponent } from '../../../assets/earnest-money-deposit/earnest-money-deposit.component';
import { ResetLumpSumService } from '../../../assets/reset-lump-sum.service.component';
import { SchwabAssetsImportComponent } from '../../../assets/schwab-assets-import/schwab-assets-import.component';
import { FormattedNumberInputComponent } from '../../../question-input/formatted-number-input/formatted-number-input.component';
import { ActiveActionService } from '../../../services/active-action.service';
import { EntityStateName } from '../../../services/entity-state/abstract-entity-state.service';
import { AssetFormService } from '../../../services/entity-state/asset-state/asset-form.service';
import { AssetStateService } from '../../../services/entity-state/asset-state/asset-state.service';
import { AssetUpdateHandlerService } from '../../../services/entity-state/asset-state/asset-update-handler.service';
import { ProductStateService } from '../../../services/entity-state/product-state/product-state.service';
import { LoanIdService } from '../../../services/loan-id/loan-id.service';
import { SchwabService } from '../../../services/schwab/schwab.service';
import { getAssetQualifierUrl } from '../../../util/get-url';
import { AppLeftNavErrorsComponent } from '../../shared/form-errors/left-nav-errors.component';

@Component({
  selector: 'app-assets-details',
  standalone: true,
  imports: [
    AssetsFormDisplayComponent,
    MatCardModule,
    EarnestMoneyDepositComponent,
    RktTagEnterpriseModule,
    MatIconModule,
    MatButtonModule,
    CreateAssetButtonComponent,
    FormattedNumberInputComponent,
    ReactiveFormsModule,
    SchwabAssetsImportComponent,
    AssetsSkeletonComponent,
    AppLeftNavErrorsComponent,
    SaveStatusComponent,
    CurrencyPipe,
  ],
  templateUrl: './assets-details.component.html',
  styleUrl: './assets-details.component.scss',
})
export class AssetsDetailsComponent {
  private readonly schwabService = inject(SchwabService);
  private readonly assetPilotService = inject(AssetPilotService);
  private readonly activeActionService = inject(ActiveActionService);
  readonly assetStateService = inject(AssetStateService);
  readonly assetUpdateHandler = inject(AssetUpdateHandlerService);
  readonly assetsFormService = inject(AssetFormService);
  readonly loanIdService = inject(LoanIdService);
  readonly resetLumpSumService = inject(ResetLumpSumService);
  readonly leftPanelOverlayService = inject(LeftPanelOverlayService);

  private readonly productStateService = inject(ProductStateService);

  private destroyRef = inject(DestroyRef);

  readonly entityName = [EntityStateName.Assets];

  earnestMoneyFormGroup = this.assetsFormService.emdForm;
  lumpSumFormGroup = this.assetsFormService.lumpSumForm;
  lumpSumAssetValueControl = this.lumpSumFormGroup.get('assetValue') as FormControl;

  isSchwab = toSignal(this.schwabService.isSchwab$);
  isInAssetsPilot = toSignal(this.assetPilotService.isInAssetsPilot$);

  readonly totalQualifyingAssets = computed(() => {
    const { totalQualifiedAssetAmount = 0, totalQualifiedGiftAmount = 0 } =
      this.productStateService.state()?.data?.qualificationDetails?.qualifiedAmounts ?? {};
    return totalQualifiedAssetAmount + totalQualifiedGiftAmount;
  });

  readonly loanId = computed(() => this.loanIdService.loanId());
  isEmdDocumented = computed(
    () => !this.assetStateService.earnestMoneyDeposit()?.isClientEstimated,
  );

  areCreditMilestoneComplete = computed(
    () => this.activeActionService.creditItemsToComplete() === 0,
  );

  isFetching = computed(() => {
    const { data, fetching } = this.assetStateService.state() ?? {};
    return (fetching && !data) ?? false;
  });

  hasUndocumentedEmd$ = this.earnestMoneyFormGroup.valueChanges.pipe(
    startWith(this.earnestMoneyFormGroup),
    map(() => !this.earnestMoneyFormGroup.controls.noEmd.value && !this.isEmdDocumented()),
    takeUntilDestroyed(this.destroyRef),
  )
  hasDocumentedEmd$ = this.earnestMoneyFormGroup.valueChanges.pipe(
    startWith(this.earnestMoneyFormGroup),
    map(() => !this.earnestMoneyFormGroup.controls.noEmd.value && this.isEmdDocumented()),
    takeUntilDestroyed(this.destroyRef),
  )

  displayLumpSumForm = computed(
    () => !this.assetsFormService.entityValues().length && this.isInAssetsPilot(),
  );

  nonBucketedAsset$ = this.assetsFormService.formMapChanges$.pipe(
    map((assets) => new Map([...assets].filter(([, v]) => v.value.isClientEstimated == null))),
    takeUntilDestroyed(this.destroyRef),
  );

  clientEstimatedAssets$ = this.assetsFormService.formMapChanges$.pipe(
    map((assets) => new Map([...assets].filter(([, v]) => v.value.isClientEstimated === true))),
    takeUntilDestroyed(this.destroyRef),
  );

  documentedAssets$ = this.assetsFormService.formMapChanges$.pipe(
    map((assets) => new Map([...assets].filter(([, v]) => v.value.isClientEstimated === false))),
    takeUntilDestroyed(this.destroyRef),
  );

  nonBucketedAsset = toSignal(this.nonBucketedAsset$);
  clientEstimatedAssets = toSignal(this.clientEstimatedAssets$);
  documentedAssets = toSignal(this.documentedAssets$);
  hasDocumentedEmd = toSignal(this.hasDocumentedEmd$);
  hasUndocumentedEmd = toSignal(this.hasUndocumentedEmd$);

  navigateToAQ() {
    if (this.loanId()) {
      window.location.href = getAssetQualifierUrl(this.loanId()!);
    }
  }
}
