<div class="container">
  <header class="items-center justify-between">
    <div class="flex gap-2">
      <span class="rkt-Label-16 rkt-FontWeight--500">Assets</span>
      <rkt-tag-enterprise
        >Total Qualifying Assets:
        <b>{{ totalQualifyingAssets() | currency }}</b></rkt-tag-enterprise
      >
    </div>
    <div class="header-container">
      <app-save-status
        [updateHandlers]="[assetUpdateHandler]"
        [stateServices]="[assetStateService]"
      />

      <button
        mat-icon-button
        class="rkt-ButtonIcon"
        aria-label="Close"
        (click)="leftPanelOverlayService.hidePopout()"
      >
        <mat-icon class="rkt-Icon">close</mat-icon>
      </button>
    </div>
  </header>
  <div class="body flex-1">
    @if (!isFetching()) {
      <app-left-nav-errors [entityName]="entityName" />
      @if (isSchwab() && areCreditMilestoneComplete()) {
        <app-schwab-assets-import />
      }
      @if (nonBucketedAsset()?.size) {
        <mat-card class="rkt-Card rkt-Card--enterprise">
          <app-assets-form-display [clientFormMap]="nonBucketedAsset()!" />
        </mat-card>
      }
      @if (clientEstimatedAssets()?.size || hasUndocumentedEmd() || displayLumpSumForm()) {
        <span class="rkt-Label-14">Client Stated</span>
        <mat-card class="rkt-Card rkt-Card--enterprise">
          @if (displayLumpSumForm()) {
            <div class="lump-sum-container">
              <span class="rkt-Label-16 rkt-FontWeight--700">Lump Sum</span>
              <div class="lump-sum-grid">
                <ng-container [formGroup]="lumpSumFormGroup">
                  <app-formatted-number-input
                    [control]="lumpSumAssetValueControl"
                    [allowNegative]="false"
                    prefix="$"
                    label="Lump Sum Amount"
                    subscriptSizing="dynamic"
                  />
                </ng-container>
              </div>
            </div>
          }
          @if (hasUndocumentedEmd()) {
            <app-earnest-money-deposit [isInPricing]="true" />
          }
          <app-assets-form-display [clientFormMap]="clientEstimatedAssets()!" />
        </mat-card>
      }
      @if (documentedAssets()?.size || hasDocumentedEmd()) {
        <span class="rkt-Label-14">Documented</span>
        <mat-card class="rkt-Card rkt-Card--enterprise">
          @if (hasDocumentedEmd()) {
            <app-earnest-money-deposit [isInPricing]="true" />
          }
          <app-assets-form-display [clientFormMap]="documentedAssets()!" />
        </mat-card>
      }
    } @else {
      <app-assets-skeleton />
    }
  </div>

  <footer>
    <button
      [disabled]="isFetching()"
      mat-button
      class="rkt-Button rkt-Button--tertiary rkt-Button--has-icon"
      [class.rkt-Button--is-disabled]="isFetching()"
      color="accent"
      (click)="navigateToAQ()"
    >
      <mat-icon class="rkt-Icon" color="secondary" svgIcon="open_in_new-outlined" />
      Asset Qualifier
    </button>
    <app-create-asset-button
      [buttonLabel]="
        assetsFormService.entityValues().length ? 'Add Additional Assets' : 'Add Assets'
      "
      (createTypeSelected)="resetLumpSumService.handleLumpSumOnAddAsset($event)"
      icon="add-outlined"
      color="secondary"
      [isStroked]="true"
      [isLarge]="false"
    />
  </footer>
</div>
