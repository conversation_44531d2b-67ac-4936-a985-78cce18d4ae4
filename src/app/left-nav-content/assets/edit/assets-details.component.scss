@use '../../../assets/base-asset/base-asset.component.scss';
@use '../../shared/styles/left-nav-details.component.scss';

header {
  padding-right: 1rem;
}

.container {
  display: flex;
  flex-direction: column;
  height: var(--left-panel-max-height);
}

mat-card {
  border: 1px solid var(--rlxp-gray-200, #d3d3d3);
}

.lump-sum-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.lump-sum-grid {
  @include base-asset.grid-container;
}

.header-container {
  display: flex;
  align-items: center;
  gap: 16px;
}
