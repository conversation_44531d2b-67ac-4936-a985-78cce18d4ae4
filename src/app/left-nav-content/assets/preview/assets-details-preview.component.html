<h2 class="rkt-Tracked-12">Assets</h2>

<div class="tile-section">
  <app-tile label="Total Qualifying Assets" [isLoading]="isAssetStateServiceLoading()">
    <strong>{{ totalQualifyingAssets() | currency }}</strong>
  </app-tile>
  <app-tile label="Earnest Money Deposit" [isLoading]="isAssetStateServiceLoading()">
    <strong>{{ earnestMoneyDeposit() }}</strong>
  </app-tile>
  @if (lumpSumExists()) {
    <app-tile label="Lump Sum" [isLoading]="isAssetStateServiceLoading()">
      <strong>{{ lumpSum() }}</strong>
    </app-tile>
  } @else {
    <app-tile label="Total Gifts" [isLoading]="isAssetStateServiceLoading()">
      <strong>{{ totalGifts() }}</strong>
    </app-tile>
    <app-tile label="Total Grants" [isLoading]="isAssetStateServiceLoading()">
      <strong>{{ totalGrants() }}</strong>
    </app-tile>
    <app-tile label="Total Bridge Loan" [isLoading]="isAssetStateServiceLoading()">
      <strong>{{ totalBridgeLoans() }}</strong>
    </app-tile>
    <app-tile
      label="Other Assets (Checking, Savings
    401K, Mutual Fund, etc.)"
      [isLoading]="isAssetStateServiceLoading()"
    >
      <strong>{{ totalOtherAssets() }}</strong>
    </app-tile>
  }
  <app-tile label="Net Proceeds" [isLoading]="isAssetStateServiceLoading()">
    <strong>{{ netProceeds() }}</strong>
  </app-tile>
</div>
