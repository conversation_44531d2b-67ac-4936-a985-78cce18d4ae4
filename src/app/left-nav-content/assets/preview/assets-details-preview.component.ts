import { CurrencyPipe, formatCurrency } from '@angular/common';
import { Component, computed, inject } from '@angular/core';
import { MatTabsModule } from '@angular/material/tabs';
import { RktTagEnterpriseModule } from '@rocketcentral/rocket-design-system-enterprise-angular';
import { AssetStateService } from '../../../services/entity-state/asset-state/asset-state.service';
import { ProductStateService } from '../../../services/entity-state/product-state/product-state.service';
import { TileComponent } from '../../../tile/tile.component';

@Component({
  selector: 'app-assets-details-preview',
  standalone: true,
  imports: [MatTabsModule, RktTagEnterpriseModule, TileComponent, CurrencyPipe],
  templateUrl: './assets-details-preview.component.html',
  styleUrl: './assets-details-preview.component.scss',
})
export class AssetsDetailsPreviewComponent {
  readonly assetStateService = inject(AssetStateService);
  private readonly productStateService = inject(ProductStateService);

  readonly isAssetStateServiceLoading = computed(
    () => this.assetStateService.state()?.fetching ?? false,
  );

  readonly lumpSumExists = computed(
    () =>
      !!this.assetStateService.lumpSum() &&
      this.assetStateService.getAssetTotalFromTypes([this.assetStateService.lumpSum()!]) > 0,
  );

  readonly totalQualifyingAssets = computed(() => {
    const { totalQualifiedAssetAmount = 0, totalQualifiedGiftAmount = 0 } =
      this.productStateService.state()?.data?.qualificationDetails?.qualifiedAmounts ?? {};
    return totalQualifiedAssetAmount + totalQualifiedGiftAmount;
  });

  readonly lumpSum = computed(() =>
    !this.assetStateService.lumpSum()
      ? '$0'
      : formatCurrency(
          this.assetStateService.getAssetTotalFromTypes([this.assetStateService.lumpSum()!]),
          'en-US',
          '$',
          'USD',
        ),
  );

  readonly earnestMoneyDeposit = computed(() =>
    !this.assetStateService.earnestMoneyDeposit()
      ? '$0'
      : formatCurrency(
          this.assetStateService.getAssetTotalFromTypes([
            this.assetStateService.earnestMoneyDeposit()!,
          ]),
          'en-US',
          '$',
          'USD',
        ),
  );

  readonly netProceeds = computed(() =>
    this.assetStateService.netProceeds()
      ? '$0'
      : formatCurrency(
          this.assetStateService.getAssetTotalFromTypes(this.assetStateService.netProceeds()!),
          'en-US',
          '$',
          'USD',
        ),
  );

  readonly totalGifts = computed(() =>
    !this.assetStateService.gifts() ||
    this.assetStateService.getAssetTotalFromTypes(this.assetStateService.gifts()!) === 0
      ? '$0'
      : formatCurrency(
          this.assetStateService.getAssetTotalFromTypes(this.assetStateService.gifts()!),
          'en-US',
          '$',
          'USD',
        ),
  );

  readonly totalGrants = computed(() =>
    !this.assetStateService.grants()
      ? '$0'
      : formatCurrency(
          this.assetStateService.getAssetTotalFromTypes([this.assetStateService.grants()!]),
          'en-US',
          '$',
          'USD',
        ),
  );

  readonly totalBridgeLoans = computed(() =>
    !this.assetStateService.bridgeLoans()
      ? '$0'
      : formatCurrency(
          this.assetStateService.getAssetTotalFromTypes([this.assetStateService.bridgeLoans()!]),
          'en-US',
          '$',
          'USD',
        ),
  );

  readonly totalOtherAssets = computed(() =>
    !this.assetStateService.otherAssets() ||
    this.assetStateService.getAssetTotalFromTypes(this.assetStateService.otherAssets()!) === 0
      ? '$0'
      : formatCurrency(
          this.assetStateService.getAssetTotalFromTypes(this.assetStateService.otherAssets()!),
          'en-US',
          '$',
          'USD',
        ),
  );
}
