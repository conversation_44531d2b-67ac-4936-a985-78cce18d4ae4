import { Component, computed, inject } from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import { MatButtonModule } from '@angular/material/button';
import { MatIcon } from '@angular/material/icon';
import { map } from 'rxjs';
import { environment } from '../../../../../environments/environment';
import { LiabilityComponent } from '../../../../reo/liability/liability.component';
import { EntityStateName } from '../../../../services/entity-state/abstract-entity-state.service';
import { AssetFormService } from '../../../../services/entity-state/asset-state/asset-form.service';
import {
  LiabilityFormRef,
  LiabilityFormService,
} from '../../../../services/entity-state/liability-state/liability-form.service';
import { LiabilityStateService } from '../../../../services/entity-state/liability-state/liability-state.service';
import { LoanEditingState } from '../../../../services/entity-state/loan-state/loan-editing-state.service';
import { OwnedPropertyFormService } from '../../../../services/entity-state/owned-property-state/owned-property-form.service';
import { OwnedPropertyLienAssociationWarningService } from '../../../../services/entity-state/owned-property-state/owned-property-lien-association-warning.service';
import { OwnedPropertyStateService } from '../../../../services/entity-state/owned-property-state/owned-property-state.service';
import {
  FormNavSectionService,
  FormSection,
} from '../../../../services/form-nav/form-nav-section.service';
import { LoanIdService } from '../../../../services/loan-id/loan-id.service';
import { AppLeftNavErrorsComponent } from '../../../shared/form-errors/left-nav-errors.component';
import { CurrentlySelectedPropertyInfoService } from './currently-selected-property-info.service';
import { OwnedPropertySectionDetailsComponent } from './owned-property-section-details/owned-property-section-details.component';
import { OwnedPropertyTileDisplayComponent } from './owned-property-tile-display/owned-property-tile-display.component.';

@Component({
  selector: 'app-reo-details',
  standalone: true,
  imports: [
    MatButtonModule,
    MatIcon,
    OwnedPropertySectionDetailsComponent,
    AppLeftNavErrorsComponent,
    LiabilityComponent,
    OwnedPropertyTileDisplayComponent,
  ],
  templateUrl: './reo-details.component.html',
  styleUrl: './reo-details.component.scss',
})
export class ReoDetailsComponent {
  readonly liabilityFormRef = inject(LiabilityFormRef);
  readonly liabilityFormService = inject(LiabilityFormService);
  readonly ownedPropertyFormService = inject(OwnedPropertyFormService);
  readonly liabilityStateService = inject(LiabilityStateService);
  readonly ownedPropertyStateService = inject(OwnedPropertyStateService);
  readonly formNavSectionService = inject(FormNavSectionService);
  readonly loanIdService = inject(LoanIdService);
  readonly currentPropInfoSerivce = inject(CurrentlySelectedPropertyInfoService);
  readonly ownedPropWarningService = inject(OwnedPropertyLienAssociationWarningService);
  readonly isDisabled = inject(LoanEditingState).isLoanEditingDisabled;
  readonly estimatedNetProceedsForm = inject(AssetFormService).estimatedNetProceedsForm;

  readonly FormSection = FormSection;
  readonly entityName = [
    EntityStateName.Liability,
    EntityStateName.OwnedProperty,
    EntityStateName.Assets,
  ];

  ownedPropertyMapEntries = computed(
    () => this.ownedPropertyFormService.ownedPropertyMapEntries() ?? [],
  );

  unassociatedLiabilityForms = toSignal(
    this.liabilityFormService.unassociatedLiens$.pipe(
      map((liabilities) => {
        return liabilities.filter(
          ([, liabilityFormGroup]) => liabilityFormGroup?.getRawValue()?.ownedPropertyId == null,
        );
      }),
    ),
  );

  ownedProperties = this.ownedPropertyStateService.stateValues;
  ownedPropertiesEntityValues = this.ownedPropertyFormService.entityValues;

  errorLookupFormIds = computed(() => {
    const formIdList: string[] = [];
    if (this.currentPropInfoSerivce.selectedOwnedPropertyFormId()) {
      formIdList.push(this.currentPropInfoSerivce.selectedOwnedPropertyFormId()!);
    }
    formIdList.push(...(this.currentPropInfoSerivce.associatedLienFormIds() ?? []));
    return formIdList;
  });

  isFetching = computed(() => {
    const { data, fetching } = this.liabilityStateService.state() ?? {};
    return fetching && !data;
  });

  onAddProperty() {
    this.ownedPropertyFormService.addOwnedProperty();
    this.currentPropInfoSerivce.updateSelectedOwnedProperty(
      this.ownedPropertyFormService.entityValues().length - 1,
    );
  }

  openInPQ() {
    window.open(
      `${environment.propertyQualifierUrl}/${this.loanIdService.loanId()}/property`,
      '_blank',
    );
  }
}
