import { CommonModule } from '@angular/common';
import {
  ChangeDetectionStrategy,
  Component,
  ViewContainerRef,
  computed,
  inject,
} from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatDialog } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { OwnedPropertyCategorizationService } from '../../../../../declarations-demographics/services/owned-property-categorization.service';
import { OwnedPropertyFormService } from '../../../../../services/entity-state/owned-property-state/owned-property-form.service';
import { TileComponent } from '../../../../../tile/tile.component';
import { ReoPopOutComponent } from '../../reo-pop-out/reo-pop-out.component';
import { CurrentlySelectedPropertyInfoService } from '../currently-selected-property-info.service';

@Component({
  selector: 'app-owned-property-tile-display',
  standalone: true,
  imports: [CommonModule, TileComponent, MatIconModule, MatButtonModule],
  templateUrl: './owned-property-tile-display.component.html',
  styleUrl: './owned-property-tile-display.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class OwnedPropertyTileDisplayComponent {
  private readonly ownedPropertyCategorizationService = inject(OwnedPropertyCategorizationService);
  readonly currentPropInfoSerivce = inject(CurrentlySelectedPropertyInfoService);
  private readonly dialog = inject(MatDialog);
  private readonly viewContainerRef = inject(ViewContainerRef);
  private readonly ownedPropertyFormService = inject(OwnedPropertyFormService);

  ownedProperties = computed(() => this.ownedPropertyCategorizationService.ownedPropInfo());

  private readonly ownedPropertyForms = computed(
    () => this.ownedPropertyFormService.ownedPropertyMapEntries() ?? [],
  );

  selectedOwnedProperty = computed(() => this.currentPropInfoSerivce.selectedOwnedPropertyFormId());

  onDeleteClick(index: number) {
    this.currentPropInfoSerivce.updateSelectedOwnedProperty(index);
    const ownedProperty =
      this.ownedPropertyForms()[this.currentPropInfoSerivce.ownedPropertyIndex()][1];
    const ownedPropKey =
      this.ownedPropertyForms()[this.currentPropInfoSerivce.ownedPropertyIndex()][0];
    const dialogRef = this.dialog.open(ReoPopOutComponent, {
      data: { ownedProperty },
      panelClass: 'rkt-Dialog',
      backdropClass: 'rkt-Backdrop',
      viewContainerRef: this.viewContainerRef,
    });

    dialogRef.afterClosed().subscribe({
      next: (result) => {
        if (result === 'submitted') {
          if (this.currentPropInfoSerivce.ownedPropertyIndex() !== 0) {
            this.currentPropInfoSerivce.updateSelectedOwnedProperty(index - 1);
          }
          this.ownedPropertyFormService.deleteOwnedProperty(ownedPropKey);
        }
      },
    });
  }
}
