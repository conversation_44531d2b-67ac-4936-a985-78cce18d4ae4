<nav class="owned-property-tiles">
  @for (
    ownedProperty of ownedProperties();
    track ownedProperty.ownedPropertyFormId;
    let i = $index
  ) {
    <app-tile
      [isLoading]="false"
      label="Property {{ i + 1 }}"
      [icon]="ownedProperty.presentation.icon"
      [isTileInteractive]="true"
      [isSelected]="ownedProperty.ownedPropertyFormId === selectedOwnedProperty()"
      [variant]="ownedProperty.presentation.variant"
      (click)="currentPropInfoSerivce.updateSelectedOwnedProperty(i)"
    >
      <strong>{{ ownedProperty.ownedPropertyAddress }}</strong>
      @if (!ownedProperty.isCurrentResidence && !ownedProperty.isSubjectProperty) {
        <button custom-button (click)="onDeleteClick(i)">
          <mat-icon class="rkt-Icon" svgIcon="delete-outlined" />
        </button>
      }
    </app-tile>
  }
</nav>
