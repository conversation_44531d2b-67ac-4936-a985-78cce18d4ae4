<mat-accordion>
  <mat-expansion-panel #panel class="rkt-AccordionPanel rkt-AccordionPanel--enterprise">
    <mat-expansion-panel-header>
      <mat-panel-title class="rkt-AccordionPanel__header-title center-aligned gap-2">
        <span class="rkt-Label-14 rkt-FontWeight--700">{{ creditorName() }}</span>
        <span class="rkt-Label-14 rkt-FontWeight--400"
          >{{ unpaidBalance() | currency }} Balance, {{ monthlyPayment() | currency }}/mo</span
        >
        <rkt-tag-enterprise variant="success" iconPosition="right" iconName="link-outlined">
          {{ liabilityType() }}
        </rkt-tag-enterprise>
      </mat-panel-title>
    </mat-expansion-panel-header>

    <div [formGroup]="lienForm()" class="form-grid">
      <app-client-select
        label="Debt Owner(s)"
        [control]="lienForm().controls.clientIds"
        class="col-span-3"
      />
      <mat-form-field class="rkt-FormField col-span-3">
        <mat-label>Lien Type</mat-label>
        <mat-select class="rkt-Input" [formControl]="lienForm().controls.liabilityType">
          @for (option of lienTypeOptions(); track option.value) {
            <mat-option [value]="option.value">{{ option.display }}</mat-option>
          }
        </mat-select>
      </mat-form-field>
      <mat-form-field class="rkt-FormField col-span-3">
        <mat-label>Lien Position</mat-label>
        <mat-select
          class="rkt-Input"
          [formControl]="lienForm().controls.lienInformation.controls.lienPosition"
          (selectionChange)="handleLienPositionChange($event)"
          (focus)="onLienPositionFocus()"
        >
          @for (option of lienPositionOptions(); track option) {
            <mat-option [value]="option.value">{{ option.display }}</mat-option>
          }
        </mat-select>
      </mat-form-field>
      <mat-form-field class="rkt-FormField col-span-3">
        <mat-label>Creditor</mat-label>
        <input matInput class="rkt-Input" formControlName="creditorName" />
      </mat-form-field>
      <mat-form-field class="rkt-FormField col-span-6">
        <mat-label>Account Number</mat-label>
        <input matInput class="rkt-Input" formControlName="accountId" />
      </mat-form-field>
      <mat-slide-toggle class="rkt-SlideToggle col-span-3" color="accent">
        <span class="rkt-SlideToggle__label rkt-Spacing--ml8">Lien on Credit Report</span>
      </mat-slide-toggle>
      <mat-divider class="rkt-HorizontalDivider col-span-9" />
      <p class="rkt-Label-14 rkt-FontWeight--500 col-span-10">Payment Information</p>
      <app-formatted-number-input
        class="col-span-3"
        label="Monthly Lien Payment"
        prefix="$"
        [control]="lienForm().controls.monthlyPaymentAmount"
        [allowNegative]="false"
      />
      <mat-slide-toggle
        class="rkt-SlideToggle col-span-3"
        color="accent"
        formControlName="monthlyPaymentIncludesTaxesAndInsurance"
      >
        <span class="rkt-SlideToggle__label rkt-Spacing--ml8">Includes Taxes and Insurance</span>
      </mat-slide-toggle>
      <mat-slide-toggle
        class="rkt-SlideToggle col-span-3"
        color="accent"
        #mortgageInsuranceToggle
        [checked]="mortgageInsuranceAmount()"
        (change)="onToggleChange($event)"
        [disabled]="lienForm().disabled"
      >
        <span class="rkt-SlideToggle__label rkt-Spacing--ml8">Includes Mortgage Insurance</span>
      </mat-slide-toggle>
      @if (mortgageInsuranceToggle.checked) {
        <app-formatted-number-input
          class="col-span-3"
          label="Monthly Mortgage Insurance Amount"
          prefix="$"
          [control]="lienForm().controls.mortgageInsuranceMonthlyPaymentAmount"
          [allowNegative]="false"
        />
      }
      <button
        mat-button
        color="accent"
        class="rkt-Button rkt-Button--tertiary rkt-Button--has-icon-left col-span-4"
        (click)="openInPQ()"
      >
        <mat-icon class="rkt-Icon" svgIcon="open_in_new-outlined" />
        Go to Property Qualifier to Exclude
      </button>
      <mat-divider class="rkt-HorizontalDivider col-span-9" />
      <p class="rkt-Label-14 rkt-FontWeight--500 col-span-10">Current Mortgage</p>
      <app-formatted-number-input
        class="col-span-3"
        label="Unpaid Balance"
        prefix="$"
        [control]="lienForm().controls.unpaidBalance"
        [allowNegative]="false"
      />
      <app-formatted-number-input
        class="col-span-3"
        label="Past Due Balance"
        prefix="$"
        [control]="lienForm().controls.pastDueAmount"
        [allowNegative]="false"
      />
      <mat-form-field class="rkt-FormField col-span-3">
        <mat-label>Mortgage Type</mat-label>
        <mat-select formControlName="mortgageType" class="rkt-Input">
          <mat-option [value]="null">None</mat-option>
          @for (option of mortgageTypeOptions; track option) {
            <mat-option [value]="option.value">{{ option.display }}</mat-option>
          }
        </mat-select>
      </mat-form-field>
      <app-formatted-date-input
        class="col-span-4"
        label="Original Closing Date"
        [control]="lienForm().controls.accountOpenedDate"
      />
      <app-formatted-number-input
        class="col-span-4"
        label="Original Mortgage Amount"
        prefix="$"
        [control]="lienForm().controls.highBalanceAmount"
        [allowNegative]="false"
      />
    </div>
    <div class="flex justify-end gap-2">
      <button
        mat-button
        cdkOverlayOrigin
        #trigger="cdkOverlayOrigin"
        (click)="menu.toggleMenu()"
        [class.rkt-Button--is-disabled]="isDisabled()"
        [disabled]="isDisabled()"
        class="rkt-Button rkt-Button--tertiary rkt-Button--has-icon"
      >
        <mat-icon color="primary">move_up</mat-icon>
        Reassociate Lien
      </button>

      @if (!lienForm().getRawValue().isOnCreditReport) {
        <button
          mat-button
          class="rkt-Button rkt-Button--tertiary rkt-Button--has-icon"
          (click)="onDeleteClick()"
          [class.rkt-Button--is-disabled]="isDisabled()"
          [disabled]="isDisabled()"
        >
          <mat-icon color="primary" svgIcon="delete-outlined"></mat-icon>
          Delete Lien
        </button>
      }
    </div>
  </mat-expansion-panel>
</mat-accordion>
<app-lien-menu
  #menu
  [overlayOrigin]="trigger"
  [ownedProperties]="ownedProperties()"
  (associate)="onAssociate($event)"
  (convert)="onConvert()"
/>
