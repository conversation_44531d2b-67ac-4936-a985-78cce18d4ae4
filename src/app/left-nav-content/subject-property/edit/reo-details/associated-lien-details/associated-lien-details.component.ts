import { OverlayModule } from '@angular/cdk/overlay';
import { <PERSON><PERSON>rencyPipe } from '@angular/common';
import { Component, computed, inject, input, signal } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatDividerModule } from '@angular/material/divider';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectChange, MatSelectModule } from '@angular/material/select';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { LiabilityType } from '@rocket-logic/rl-xp-bff-models';
import { RktTagEnterpriseModule } from '@rocketcentral/rocket-design-system-enterprise-angular';
import { environment } from '../../../../../../environments/environment';
import { ClientSelectComponent } from '../../../../../assets/client-select/client-select.component';
import { InputSectionDirective } from '../../../../../form-nav/nav-section/input-section.directive';
import { FormattedDateInputComponent } from '../../../../../question-input/formatted-date-input/formatted-date-input.component';
import { FormattedNumberInputComponent } from '../../../../../question-input/formatted-number-input/formatted-number-input.component';
import { BaseAssociatedLienComponent } from '../../../../../reo/associated-lien/base-associated-lien.component';
import { LienMenuComponent } from '../../../../../reo/lien-menu/lien-menu.component';
import { AllLiabilityGroup } from '../../../../../services/entity-state/liability-state/form-types';
import { LoanIdService } from '../../../../../services/loan-id/loan-id.service';
import { formatLienType } from '../../../../../util/formaters/format-lien-type';

@Component({
  selector: 'app-associated-lien-details',
  standalone: true,
  imports: [
    MatExpansionModule,
    CurrencyPipe,
    RktTagEnterpriseModule,
    MatFormFieldModule,
    ReactiveFormsModule,
    MatInputModule,
    FormattedNumberInputComponent,
    MatSlideToggleModule,
    MatSelectModule,
    MatIconModule,
    MatButtonModule,
    LienMenuComponent,
    OverlayModule,
    ClientSelectComponent,
    InputSectionDirective,
    MatDividerModule,
    FormattedDateInputComponent,
    ReactiveFormsModule,
  ],
  templateUrl: './associated-lien-details.component.html',
  styleUrl: './associated-lien-details.component.scss',
})
export class AssociatedLienDetailsComponent extends BaseAssociatedLienComponent {
  readonly associatedLienForms = input.required<[string, AllLiabilityGroup][]>();
  readonly #oldLienPosition = signal<number | null | undefined>(null);
  readonly #loanIdService = inject(LoanIdService);

  openInPQ() {
    window.open(
      `${environment.propertyQualifierUrl}/${this.#loanIdService.loanId()}/reo`,
      '_blank',
    );
  }

  onLienPositionFocus() {
    this.#oldLienPosition.set(this.lienForm().controls.lienInformation.controls.lienPosition.value);
  }

  lienTypes = computed(() => {
    return [
      LiabilityType.MortgageLoan,
      LiabilityType.HomeEquityLineOfCredit,
      LiabilityType.LandContract,
      LiabilityType.MechanicsLien,
      LiabilityType.MonetaryJudgement,
      LiabilityType.PropertyAssessedCleanEnergy,
      LiabilityType.SolarPanelPayment,
      LiabilityType.TaxesOwed,
      LiabilityType.EquityBuyout,
    ];
  });

  lienTypeOptions = computed(() =>
    this.lienTypes().map((value) => ({
      value,
      display: formatLienType(value),
    })),
  );

  lienPositionOptions = computed(() => {
    const positions = this.associatedLienForms().length ?? 0;
    return Array(positions)
      .fill(0)
      .map((_, index) => ({
        value: index,
        display: `Lien Position ${index + 1}`,
      }));
  });

  handleLienPositionChange(event: MatSelectChange) {
    const newPosition = event.value;
    const oldPosition = this.#oldLienPosition();

    if (oldPosition == null) {
      return;
    }

    this.swapLienPositions(oldPosition, newPosition);
  }

  swapLienPositions(oldPosition: number, newPosition: number) {
    let swapped = false;

    this.associatedLienForms().forEach(([_, currForm]) => {
      if (
        newPosition === currForm.controls.lienInformation.controls.lienPosition.value &&
        currForm.controls.id.value !== this.lienForm().controls.id.value
      ) {
        currForm.controls.lienInformation.controls.lienPosition.markAsDirty();
        this.lienForm().controls.lienInformation.controls.lienPosition.markAsDirty();

        currForm.controls.lienInformation.controls.lienPosition.setValue(oldPosition);
        this.lienForm().controls.lienInformation.controls.lienPosition.setValue(newPosition);

        swapped = true;
      }
    });

    if (swapped) {
      this.#oldLienPosition.set(null);
    }
  }
}
