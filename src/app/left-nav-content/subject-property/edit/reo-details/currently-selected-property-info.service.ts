import { Injectable, computed, inject, signal } from '@angular/core';
import { toObservable, toSignal } from '@angular/core/rxjs-interop';
import { map, of, switchMap } from 'rxjs';
import { LiabilityFormService } from '../../../../services/entity-state/liability-state/liability-form.service';
import { OwnedPropertyFormService } from '../../../../services/entity-state/owned-property-state/owned-property-form.service';

@Injectable()
export class CurrentlySelectedPropertyInfoService {
  private ownedPropertyFormService = inject(OwnedPropertyFormService);
  readonly liabilityFormService = inject(LiabilityFormService);

  ownedPropertyIndex = signal(0);

  readonly selectedOwnedPropertyFormId = signal<string | undefined>(
    this.ownedPropertyFormService.ownedPropertyMapEntries()?.[this.ownedPropertyIndex()]?.[0],
  );

  readonly selectedOwnedPropertyId = signal<string | undefined>(
    this.ownedPropertyFormService.ownedPropertyMapEntries()?.[this.ownedPropertyIndex()]?.[1]?.value
      ?.id ?? undefined,
  );
  selectedOwnedPropertyId$ = toObservable(this.selectedOwnedPropertyId);

  associatedLienFormIds = toSignal(
    this.selectedOwnedPropertyId$.pipe(
      switchMap((id) => {
        return id
          ? this.liabilityFormService
              .getAssociatedLiens$(this.selectedOwnedPropertyId()!)
              .pipe(map((lienMapList) => lienMapList.map((lienMap) => lienMap[0])))
          : of([]);
      }),
    ),
  );

  associatedLienFormIds$ = toObservable(this.associatedLienFormIds);

  selectedOwnedPropForm = computed(() =>
    this.ownedPropertyFormService.entityFormMap().get(this.selectedOwnedPropertyFormId() ?? ''),
  );

  updateSelectedOwnedProperty(i: number) {
    this.ownedPropertyIndex.set(i);
    const mapEntries = this.ownedPropertyFormService.ownedPropertyMapEntries()!;

    this.selectedOwnedPropertyFormId.set(mapEntries[i]?.[0]);
    this.selectedOwnedPropertyId.set(mapEntries[i]?.[1]?.value?.id ?? undefined);
  }
}
