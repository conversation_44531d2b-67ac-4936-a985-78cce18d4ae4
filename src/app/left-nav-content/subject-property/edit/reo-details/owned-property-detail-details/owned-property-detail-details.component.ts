import { Component, computed, inject, input, output, viewChild } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatDividerModule } from '@angular/material/divider';
import { MatExpansionModule, MatExpansionPanel } from '@angular/material/expansion';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { OwnedProperty, OwnedPropertyDispositionStatus } from '@rocket-logic/rl-xp-bff-models';
import { RktAlertEnterpriseModule } from '@rocketcentral/rocket-design-system-enterprise-angular';
import { SelectFieldComponent } from '../../../../../_shared/components/select-field/select-field.component';
import { AddressComponent } from '../../../../../address/address.component';
import { ClientSelectComponent } from '../../../../../assets/client-select/client-select.component';
import { FormattedDateInputComponent } from '../../../../../question-input/formatted-date-input/formatted-date-input.component';
import { FormattedNumberInputComponent } from '../../../../../question-input/formatted-number-input/formatted-number-input.component';
import { AssetFormService } from '../../../../../services/entity-state/asset-state/asset-form.service';
import { AssetStateService } from '../../../../../services/entity-state/asset-state/asset-state.service';
import { LoanEditingState } from '../../../../../services/entity-state/loan-state/loan-editing-state.service';
import { OwnedPropertyGroup } from '../../../../../services/entity-state/owned-property-state/form-types';
import { OwnedPropertyFormService } from '../../../../../services/entity-state/owned-property-state/owned-property-form.service';
import { OwnedPropertyLienAssociationWarningService } from '../../../../../services/entity-state/owned-property-state/owned-property-lien-association-warning.service';
import { OwnedPropertyStateService } from '../../../../../services/entity-state/owned-property-state/owned-property-state.service';
import { AddressPipe } from '../../../../../util/address.pipe';
import { pascalCaseSplit } from '../../../../../util/formatting-helpers';
import { OCCUPANCY } from '../../../../../util/occupancy';
import { OWNED_PROPERTY } from '../../../../../util/owned-property-property-types';
import { toValueChangesSignal } from '../../../../../util/value-changes-signal';

@Component({
  selector: 'app-owned-property-detail-details',
  standalone: true,
  imports: [
    ReactiveFormsModule,
    MatExpansionModule,
    MatIconModule,
    AddressComponent,
    MatSlideToggleModule,
    MatFormFieldModule,
    MatSelectModule,
    MatInputModule,
    MatButtonModule,
    ClientSelectComponent,
    MatDividerModule,
    FormattedNumberInputComponent,
    AddressPipe,
    FormattedDateInputComponent,
    ReactiveFormsModule,
    FormsModule,
    SelectFieldComponent,
    RktAlertEnterpriseModule,
  ],
  templateUrl: './owned-property-detail-details.component.html',
  styleUrl: './owned-property-detail-details.component.scss',
})
export class OwnedPropertyDetailDetailsComponent {
  ownedPropertyFormService = inject(OwnedPropertyFormService);
  assetStateService = inject(AssetStateService);
  assetFormService = inject(AssetFormService);
  estimatedNetProceedsForm = this.assetFormService.estimatedNetProceedsForm;
  formGroup = input.required<OwnedPropertyGroup>();
  ownedProperty = input.required<OwnedProperty>();
  isDisabled = inject(LoanEditingState).isLoanEditingDisabled;
  ownedPropertyService = inject(OwnedPropertyStateService);
  ownedPropWarningService = inject(OwnedPropertyLienAssociationWarningService);
  lumpSumFormGroup = this.assetFormService.lumpSumForm;

  readonly OwnedPropertyDispositionStatus = OwnedPropertyDispositionStatus;

  readonly ownedPropertyDispositionStatusOptions = Object.values(
    OwnedPropertyDispositionStatus,
  ).map((value) => ({
    value,
    display: pascalCaseSplit(value),
  }));

  delete = output();

  expansionPanel = viewChild(MatExpansionPanel);
  addressForm = computed(() => this.formGroup().controls.address);

  readonly propertyTypeOptions = OWNED_PROPERTY;
  readonly occupancyTypeOptions = OCCUPANCY;

  dispositionStatus = toValueChangesSignal<OwnedPropertyDispositionStatus>(
    this.formGroup,
    'dispositionStatus',
  );

  hasHoa = computed(() => {
    const hoaPaymentExists = this.ownedProperty()?.hoaPayment !== undefined;
    return hoaPaymentExists;
  });

  netProceedsControl = this.estimatedNetProceedsForm.controls.estimatedNetProceeds;

  propertyTypeControl = computed(() => {
    return this.formGroup()?.controls.propertyType;
  });

  occupancyTypeControl = computed(() => {
    return this.formGroup()?.controls.occupancyType;
  });

  pendingSalesClosingDateControl = computed(() => {
    return this.formGroup()?.controls.pendingSaleClosingDate;
  });

  propertyTaxAmountControl = computed(() => {
    return this.formGroup()?.controls.taxPayment.controls.amount;
  });

  isForSaleByOwnerControl = computed(() => {
    return this.formGroup()?.controls.isForSaleByOwner;
  });

  estimatedPropertyValueControl = computed(() => {
    return this.formGroup()?.controls.estimatedPropertyValue;
  });

  estimatedPropertyStatusControl = computed(() => {
    return this.formGroup()?.controls.dispositionStatus;
  });

  hoaAmountControl = computed(() => {
    return this.formGroup()?.controls.hoaPayment.controls.amount;
  });

  hoaFrequencyControl = computed(() => {
    return this.formGroup()?.controls.hoaPayment.controls.frequency;
  });

  hoiAmountControl = computed(() => {
    return this.formGroup()?.controls.insurancePayment.controls.amount;
  });

  associationWarning = computed(() => this.ownedPropWarningService.warnings());

  ngOnInit(): void {
    if (!this.formGroup().getRawValue().id) {
      this.expansionPanel()?.open();
    }
  }
}
