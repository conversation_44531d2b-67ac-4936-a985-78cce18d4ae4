<mat-accordion class="reo-property">
  <mat-expansion-panel
    #panel
    class="rkt-AccordionPanel rkt-AccordionPanel--enterprise"
    [expanded]="true"
  >
    <mat-expansion-panel-header>
      <mat-panel-title class="rkt-AccordionPanel__header-title center-aligned gap-2">
        <mat-icon svgIcon="home-outlined" class="rkt-Icon" />
        <p class="rkt-Label-14">
          Address
          {{
            !panel.expanded && ownedProperty() && ownedProperty().address
              ? (ownedProperty().address | address)
              : ''
          }}
        </p>
      </mat-panel-title>
    </mat-expansion-panel-header>
    <div class="grid grid-cols-10 detail-grid">
      <app-address class="col-span-10" [addressForm]="addressForm()" [showCounty]="true" />
      <ng-container [formGroup]="formGroup()">
        <mat-form-field class="rkt-FormField col-span-5">
          <mat-label>Property Type</mat-label>
          <mat-select
            class="rkt-Input"
            formControlName="propertyType"
            data-synthetic-monitor-id="propertyType"
          >
            <mat-option [value]="null">None</mat-option>
            @for (propertyType of propertyTypeOptions; track propertyType) {
              <mat-option
                attr.data-synthetic-monitor-id="{{ 'property-Type-option_' + propertyType.value }}"
                [value]="propertyType.value"
                >{{ propertyType.display }}
              </mat-option>
            }
          </mat-select>
        </mat-form-field>
        <app-select-field
          class="col-span-5"
          label="Occupancy Type"
          [options]="occupancyTypeOptions"
          [control]="occupancyTypeControl()"
        />
        <app-client-select
          label="Title Holder(s)"
          class="col-span-10"
          [control]="formGroup().controls.ownerClientIds"
        />
        <div class="hoa-fac-pp-container col-span-10">
          <mat-slide-toggle #hoaToggle class="rkt-SlideToggle" [checked]="hasHoa()">
            <span class="rkt-SlideToggle__label rkt-Spacing--ml8">HOA</span>
          </mat-slide-toggle>
          <mat-slide-toggle
            class="rkt-SlideToggle"
            color="accent"
            formControlName="isOwnedFreeAndClear"
          >
            <span class="rkt-SlideToggle__label rkt-Spacing--ml8">Free and Clear</span>
          </mat-slide-toggle>
          <mat-slide-toggle
            class="rkt-SlideToggle"
            color="accent"
            formControlName="isConvertedFromPrimary"
          >
            <span class="rkt-SlideToggle__label rkt-Spacing--ml8">Previous Primary</span>
          </mat-slide-toggle>
        </div>
        @if (associationWarning()) {
          <div class="col-span-10">
            <rkt-alert-enterprise variant="caution" [isDismissible]="true">
              <div class="rkt-Alert__text">
                {{ associationWarning()!.message }}
              </div>
            </rkt-alert-enterprise>
          </div>
        }
        @if (hoaToggle.checked) {
          <mat-divider class="rkt-HorizontalDivider col-span-10" />
          <div class="col-span-10 grid grid-cols-10 gap-4">
            <p class="rkt-Label-14 rkt-FontWeight--500 col-span-10">HOA Conditions</p>

            <app-formatted-number-input
              class="col-span-5"
              prefix="$"
              label="Payment Amount"
              appNavInput
              [control]="hoaAmountControl()"
              [allowNegative]="false"
            />
            <mat-form-field class="rkt-FormField col-span-5">
              <mat-label>Frequency</mat-label>
              <mat-select class="rkt-Input" [formControl]="hoaFrequencyControl()">
                <mat-option [value]="null">None</mat-option>
                <mat-option value="Monthly">Monthly</mat-option>
                <!-- Temporary change until AMP supports more frequency types
                       @for (status of paymentFrequency; track status) {
                        <mat-option [value]="status.value">{{ status.display }}</mat-option>
                      } -->
              </mat-select>
            </mat-form-field>
          </div>
        }
        <mat-divider class="rkt-HorizontalDivider col-span-10" />
        <div class="col-span-10 grid grid-cols-10 gap-4">
          <p class="rkt-Label-14 rkt-FontWeight--500 col-span-10">Property Information</p>
          <app-formatted-number-input
            class="col-span-5"
            prefix="$"
            label="Present Value"
            appNavInput
            [control]="estimatedPropertyValueControl()"
            [allowNegative]="false"
          />
          <app-select-field
            class="col-span-5"
            label="Property Status"
            [control]="estimatedPropertyStatusControl()"
            [options]="ownedPropertyDispositionStatusOptions"
          />
          <p class="rkt-Label-14 rkt-FontWeight--500 col-span-10">Net Proceeds</p>
          <div class="col-span-10 grid grid-cols-10 gap-6">
            @if (dispositionStatus() === OwnedPropertyDispositionStatus.PendingSale) {
              <app-formatted-date-input
                class="col-span-3"
                label="ACD of Sale"
                [control]="pendingSalesClosingDateControl()"
              />
              <app-formatted-number-input
                class="col-span-5"
                prefix="$"
                label="Estimated Net Proceeds"
                appNavInput
                [control]="netProceedsControl"
                [allowNegative]="false"
              />
            }
            <mat-slide-toggle
              class="rkt-SlideToggle col-span-2 pt-2"
              color="accent"
              [formControl]="isForSaleByOwnerControl()"
            >
              <span class="rkt-SlideToggle__label rkt-Spacing--ml8">FSBO</span>
            </mat-slide-toggle>
          </div>
        </div>
        <mat-divider class="rkt-HorizontalDivider col-span-10" />
        <div class="col-span-10 grid grid-cols-10 gap-4">
          <p class="rkt-Label-14 rkt-FontWeight--500 col-span-10">Taxes and Insurance</p>
          <app-formatted-number-input
            class="col-span-5"
            prefix="$"
            label="Property Tax (Annually)"
            appNavInput
            [control]="propertyTaxAmountControl()"
            [allowNegative]="false"
          />
          <app-formatted-number-input
            class="col-span-5"
            prefix="$"
            label="Homeowners Insurance (Annually)"
            appNavInput
            [control]="hoiAmountControl()"
            [allowNegative]="false"
          />
        </div>
      </ng-container>
    </div>
  </mat-expansion-panel>
</mat-accordion>
