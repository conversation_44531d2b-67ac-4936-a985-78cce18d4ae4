:host {
  ::ng-deep {
    .rkt-AccordionPanel .mat-expansion-panel-body {
      padding: 8px 12px 16px;
    }
  }
}

.detail-grid {
  column-gap: 1rem;
  row-gap: 12px;
}

.hoa-fac-pp-container {
  column-gap: 16px;
  row-gap: 12px;
  display: flex;
}

mat-expansion-panel .mat-expansion-panel-header {
  padding: 8px 12px;
}

mat-expansion-panel.rkt-AccordionPanel.mat-expansion-panel.mat-expansion-panel.rkt-AccordionPanel--enterprise:hover,
mat-expansion-panel.rkt-AccordionPanel.mat-expansion-panel.mat-expansion-panel.rkt-AccordionPanel--enterprise.mat-expanded {
  background-color: var(--mat-expansion-panel-background-color);

  .mat-expansion-panel-header.mat-expansion-panel-header:hover {
    background-color: var(--mat-expansion-panel-hover-background-color);
  }
}

.center-aligned {
  display: flex;
  align-items: center;
  gap: 8px;

  p {
    margin: 0;
  }
}
