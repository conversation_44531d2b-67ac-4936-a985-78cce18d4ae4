@if (!isFetching()) {
  <app-left-nav-errors [entityName]="entityName" [formIds]="errorLookupFormIds()" />

  @if (unassociatedLiabilityForms()?.length ?? 0 > 0) {
    <div class="liability-group">
      @for (liabilityFormMap of unassociatedLiabilityForms(); track liabilityFormMap[0]) {
        <app-liability
          [formGroup]="liabilityFormMap[1]"
          [ownedProperties]="ownedProperties()"
          (delete)="liabilityFormService.deleteLiability(liabilityFormMap[0])"
          (convert)="liabilityFormService.convertLienToInstallmentRevolving(liabilityFormMap[1])"
        />
      }
    </div>
  }

  @if (ownedPropertyMapEntries().length > 0) {
    <header class="tile-container">
      <app-owned-property-tile-display />
      <button
        id="add-button"
        mat-button
        class="rkt-Button rkt-Button--large rkt-Button--tertiary rkt-Button--has-icon"
        color="accent"
        (click)="onAddProperty()"
      >
        <mat-icon class="rkt-Icon" color="primary" svgIcon="add-outlined" />
      </button>
    </header>

    <app-owned-property-section-details
      class="flex-1"
      [formGroupEntry]="ownedPropertyMapEntries()[currentPropInfoSerivce.ownedPropertyIndex()]"
      [ownedProperties]="ownedProperties()"
      [ownedProperty]="ownedProperties()[currentPropInfoSerivce.ownedPropertyIndex()]"
    />
  } @else {
    <div class="no-owned-property-container">
      <p class="rkt-Label-14">No real estate owned property added</p>
      <button
        mat-button
        color="accent"
        class="rkt-Button rkt-Button--secondary rkt-Button--has-icon"
        (click)="onAddProperty()"
      >
        <mat-icon svgIcon="add-outlined" class="rkt-Icon" color="primary" />
        Add Property
      </button>
      <button
        mat-button
        color="accent"
        class="rkt-Button rkt-Button--tertiary rkt-Button--large rkt-Button--has-icon"
        (click)="openInPQ()"
      >
        <mat-icon class="rkt-Icon" svgIcon="open_in_new-outlined" />
        Property Qualfier
      </button>
    </div>
  }
}
