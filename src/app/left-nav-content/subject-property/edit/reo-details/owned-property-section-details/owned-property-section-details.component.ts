import { Component, inject, input } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatIcon } from '@angular/material/icon';
import { OwnedProperty } from '@rocket-logic/rl-xp-bff-models';
import { RktTagEnterpriseModule } from '@rocketcentral/rocket-design-system-enterprise-angular';
import { environment } from '../../../../../../environments/environment';
import { BaseOwnedPropertySectionComponent } from '../../../../../reo/owned-property-section/base-owned-property-section.component';
import { LoanIdService } from '../../../../../services/loan-id/loan-id.service';
import { AssociatedLienDetailsComponent } from '../associated-lien-details/associated-lien-details.component';
import { OwnedPropertyDetailDetailsComponent } from '../owned-property-detail-details/owned-property-detail-details.component';

@Component({
  selector: 'app-owned-property-section-details',
  standalone: true,
  imports: [
    MatIcon,
    RktTagEnterpriseModule,
    OwnedPropertyDetailDetailsComponent,
    AssociatedLienDetailsComponent,
    MatButtonModule,
  ],
  templateUrl: './owned-property-section-details.component.html',
  styleUrl: './owned-property-section-details.component.scss',
})
export class OwnedPropertySectionDetailsComponent extends BaseOwnedPropertySectionComponent {
  loanIdService = inject(LoanIdService);
  ownedProperty = input.required<OwnedProperty>();

  openInPQ() {
    window.open(
      `${environment.propertyQualifierUrl}/${this.loanIdService.loanId()}/property`,
      '_blank',
    );
  }
}
