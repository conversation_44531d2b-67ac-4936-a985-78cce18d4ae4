<div class="flex flex-col gap-4 h-full">
  <div class="flex-1 pl-4 pr-4 flex flex-col owned-property-container">
    <app-owned-property-detail-details [ownedProperty]="ownedProperty()" [formGroup]="formGroup()" />

    @for (lienFormMap of associatedLienForms(); track lienFormMap[0]) {
      <app-associated-lien-details
        [associatedLienForms]="associatedLienForms()"
        [lienForm]="lienFormMap[1]"
        [ownedProperties]="otherOwnedProperties()"
        [isSubjectProperty]="isSubjectProperty()"
        (delete)="onDeleteLien(lienFormMap[0])"
        (convert)="onConvertLien(lienFormMap[1])"
      />
    }
  </div>

  <footer>
    <button
      mat-button
      color="accent"
      class="rkt-Button rkt-Button--tertiary rkt-Button--has-icon-left"
      (click)="openInPQ()"
    >
      <mat-icon class="rkt-Icon" svgIcon="open_in_new-outlined" />
      Property Qualfier
    </button>
    <button
      class="rkt-Button rkt-Button--secondary rkt-Button--has-icon-left"
      mat-stroked-button
      (click)="onAddLien()"
      [class.rkt-Button--is-disabled]="isUnsavedProperty() || isDisabled()"
      [disabled]="isUnsavedProperty() || isDisabled()"
    >
      <mat-icon class="rkt-Icon" svgIcon="add-outlined" />
      Add Lien
    </button>
  </footer>
</div>
