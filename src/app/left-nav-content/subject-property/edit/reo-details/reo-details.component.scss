@use '../../../shared/styles/left-nav-details.component.scss';

:host {
  display: flex;
  flex-direction: column;
  height: 100%;
}

mat-expansion-panel {
  border-radius: 12px;
  border: 1px solid var(--rlxp-gray-200, #d3d3d3);
}

.center-aligned {
  display: flex;
  align-items: center;
  gap: 8px;

  p {
    margin: 0;
  }
}

.tile-container {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 10px;
}

.liability-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 16px;
  padding-bottom: 0;
}

:host {
  #add-button {
    width: 45px;
    height: 52px;
    align-self: center;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;

    mat-icon {
      margin: 0;
    }
  }
}

.no-owned-property-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 16px;
  flex: 1 0 0;
  align-self: stretch;
  width: 761px;
  height: 590px;
}

app-left-nav-errors {
  margin: 16px;
}
