import { Component, Inject, inject } from '@angular/core';
import {
  MAT_DIALOG_DATA,
  MatDialogActions,
  MatDialogContent,
  MatDialogRef,
} from '@angular/material/dialog';
import { OwnedPropertyGroup } from '../../../../services/entity-state/owned-property-state/form-types';

@Component({
  selector: 'app-reo-pop-out',
  standalone: true,
  imports: [MatDialogContent, MatDialogActions],
  templateUrl: './reo-pop-out.component.html',
  styleUrl: './reo-pop-out.component.scss',
})
export class ReoPopOutComponent {
  private dialogRef = inject(MatDialogRef<ReoPopOutComponent>);

  constructor(
    @Inject(MAT_DIALOG_DATA)
    public data: {
      ownedProperty: OwnedPropertyGroup;
    },
  ) {}

  onSubmit() {
    this.dialogRef.close('submitted');
  }

  onCancel() {
    this.dialogRef.close('cancelled');
  }
}
