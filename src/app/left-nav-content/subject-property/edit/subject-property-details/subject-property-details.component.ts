import { ChangeDetectionStrategy, Component, computed, inject } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { RktTagEnterpriseModule } from '@rocketcentral/rocket-design-system-enterprise-angular';
import { EntityStateName } from '../../../../services/entity-state/abstract-entity-state.service';
import { SubjectPropertyStateService } from '../../../../services/entity-state/subject-property-state/subject-property-state.service';
import { LoanIdService } from '../../../../services/loan-id/loan-id.service';
import { SubjectPropertyFormDisplayComponent } from '../../../../subject-property/subject-property-form-display/subject-property-form-display.component';
import { SubjectPropertySkeletonComponent } from '../../../../subject-property/subject-property-skeleton/subject-property-skeleton.component';
import { getPropertyQualifierUrl } from '../../../../util/get-url';
import { AppLeftNavErrorsComponent } from '../../../shared/form-errors/left-nav-errors.component';

@Component({
  selector: 'app-subject-property-details',
  standalone: true,
  imports: [
    SubjectPropertyFormDisplayComponent,
    SubjectPropertySkeletonComponent,
    MatIconModule,
    RktTagEnterpriseModule,
    MatCardModule,
    MatButtonModule,
    AppLeftNavErrorsComponent,
  ],
  templateUrl: './subject-property-details.component.html',
  styleUrl: './subject-property-details.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SubjectPropertyDetailsComponent {
  readonly subjectPropertyStateService = inject(SubjectPropertyStateService);
  readonly loanIdService = inject(LoanIdService);

  readonly entityName = [EntityStateName.SubjectProperty];

  isFetching = this.subjectPropertyStateService.isFetching;

  readonly loanId = computed(() => this.loanIdService.loanId());

  navigateToPQ() {
    if (this.loanId()) {
      window.location.href = getPropertyQualifierUrl(this.loanId()!);
    }
  }
}
