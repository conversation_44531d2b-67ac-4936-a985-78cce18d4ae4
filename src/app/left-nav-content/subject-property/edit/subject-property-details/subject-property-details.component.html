<div class="body">
  @if (!isFetching()) {
    <app-left-nav-errors [entityName]="entityName" />
    <mat-card class="rkt-Card rkt-Card--enterprise">
      <app-subject-property-form-display [leftNavSection]="true" />
    </mat-card>
  } @else {
    <app-subject-property-skeleton />
  }
</div>
<footer>
  <button
    [disabled]="isFetching()"
    mat-button
    class="rkt-Button rkt-Button--tertiary rkt-Button--has-icon"
    [class.rkt-Button--is-disabled]="isFetching()"
    color="accent"
    (click)="navigateToPQ()"
  >
    <mat-icon class="rkt-Icon" color="secondary" svgIcon="open_in_new-outlined" />
    Property Qualifier
  </button>
</footer>
