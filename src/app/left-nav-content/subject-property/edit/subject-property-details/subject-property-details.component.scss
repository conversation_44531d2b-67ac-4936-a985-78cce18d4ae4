@use '../../../shared/styles/left-nav-details.component.scss';

:host {
  display: flex;
  height: 100%;
  flex-direction: column;
}

.body {
  flex: 1 1 0;
  display: flex;
  flex-direction: column;
  padding: 16px;
  gap: 16px;
}

footer {
  display: flex;
  justify-content: flex-end;
  gap: 16px;
  padding: 16px 16px 16px 0;
  box-shadow: 0px -8px 12px -12px rgba(0, 0, 0, 0.16);
  z-index: 1;
  border-top: 1px solid var(--rlxp-gray-200);
}

mat-card {
  border: 1px solid var(--rlxp-gray-200, #d3d3d3);
}
