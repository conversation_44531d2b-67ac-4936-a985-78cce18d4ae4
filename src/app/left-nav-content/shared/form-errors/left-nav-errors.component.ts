import { Component, computed, inject, input } from '@angular/core';
import { EnhancedErrorDisplayComponent } from '../../../components/enhanced-error-display/enhanced-error-display.component';
import { EntityStateName } from '../../../services/entity-state/abstract-entity-state.service';
import { ValidationErrorProviderService } from '../../../services/error/validation-error-provider.service';

@Component({
  selector: 'app-left-nav-errors',
  standalone: true,
  imports: [EnhancedErrorDisplayComponent],
  template: `
    @if (errors().length) {
      <app-enhanced-error-display [errors]="errors()" />
    }
  `,
  styleUrl: './left-nav-errors.component.scss',
  host: {
    '[class.hidden]': '!errors().length',
    '[class.flex]': 'errors.length',
  },
})
export class AppLeftNavErrorsComponent {
  readonly entityName = input.required<EntityStateName[]>();
  readonly formIds = input<string[]>([]);

  readonly validationErrorService = inject(ValidationErrorProviderService);
  readonly errors = computed(() =>
    this.validationErrorService
      .errors()
      .filter(
        (error) =>
          this.handleFormLookup(error.formId) && this.entityName().includes(error.stateName),
      ),
  );

  dismissAllErrors() {
    this.errors().forEach((error) => error?.onDismiss());
  }

  // filter errors to specific form id or return every one of an entity type
  private handleFormLookup(formId?: string): boolean {
    return this.formIds().length && formId ? this.formIds().includes(formId) : true;
  }
}
