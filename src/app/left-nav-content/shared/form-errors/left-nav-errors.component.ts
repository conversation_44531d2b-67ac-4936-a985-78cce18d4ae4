import { Component, computed, inject, input } from '@angular/core';
import { RktAlertEnterpriseModule } from '@rocketcentral/rocket-design-system-enterprise-angular';
import { FormPathLabelPipe } from '../../../form-path-label.pipe';
import { EntityStateName } from '../../../services/entity-state/abstract-entity-state.service';
import { ValidationErrorProviderService } from '../../../services/error/validation-error-provider.service';

@Component({
  selector: 'app-left-nav-errors',
  standalone: true,
  imports: [RktAlertEnterpriseModule, FormPathLabelPipe],
  templateUrl: './left-nav-errors.component.html',
  styleUrl: './left-nav-errors.component.scss',
  host: {
    '[class.hidden]': '!errors().length',
    '[class.flex]': 'errors.length',
  },
})
export class AppLeftNavErrorsComponent {
  readonly entityName = input.required<EntityStateName[]>();
  readonly formIds = input<string[]>([]);

  readonly validationErrorService = inject(ValidationErrorProviderService);
  readonly errors = computed(() =>
    this.validationErrorService
      .errors()
      .filter(
        (error) =>
          this.handleFormLookup(error.formId) && this.entityName().includes(error.stateName),
      ),
  );

  dismissAllErrors() {
    this.errors().forEach((error) => error?.onDismiss());
  }

  // filter errors to specific form id or return every one of an entity type
  private handleFormLookup(formId?: string): boolean {
    return this.formIds().length && formId ? this.formIds().includes(formId) : true;
  }
}
