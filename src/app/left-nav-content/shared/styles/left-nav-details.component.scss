header {
  display: flex;
  gap: 8px;
  padding: 16px 0 18px 16px;
  position: sticky;
  top: 0;
  z-index: 3;
  background: var(--mdc-elevated-card-container-color);
}

.body {
  display: flex;
  flex-direction: column;
  padding: 16px;
  gap: 16px;
  overflow-y: auto;
}

footer {
  display: flex;
  justify-content: flex-end;
  gap: 16px;
  padding: 16px 16px 16px 0;
  box-shadow: 0px -8px 12px -12px rgba(0, 0, 0, 0.16);
  z-index: 3;
  border-top: 1px solid var(--rlxp-gray-200);
  position: sticky;
  bottom: 0;
  background: var(--mdc-elevated-card-container-color);
  width: 100%;
}
