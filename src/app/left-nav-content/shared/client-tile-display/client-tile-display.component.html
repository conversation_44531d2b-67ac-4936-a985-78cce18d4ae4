<nav class="client-tiles">
  @if (primaryBorrower(); as borrower) {
    <app-tile
      [isLoading]="false"
      [label]="borrower.role"
      [isTileInteractive]="true"
      [isSelected]="borrower.isSelected"
      [variant]="borrower.presentation.variant"
      [icon]="borrower.presentation.icon"
      (click)="onClientSelect(borrower.clientFormId)"
    >
      <strong>{{ borrower.fullName ? borrower.fullName : '--' }}</strong>
      @if (hasCustomButton()) {
        <app-update-client-button
          custom-button
          [clientForm]="borrower.clientForm!"
          [clients]="clients()"
          (delete)="clientActionsHandlerService.onDelete(borrower.clientFormId)"
        />
      }
    </app-tile>
  }
  @for (coClient of coClients(); track coClient.id) {
    <app-tile
      [isLoading]="false"
      [label]="coClient.role"
      [isTileInteractive]="true"
      [isSelected]="coClient.isSelected"
      [variant]="coClient.presentation.variant"
      [icon]="coClient.presentation.icon"
      (click)="onClientSelect(coClient.clientFormId)"
    >
      <strong>{{ coClient.fullName ? coClient.fullName : '--' }}</strong>
      @if (hasCustomButton()) {
        <app-update-client-button
          custom-button
          [clientForm]="coClient.clientForm!"
          [clients]="clients()"
          (delete)="clientActionsHandlerService.onDelete(coClient.clientFormId)"
        />
      }
    </app-tile>
  }
</nav>
