import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, computed, inject, input } from '@angular/core';
import { UpdateClientButtonComponent } from '../../../clients-info/update-client-button/update-client-button.component';
import { ClientCategorizationFacadeService } from '../../../declarations-demographics/services/client-categorization.service';
import { ClientActionsHandlerService } from '../../../services/entity-state/client-state/client-actions-handler.component';
import { ClientStateService } from '../../../services/entity-state/client-state/client-state.service';
import { TileComponent } from '../../../tile/tile.component';

@Component({
  selector: 'app-client-tile-display',
  standalone: true,
  imports: [CommonModule, TileComponent, UpdateClientButtonComponent],
  templateUrl: './client-tile-display.component.html',
  styleUrl: './client-tile-display.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ClientTileDisplayComponent {
  hasCustomButton = input<boolean>(false);

  private readonly clientCategorizationFacade = inject(ClientCategorizationFacadeService);
  readonly clientActionsHandlerService = inject(ClientActionsHandlerService);
  readonly clientStateService = inject(ClientStateService);

  clients = computed(() => this.clientStateService.stateValues() ?? []);

  readonly primaryBorrower = computed(
    () => this.clientCategorizationFacade.clients().primaryClient,
  );

  readonly coClients = computed(() => this.clientCategorizationFacade.clients().coClients);

  onClientSelect(clientFormId: string | undefined) {
    this.clientCategorizationFacade.selectClient(clientFormId || '');
  }
}
