import { Directive, OnDestroy, computed, inject } from '@angular/core';
import { LeftPanelEditStateListenerService } from '../../[containers]/left-panel/edit-state-listener.service';
import { LeftPanelOverlayService } from '../../[containers]/left-panel/left-panel-overlay.service';

@Directive()
export abstract class LeftNavContainerComponent implements OnDestroy {
  readonly leftPanelOverlayService = inject(LeftPanelOverlayService);
  readonly leftPanelEditStateListenerService = inject(LeftPanelEditStateListenerService);

  isEditScreen = computed(() => this.leftPanelEditStateListenerService.isEditState());

  setEditState() {
    this.leftPanelEditStateListenerService.isEditState.set(true);
    this.leftPanelOverlayService.updateEditPositioning();
  }

  ngOnDestroy() {
    this.leftPanelEditStateListenerService.isEditState.set(false);
  }
}
