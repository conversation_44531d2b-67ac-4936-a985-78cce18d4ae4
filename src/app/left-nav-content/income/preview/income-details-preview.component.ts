import { CurrencyPipe, formatCurrency } from '@angular/common';
import { Component, computed, inject } from '@angular/core';
import { MatDividerModule } from '@angular/material/divider';
import { MatTabsModule } from '@angular/material/tabs';
import { MatTooltipModule } from '@angular/material/tooltip';
import { Client, Income } from '@rocket-logic/rl-xp-bff-models';
import { RktTagEnterpriseModule } from '@rocketcentral/rocket-design-system-enterprise-angular';
import { ClientStateService } from '../../../services/entity-state/client-state/client-state.service';
import { IncomeStateService } from '../../../services/entity-state/income-state/income-state.service';
import { ProductStateService } from '../../../services/entity-state/product-state/product-state.service';
import { TileComponent } from '../../../tile/tile.component';
import { ClientNamePipe } from '../../../util/client-name.pipe';

@Component({
  selector: 'app-income-details-preview',
  standalone: true,
  imports: [
    MatTabsModule,
    RktTagEnterpriseModule,
    TileComponent,
    MatDividerModule,
    ClientNamePipe,
    MatTooltipModule,
    CurrencyPipe,
  ],
  templateUrl: './income-details-preview.component.html',
  styleUrl: './income-details-preview.component.scss',
})
export class IncomeDetailsPreviewComponent {
  clientStateService = inject(ClientStateService);
  incomeStateService = inject(IncomeStateService);
  private readonly productStateService = inject(ProductStateService);

  readonly #currentData = computed(() => this.incomeStateService.state()?.data);
  readonly clients = computed(() => this.clientStateService.stateValues() ?? []);

  readonly isLoading = computed(
    () =>
      (this.incomeStateService.state()?.fetching ?? false) ||
      (this.clientStateService.state()?.fetching ?? false),
  );

  clientIncomeMap = computed(() => {
    const incomes: { client: Client; incomeInfo: Map<string, Income> | undefined }[] = [];
    this.clients().forEach((client) => {
      const incomeInfo = this.#currentData()?.[client?.id ?? ''];
      incomes.push({ client, incomeInfo });
    });
    return incomes;
  });

  readonly totalQualifiedIncome = computed(() =>
    formatCurrency(
      this.productStateService.state()?.data?.qualificationDetails?.qualifiedAmounts
        ?.totalQualifiedIncomeAmount ?? 0,
      'en-US',
      '$',
    ),
  );

  totalIncomeByClient = computed(() => {
    return this.clientIncomeMap().map((clientIncome) => {
      let totalIncome = 0;

      const client = clientIncome.client;
      const incomeMap = clientIncome.incomeInfo ?? new Map<string, Income>();
      incomeMap.forEach((value: Income, key: string) => {
        totalIncome += value.totalMonthlyIncomeAmount ?? 0;
      });

      return { client, totalIncome };
    });
  });
}
