<h2 class="rkt-Tracked-12">Income</h2>

<div class="tile-section">
  <app-tile label="Qualifying Income" [isLoading]="isLoading()">
    <strong>{{ totalQualifiedIncome() }}</strong>
  </app-tile>
  @for (clientIncome of totalIncomeByClient(); track clientIncome) {
    <app-tile
      label="{{ clientIncome.client | clientName }}"
      [isLoading]="isLoading()"
      [icon]="clientIncome?.client?.isPrimaryBorrower ? 'person_outline-outlined' : ''"
      [tooltip]="clientIncome?.client?.isPrimaryBorrower ? 'Primary Client' : ''"
    >
      @if (clientIncome.totalIncome === 0) {
        <strong>$0</strong>
      } @else {
        <strong>{{ clientIncome.totalIncome | currency }}</strong>
      }
    </app-tile>
  }
</div>
