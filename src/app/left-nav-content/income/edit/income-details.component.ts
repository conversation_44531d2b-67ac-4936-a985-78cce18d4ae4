import { formatCurrency } from '@angular/common';
import { Component, computed, inject } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatIconModule } from '@angular/material/icon';
import { Client, IncomeType } from '@rocket-logic/rl-xp-bff-models';
import { RktTagEnterpriseModule } from '@rocketcentral/rocket-design-system-enterprise-angular';
import { LeftPanelOverlayService } from '../../../[containers]/left-panel/left-panel-overlay.service';
import { SaveStatusComponent } from '../../../application-island/save-status/save-status.component';
import { AddIncomeButtonComponent } from '../../../income-info/add-income-button/add-income-button.component';
import { BaseIncomeComponent } from '../../../income-info/base-income/base-income.component';
import { IncomeSkeletonComponent } from '../../../income-info/income-skeleton/income-skeleton.component';
import { EntityStateName } from '../../../services/entity-state/abstract-entity-state.service';
import { ClientStateService } from '../../../services/entity-state/client-state/client-state.service';
import { AllIncomeGroup } from '../../../services/entity-state/income-state/form-types';
import { IncomeFormService } from '../../../services/entity-state/income-state/income-form.service';
import { IncomeStateService } from '../../../services/entity-state/income-state/income-state.service';
import { IncomeUpdateHandlerService } from '../../../services/entity-state/income-state/income-update-handler.service';
import { ProductStateService } from '../../../services/entity-state/product-state/product-state.service';
import {
  ValidationErrorDto,
  ValidationErrorProviderService,
} from '../../../services/error/validation-error-provider.service';
import { LoanIdService } from '../../../services/loan-id/loan-id.service';
import { ClientNamePipe } from '../../../util/client-name.pipe';
import { getEmployerQualifierUrl, getIncomeQualifierUrl } from '../../../util/get-url';
import { AppLeftNavErrorsComponent } from '../../shared/form-errors/left-nav-errors.component';

@Component({
  selector: 'app-income-details',
  standalone: true,
  imports: [
    BaseIncomeComponent,
    MatButtonModule,
    MatIconModule,
    IncomeSkeletonComponent,
    MatCardModule,
    ClientNamePipe,
    MatExpansionModule,
    AddIncomeButtonComponent,
    RktTagEnterpriseModule,
    AppLeftNavErrorsComponent,
    SaveStatusComponent,
  ],
  templateUrl: './income-details.component.html',
  styleUrl: './income-details.component.scss',
})
export class IncomeDetailsComponent {
  readonly formService = inject(IncomeFormService);
  readonly incomeStateService = inject(IncomeStateService);
  readonly incomeUpdateHandler = inject(IncomeUpdateHandlerService);
  readonly clientStateService = inject(ClientStateService);
  readonly loanIdService = inject(LoanIdService);
  readonly leftPanelOverlayService = inject(LeftPanelOverlayService);
  readonly validationErrorService = inject(ValidationErrorProviderService);
  private readonly productStateService = inject(ProductStateService);

  readonly entityName = [EntityStateName.Income];

  readonly loanId = computed(() => this.loanIdService.loanId());

  isFetching = computed(() => {
    const { data, fetching } = this.incomeStateService.state() ?? {};
    return (fetching && !data) ?? false;
  });
  clients = computed(() => this.clientStateService.stateValues());
  primaryClientId = computed(() => this.clients().find((client) => client.isPrimaryBorrower)?.id);
  errors = computed(() =>
    this.validationErrorService
      .errors()
      .filter((error) => error.stateName === EntityStateName.Income),
  );
  clientIncomeMap = computed(() =>
    Object.entries(this.formService.clientIncomeMap())
      .map(([clientId, incomeMap]) => {
        const client = this.clients().find((client) => client.id === clientId);
        const incomeSourceLabel =
          incomeMap.size === 1 ? '1 Income Source' : `${incomeMap.size} Income Sources`;
        const totalMonthly = this.getTotalQualifyingMonthlyIncome(incomeMap);
        const formattedTotal =
          totalMonthly > 0 ? formatCurrency(totalMonthly, 'en-US', '$', 'USD') : '$0';
        const hasErrors = this.incomeFormHasErrors(incomeMap, this.errors());
        const incomeLabel = `${incomeSourceLabel}, Total: ${formattedTotal}/Month`;

        return { client, incomeMap, incomeLabel, hasErrors };
      })
      .filter(
        (
          value,
        ): value is {
          client: Client;
          incomeMap: Map<string, AllIncomeGroup>;
          incomeLabel: string;
          hasErrors: boolean;
        } => value.client !== undefined,
      ),
  );

  readonly totalQualifyingIncome = computed(() =>
    formatCurrency(
      this.productStateService.state()?.data?.qualificationDetails?.qualifiedAmounts
        ?.totalQualifiedIncomeAmount ?? 0,
      'en-US',
      '$',
    ),
  );

  addIncome(client: Client) {
    this.formService.addIncome(client.id!);
  }

  onDeleteIncome(client: Client, incomeId: string, incomeType: IncomeType) {
    this.formService.deleteIncome(client.id!, incomeId, incomeType);
  }

  navigateToIQ() {
    if (this.loanId()) {
      window.location.href = getIncomeQualifierUrl(this.loanId()!);
    }
  }

  navigateToEM() {
    if (this.loanId()) {
      window.location.href = getEmployerQualifierUrl(this.loanId()!);
    }
  }

  private getTotalQualifyingMonthlyIncome(incomeMap: Map<string, AllIncomeGroup>) {
    return Array.from(incomeMap.values()).reduce(
      (total, income) => (income.value.totalMonthlyIncomeAmount ?? 0) + total,
      0,
    );
  }

  private incomeFormHasErrors(
    incomeMap: Map<string, AllIncomeGroup>,
    errors: ValidationErrorDto[],
  ): boolean {
    const mapKeys = Array.from(incomeMap.keys());
    return errors.some((error) => mapKeys.some((key) => error.formId?.includes(key)));
  }
}
