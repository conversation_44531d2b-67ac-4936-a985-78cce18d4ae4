<div class="container">
  <header class="items-center justify-between">
    <div class="flex gap-2">
      <span class="rkt-Label-16 rkt-FontWeight--500">Income</span>
      <rkt-tag-enterprise
        >Total Qualifying Income: <b>{{ totalQualifyingIncome() }}/month</b></rkt-tag-enterprise
      >
    </div>
    <div class="header-container">
      <app-save-status
        [updateHandlers]="[incomeUpdateHandler]"
        [stateServices]="[incomeStateService]"
      />

      <button
        mat-icon-button
        class="rkt-ButtonIcon"
        aria-label="Close"
        (click)="leftPanelOverlayService.hidePopout()"
      >
        <mat-icon class="rkt-Icon">close</mat-icon>
      </button>
    </div>
  </header>
  <div class="body flex-1">
    @if (!isFetching()) {
      <app-left-nav-errors [entityName]="entityName" />
      @for (clientGroup of clientIncomeMap(); track clientGroup.client) {
        <mat-accordion multi displayMode="flat" color="accent">
          <mat-expansion-panel
            class="rkt-AccordionPanel rkt-AccordionPanel--enterprise"
            [expanded]="true"
          >
            <mat-expansion-panel-header>
              <mat-panel-title class="rkt-AccordionPanel__header-title">
                <div class="title-info">
                  <mat-icon class="rkt-Icon" svgIcon="account_circle-outlined" />
                  <span class="rkt-Label-14 rkt-FontWeight--700">{{
                    clientGroup.client | clientName
                  }}</span>
                  <span class="rkt-Label-14 rkt-FontWeight--400">{{
                    clientGroup.incomeLabel
                  }}</span>
                  @if (clientGroup.client.isPrimaryBorrower) {
                    <rkt-tag-enterprise
                      variant="info"
                      iconPosition="left"
                      iconName="person_outline-outlined"
                      >Primary Client</rkt-tag-enterprise
                    >
                  }
                </div>
                @if (clientGroup.hasErrors) {
                  <mat-icon class="rkt-Icon error-icon" svgIcon="error-outlined" />
                }
              </mat-panel-title>
            </mat-expansion-panel-header>
            <div class="mb-3">
              <div class="flex flex-col gap-5">
                @for (incomeMap of clientGroup.incomeMap.entries(); track incomeMap[0]) {
                  <section>
                    <app-base-income
                      class="mt-4 first:mt-0"
                      [formGroup]="incomeMap[1]"
                      (delete)="
                        onDeleteIncome(
                          clientGroup.client,
                          incomeMap[0],
                          incomeMap[1].value.incomeType!
                        )
                      "
                    ></app-base-income>
                  </section>
                }
              </div>
            </div>
          </mat-expansion-panel>
        </mat-accordion>
      }
    } @else {
      <app-income-skeleton />
    }
  </div>
  <footer>
    <button
      [disabled]="isFetching()"
      mat-button
      class="rkt-Button rkt-Button--tertiary rkt-Button--has-icon"
      [class.rkt-Button--is-disabled]="isFetching()"
      color="accent"
      (click)="navigateToEM()"
    >
      <mat-icon class="rkt-Icon" color="secondary" svgIcon="open_in_new-outlined" />
      Employer Manager
    </button>
    <button
      [disabled]="isFetching()"
      mat-button
      class="rkt-Button rkt-Button--tertiary rkt-Button--has-icon"
      [class.rkt-Button--is-disabled]="isFetching()"
      color="accent"
      (click)="navigateToIQ()"
    >
      <mat-icon class="rkt-Icon" color="secondary" svgIcon="open_in_new-outlined" />
      Income Qualifier
    </button>
    <app-add-income-button
      [clients]="clients()"
      icon="add-outlined"
      color="secondary"
      [isStroked]="true"
      [isLarge]="false"
      (addIncome)="addIncome($event)"
    />
  </footer>
</div>
