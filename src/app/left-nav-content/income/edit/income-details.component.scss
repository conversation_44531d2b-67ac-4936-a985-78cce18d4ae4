@use '../../../../styling/accordion-panel-no-hover.scss';
@use '../../shared/styles/left-nav-details.component.scss';

header {
  padding-right: 1rem;
}

section:not(:last-child) {
  border-bottom: 1px solid var(--rlxp-gray-200);
}

.container {
  display: flex;
  flex-direction: column;
  height: var(--left-panel-max-height);
}

mat-expansion-panel {
  border: 1px solid var(--rlxp-gray-200);
}

mat-expansion-panel-header {
  gap: 8px;
}

.title-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

mat-panel-title {
  display: flex;
  justify-content: space-between;
}

.error-icon {
  color: var(--rlxp-rose-500);
  width: 18px;
  height: 18px;
}

.header-container {
  display: flex;
  align-items: center;
  gap: 16px;
}
