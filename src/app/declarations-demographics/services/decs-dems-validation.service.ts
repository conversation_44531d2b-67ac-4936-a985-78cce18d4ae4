import { DestroyRef, effect, inject, Injectable, untracked } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import {
  AbstractControl,
  FormArray,
  FormBuilder,
  FormControl,
  FormGroup,
  Validators,
} from '@angular/forms';
import { LiabilityType } from '@rocket-logic/rl-xp-bff-models';
import { startWith } from 'rxjs';
import { ClientFormService } from '../../services/entity-state/client-state/client-form.service';
import { ClientSubscriptionManager } from '../../services/entity-state/client-state/client-subscription-manager.service';
import { ClientControls } from '../../services/entity-state/client-state/form-types';
import { LiabilityFormService } from '../../services/entity-state/liability-state/liability-form.service';
import {
  buildBankruptcyForm,
  buildCreditApplicationForm,
  buildForeclosureForm,
  buildForfeitureForm,
  buildLawsuitForm,
  buildOtherMortgageForm,
  buildPreForeclosureOrShortSaleForm,
  getBankruptcies,
  getCreditApplications,
  getForeclosures,
  getForfeitures,
  getLawsuits,
  getOtherMortgages,
  getPreForeclosureOrShortSales,
} from '../../util/build-declaration-information-form';
import { DeclarationMainQuestion } from '../models/declaration/declaration-main-question.enum';
import { ConditionalTrailQuestion } from '../models/question/conditional-question';
import { QuestionDefinition } from '../models/question/question-definition';
import { ClientCategorizationFacadeService } from './client-categorization.service';
import { DecsAndDemsFormFacadeService, Question } from './decs-dems-form.service';

interface BaseHandlerConfig {
  type: 'formArray' | 'liability';
}

interface FormArrayHandlerConfig extends BaseHandlerConfig {
  type: 'formArray';
  getFormArray: (declarationInfo: FormGroup) => FormArray | undefined;
  buildForm: (fb: FormBuilder) => any;
}

interface LiabilityHandlerConfig extends BaseHandlerConfig {
  type: 'liability';
  liabilityTypes: LiabilityType[];
  idArrayRef: (clientForm: FormGroup<ClientControls>) => FormArray;
}

type HandlerConfig = FormArrayHandlerConfig | LiabilityHandlerConfig;

@Injectable()
export class DecsDemsValidationService {
  private readonly subManager = inject(ClientSubscriptionManager);
  private readonly destroyRef = inject(DestroyRef);
  private readonly clientCategorizationService = inject(ClientCategorizationFacadeService);
  private readonly liabilityFormService = inject(LiabilityFormService);
  private readonly clientFormService = inject(ClientFormService);
  private readonly fb = inject(FormBuilder);
  private readonly decsDemsFormService = inject(DecsAndDemsFormFacadeService);
  private readonly formValidationMap = new WeakMap<FormControl, boolean>();

  private readonly handlerConfigs = new Map<DeclarationMainQuestion, HandlerConfig>([
    [
      DeclarationMainQuestion.HasRecentlyDeclaredBankruptcy,
      {
        type: 'formArray',
        getFormArray: getBankruptcies,
        buildForm: () => buildBankruptcyForm(this.fb),
      },
    ],
    [
      DeclarationMainQuestion.HasRecentlyHadForeclosure,
      {
        type: 'formArray',
        getFormArray: getForeclosures,
        buildForm: () => buildForeclosureForm(this.fb),
      },
    ],
    [
      DeclarationMainQuestion.IsCurrentlyPartyToLawsuit,
      { type: 'formArray', getFormArray: getLawsuits, buildForm: () => buildLawsuitForm(this.fb) },
    ],
    [
      DeclarationMainQuestion.HasForfeitedTitle,
      {
        type: 'formArray',
        getFormArray: getForfeitures,
        buildForm: () => buildForfeitureForm(this.fb),
      },
    ],
    [
      DeclarationMainQuestion.HasRecentlyHadPreForeclosureOrShortSale,
      {
        type: 'formArray',
        getFormArray: getPreForeclosureOrShortSales,
        buildForm: () => buildPreForeclosureOrShortSaleForm(this.fb),
      },
    ],
    [
      DeclarationMainQuestion.ApplyingForNewCreditAccount,
      {
        type: 'formArray',
        getFormArray: getCreditApplications,
        buildForm: () => buildCreditApplicationForm(this.fb),
      },
    ],
    [
      DeclarationMainQuestion.ApplyingForAnotherMortgage,
      {
        type: 'formArray',
        getFormArray: getOtherMortgages,
        buildForm: () => buildOtherMortgageForm(this.fb),
      },
    ],
    [
      DeclarationMainQuestion.HasOutstandingJudgments,
      {
        type: 'liability',
        liabilityTypes: [LiabilityType.MonetaryJudgement],
        idArrayRef: (clientForm) =>
          clientForm.controls.declarationInformation.controls.outstandingJudgmentLiabilityIds,
      },
    ],
    [
      DeclarationMainQuestion.IsPayingAlimonyOrChildSupport,
      {
        type: 'liability',
        liabilityTypes: [LiabilityType.Alimony, LiabilityType.ChildSupport],
        idArrayRef: (clientForm) =>
          clientForm.controls.declarationInformation.controls.childSupportOrAlimonyLiabilityIds,
      },
    ],
    [
      DeclarationMainQuestion.HasDelinquentFederalDebt,
      {
        type: 'liability',
        liabilityTypes: [LiabilityType.TaxesOwed],
        idArrayRef: (clientForm) =>
          clientForm.controls.declarationInformation.controls.delinquentFederalDebtLiabilityIds,
      },
    ],
    // TODO: Split into seperate formArrays
    [
      DeclarationMainQuestion.IsBorrowingMoney,
      {
        type: 'liability',
        liabilityTypes: [LiabilityType.MiscellaneousInstallment],
        idArrayRef: (clientForm) =>
          clientForm.controls.declarationInformation.controls.borrowedFundsLiabilityIds,
      },
    ],
    [
      DeclarationMainQuestion.IsCoSignerOrGuarantor,
      {
        type: 'liability',
        liabilityTypes: [LiabilityType.MiscellaneousInstallment],
        idArrayRef: (clientForm) =>
          clientForm.controls.declarationInformation.controls.cosignerOrGuarantorLiabilityIds,
      },
    ],
  ]);

  constructor() {
    effect(() => {
      const clientForms = this.clientFormService.entityFormMap();

      clientForms.forEach((clientForm, clientKey) => {
        const questions = this.decsDemsFormService.clientQuestionMap().get(clientKey);
        if (!questions) {
          return;
        }
        untracked(() => {
          this.addDecsDemsValidationListener(clientForm, clientKey, questions);
        });
      });
    });
  }

  addDecsDemsValidationListener(
    clientForm: FormGroup<ClientControls> | undefined,
    clientKey: string,
    questions: Question[],
  ) {
    if (!clientForm || !clientKey) {
      return;
    }

    questions.forEach((question) => {
      this.manageFormArrayValidation(clientForm, clientKey, question);

      question.question.conditionalTrails.forEach((trail: ConditionalTrailQuestion) => {
        trail.trailQuestions.forEach((nestedQuestion: QuestionDefinition) => {
          if (nestedQuestion.conditionalTrails?.length) {
            this.setupNestedValidators(nestedQuestion.conditionalTrails, clientKey);
          }
        });
      });
    });
  }

  private manageFormArrayValidation(
    clientForm: FormGroup<ClientControls>,
    clientKey: string,
    question: Question,
  ) {
    const declarationInfo = clientForm.controls.declarationInformation;
    if (!declarationInfo || !question.question.targetFormControl) {
      return;
    }

    const subscription = (question.question.targetFormControl as FormControl).valueChanges
      .pipe(
        startWith(question.question.targetFormControl.value),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe((value) =>
        this.runHandlerForQuestionType(
          question.name,
          declarationInfo,
          clientKey,
          clientForm,
          value,
        ),
      );

    this.subManager.addPersistentSubscription(clientKey, subscription);
  }

  private runHandlerForQuestionType(
    questionType: DeclarationMainQuestion,
    declarationInfo: FormGroup,
    clientKey: string,
    clientForm: FormGroup<ClientControls>,
    isMet?: boolean | null,
  ) {
    const config = this.handlerConfigs.get(questionType);

    if (!config) {
      return;
    }

    if (config.type === 'formArray') {
      this.handleFormArray(config.getFormArray(declarationInfo), config.buildForm, isMet);
    } else if (config.type === 'liability') {
      this.handleLiabilityFormArray(
        config.liabilityTypes,
        isMet,
        clientKey,
        questionType,
        config.idArrayRef(clientForm),
      );
    }
  }

  private handleLiabilityFormArray(
    liabilityTypes: LiabilityType[],
    isMet: boolean | null | undefined,
    clientKey: string,
    questionType: DeclarationMainQuestion,
    formArray: FormArray,
  ) {
    const clientId = this.clientCategorizationService.getClientIdFromFormId(clientKey);

    if (!clientId) {
      return;
    }

    const liabilityFormEntries = this.liabilityFormService
      .formMapEntries()
      .filter(
        ([_, form]) =>
          form.value.clientIds?.includes(clientId) &&
          (form.value.declarationQuestion === questionType ||
            (formArray.value as string[]).includes(form.value.id ?? '')),
      );

    if (liabilityFormEntries?.length === 0 && isMet) {
      this.liabilityFormService.addLiability(liabilityTypes[0], null, [clientId], questionType);
    } else if (isMet === false && liabilityFormEntries?.length) {
      liabilityFormEntries.forEach((entry) => {
        const [id] = entry;
        this.liabilityFormService.deleteLiability(id);
      });
    }
  }

  private handleFormArray(
    formArray: FormArray | undefined,
    buildForm: (fb: FormBuilder) => any,
    isMet: boolean | null | undefined,
  ) {
    if (!formArray) {
      return;
    }

    if (!isMet) {
      formArray.clear();
    } else if (isMet && formArray.length === 0) {
      formArray.push(buildForm(this.fb));
    }
  }

  private setupNestedValidators(conditionalTrails: ConditionalTrailQuestion[], clientKey: string) {
    conditionalTrails.forEach((trail) => {
      const sub = trail.condition.isMet$
        .pipe(takeUntilDestroyed(this.destroyRef))
        .subscribe((isMet) => {
          trail.trailQuestions.forEach((question) => {
            const control = question.targetFormControl;
            if (!control) {
              return;
            }

            this.handleFormValidation(control, isMet);
          });
        });

      this.subManager.addPersistentSubscription(clientKey, sub);
    });
  }

  private handleFormValidation(control: AbstractControl, isMet: boolean) {
    if (control instanceof FormGroup) {
      this.handleFormGroupValidation(control, isMet);
    } else if (control instanceof FormControl) {
      this.toggleRequiredValidator(control, isMet);
    }
  }

  private handleFormGroupValidation(formGroup: FormGroup, isMet: boolean) {
    Object.keys(formGroup.controls).forEach((key) => {
      const control = formGroup.get(key);
      if (!control) {
        return;
      }

      this.handleFormValidation(control, isMet);
    });

    formGroup.updateValueAndValidity();
  }

  private toggleRequiredValidator(control: FormControl, isMet: boolean) {
    if (isMet) {
      this.addRequiredValidator(control);
    } else if (isMet === false) {
      this.removeRequiredValidator(control);
      control.setValue(null);
    }
  }

  private addRequiredValidator(control: FormControl) {
    if (control.hasValidator(Validators.required)) {
      return;
    }

    const hadRequiredValidators = this.formValidationMap.get(control);
    if (!hadRequiredValidators) {
      return;
    }

    control.addValidators(Validators.required);
    control.updateValueAndValidity();
  }

  private removeRequiredValidator(control: FormControl) {
    if (!control.validator) {
      return;
    }

    if (control.hasValidator(Validators.required)) {
      this.formValidationMap.set(control, true);
      control.removeValidators(Validators.required);
      control.updateValueAndValidity();
    }
  }
}
