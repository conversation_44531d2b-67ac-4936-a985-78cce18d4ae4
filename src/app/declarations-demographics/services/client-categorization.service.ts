import { computed, effect, inject, Injectable, Signal, untracked } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { EntityStateName } from '../../services/entity-state/abstract-entity-state.service';
import { ClientFormRefService } from '../../services/entity-state/client-state/client-form.service';
import { ClientControls } from '../../services/entity-state/client-state/form-types';
import { getClientName } from '../../util/get-client-name';
import { IClientsViewModel, IClientViewModel } from '../models/view-models/view-models.interface';
import { LeftNavTileStatusService } from './abstract-tile-status.service';

@Injectable()
export class ClientCategorizationFacadeService extends LeftNavTileStatusService<ClientControls> {
  readonly #clientFormService = inject(ClientFormRefService);

  readonly clientForms = computed(() => this.#clientFormService.sortedEntityForms() ?? []);

  readonly #clientInfos = computed<ReadonlyArray<IClientViewModel>>(() =>
    this.#clientFormService.sortedEntityForms()!.map((clientForm) => {
      const clientId = clientForm[0];
      const client = clientForm[1].value;

      const { variant = 'default', icon = '' } = this.presentationMap()[clientId] || {};
      const selectedFormId = this.selectedFormId();
      const selectedClientId = this.getClientIdFromFormId(selectedFormId);
      const shouldDefault =
        (!selectedFormId && client.isPrimaryBorrower) || selectedClientId === client.id;

      return {
        id: client.id!,
        isPrimaryBorrower: client.isPrimaryBorrower!,
        fullName: getClientName(client),
        role: client.isPrimaryBorrower ? 'Primary Client' : 'Co-Client',
        isSelected: shouldDefault,
        clientForm: clientForm[1],
        clientFormId: clientForm[0],
        presentation: {
          variant,
          icon,
        },
      };
    }),
  );

  readonly clients: Signal<Readonly<IClientsViewModel>> = computed(() => {
    return this.categorizeClients(this.#clientInfos());
  });

  readonly selectedClientFormId = computed(() => this.selectedFormId());
  readonly selectedClientForm = computed(
    () => this.selectedForm() ?? ({} as FormGroup<ClientControls>),
  );

  constructor() {
    super();
    effect(() => {
      const clients = this.clientForms();
      untracked(() => {
        const selectedClient =
          clients.find((client) => client[0] === this.selectedFormId()) ??
          clients.find((client) => client[1].value.isPrimaryBorrower) ??
          clients[0];

        if (selectedClient) {
          this.selectedForm.set(selectedClient[1]);
          this.selectedFormId.set(selectedClient[0]);
        }
      });
    });

    effect(() => {
      const errors = this.validationErrorService
        .errors()
        .filter((error) => error.stateName === EntityStateName.Client);
      untracked(() => {
        if (errors) {
          this.handleStates(errors, this.clientForms());
        }
      });
    });
  }

  selectClient(clientFormId: string): void {
    this.selectedFormId.set(clientFormId);
  }

  getClientIdFromFormId(formId: string | undefined): string {
    if (formId) {
      const clientMap = this.clientForms().find((client) => client[0] === formId);
      return clientMap ? clientMap[1].value.id! : '';
    }
    return '';
  }

  private categorizeClients(clients: ReadonlyArray<IClientViewModel>): Readonly<IClientsViewModel> {
    return clients.reduce<IClientsViewModel>(
      (acc, client) => {
        if (client.isPrimaryBorrower) {
          acc = { ...acc, primaryClient: client };
        } else {
          const coClientNumber = acc.coClients.length + 1;
          const updatedClient = {
            ...client,
            role: `${client.role} ${coClientNumber}`,
          };
          acc = {
            ...acc,
            coClients: [...acc.coClients, updatedClient],
          };
        }
        return acc;
      },
      {
        primaryClient: null,
        coClients: [],
      },
    );
  }
}
