import { DestroyRef, Injectable, computed, effect, inject, signal, untracked } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FormControl } from '@angular/forms';
import { Subscription, map } from 'rxjs';
import { QuestionDefinition } from '../models/question/question-definition';
import { QuestionType } from '../models/question/question-type.enum';
import { ClientCategorizationFacadeService } from './client-categorization.service';

export interface SectionControl {
  isValid: boolean;
  subscription: Subscription;
  questionDefinition: QuestionDefinition;
}

@Injectable()
export class DecsDemsCompletionService {
  private destroyRef = inject(DestroyRef);
  private clientCategorizationService = inject(ClientCategorizationFacadeService);
  private previousClientId = signal<string | undefined>(undefined);

  readonly decsDemsSection = signal(new Map<string, SectionControl[]>());

  readonly sectionCompletionStatus = computed(() => {
    const mapSections = this.decsDemsSection();
    const completionStatus = new Map<string, boolean>();

    mapSections.forEach((sectionControls, section) => {
      const isComplete = sectionControls.every((control) => control.isValid);
      completionStatus.set(section, isComplete);
    });

    return completionStatus;
  });

  constructor() {
    effect(() => {
      const currentClientId = this.clientCategorizationService.selectedFormId();

      if (this.previousClientId() && this.previousClientId() !== currentClientId) {
        untracked(() => this.clearSectionsForClient(this.previousClientId()!));
      }

      untracked(() => this.previousClientId.set(currentClientId));
    });
  }

  addSection(sectionKey: string, questionDefinition: QuestionDefinition) {
    const formControl = questionDefinition.targetFormControl as FormControl;

    if (!formControl || this.isControlRegistered(sectionKey, questionDefinition)) {
      return;
    }

    const isAddress = questionDefinition.questionType === QuestionType.Address;
    const initialValidity =
      formControl.valid && (isAddress || (formControl.value != null && formControl.value !== ''));

    const status$ = isAddress
      ? formControl.statusChanges.pipe(map(() => formControl.valid))
      : formControl.valueChanges.pipe(
          map((value) => value != null && value !== '' && formControl.valid),
        );

    const subscription = status$
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((isValid) => this.updateControlStatus(sectionKey, questionDefinition, isValid));

    const sectionControl: SectionControl = {
      isValid: initialValidity,
      subscription,
      questionDefinition,
    };

    const sections = this.decsDemsSection();
    const sectionControls = sections.get(sectionKey) ?? [];
    const updatedSections = new Map(sections);

    updatedSections.set(sectionKey, [...sectionControls, sectionControl]);
    this.decsDemsSection.set(updatedSections);
  }

  removeControl(sectionKey: string, question: QuestionDefinition) {
    const sections = this.decsDemsSection();
    const sectionControls = sections.get(sectionKey) ?? [];

    const controlToRemove = sectionControls.find(
      (section) => section.questionDefinition.targetFormControl === question.targetFormControl,
    );

    if (!controlToRemove) {
      return;
    }

    controlToRemove.subscription.unsubscribe();

    const updatedControls = sectionControls.filter(
      (section) => section.questionDefinition.targetFormControl !== question.targetFormControl,
    );

    if (updatedControls.length === sectionControls.length) {
      return;
    }

    const updatedSections = new Map(sections);
    updatedSections.set(sectionKey, updatedControls);
    this.decsDemsSection.set(updatedSections);
  }

  clearSectionsForClient(clientId: string) {
    if (!clientId) {
      return;
    }

    const sections = this.decsDemsSection();
    const updatedSections = new Map(sections);

    Array.from(sections.keys())
      .filter((key) => key.includes(clientId))
      .forEach((key) => {
        sections.get(key)?.forEach((control) => {
          if (control.subscription) {
            control.subscription.unsubscribe();
          }
        });

        updatedSections.set(key, []);
      });

    this.decsDemsSection.set(updatedSections);
  }

  updateControlStatus(section: string, question: QuestionDefinition, isValid: boolean) {
    const sections = this.decsDemsSection();
    const sectionControls = sections.get(section) ?? [];
    const controlIndex = sectionControls.findIndex(
      (section) => section.questionDefinition === question,
    );

    if (controlIndex === -1) {
      return;
    }

    const updatedControls = [...sectionControls];
    updatedControls[controlIndex] = {
      ...updatedControls[controlIndex],
      isValid,
    };

    const updatedSections = new Map(sections);
    updatedSections.set(section, updatedControls);
    this.decsDemsSection.set(updatedSections);
  }

  private isControlRegistered(section: string, question: QuestionDefinition) {
    const sections = this.decsDemsSection().get(section) ?? [];
    return sections.some(
      (section) => section.questionDefinition.targetFormControl === question.targetFormControl,
    );
  }
}
