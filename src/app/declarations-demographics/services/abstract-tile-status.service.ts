import { Injectable, inject, signal } from '@angular/core';
import { AbstractControl, FormControlStatus, FormGroup } from '@angular/forms';
import {
  ValidationErrorDto,
  ValidationErrorProviderService,
} from '../../services/error/validation-error-provider.service';
import { TileIcon, TileVariant } from '../../tile/tile.component';
import { IPresentation } from '../models/view-models/view-models.interface';

@Injectable()
export abstract class LeftNavTileStatusService<
  T extends { [K in keyof T]: AbstractControl<any, any> },
> {
  readonly validationErrorService = inject(ValidationErrorProviderService);

  readonly selectedFormId = signal<string | undefined>(undefined);
  readonly selectedForm = signal<FormGroup<T> | undefined>(undefined);
  readonly presentationMap = signal<Readonly<Record<string, IPresentation<TileVariant, TileIcon>>>>(
    {},
  );

  private updateStatus(formId: string, formControlStatus: FormControlStatus | 'DEFAULT'): void {
    const { variant, icon } = this.convertFormStatusToPresentation(formControlStatus);
    this.presentationMap.update((map) => ({
      ...map,
      [formId]: { variant, icon },
    }));
  }

  handleStates(validationErrors: ValidationErrorDto[], formMapEntries: [string, FormGroup<any>][]) {
    formMapEntries.map((formEntry) => {
      if (validationErrors.some((error) => error.formId === formEntry[0])) {
        this.updateStatus(formEntry[0], 'INVALID');
      } else {
        this.updateStatus(formEntry[0], 'DEFAULT');
      }
    });
  }

  private convertFormStatusToPresentation(
    formControlStatus: FormControlStatus | 'DEFAULT',
  ): IPresentation<TileVariant, TileIcon> {
    switch (formControlStatus) {
      case 'VALID':
        return { variant: 'success', icon: 'check_circle-two_tone' };
      case 'INVALID':
        return { variant: 'error', icon: 'error-outlined' };
      case 'PENDING':
      case 'DISABLED':
        return { variant: 'info', icon: 'in_progress-two_tone' };
      default:
        return { variant: 'default', icon: '' };
    }
  }
}
