import { computed, DestroyRef, inject, Injectable, signal } from '@angular/core';
import { takeUntilDestroyed, toSignal } from '@angular/core/rxjs-interop';
import { FormArray, FormBuilder, FormControl, FormGroup } from '@angular/forms';
import {
  BankruptcyStatus,
  BankruptcyType,
  CitizenshipResidencyType,
  ClientOccupancyType,
  ClientSellerRelationship,
  FamilyRelationshipType,
  LawsuitCompletionTimeframe,
  LawsuitPartyType,
  LawsuitStatus,
  LiabilityType,
  LoanPurpose,
  PropertyTitleHeldByType,
} from '@rocket-logic/rl-xp-bff-models';
import { PaymentFrequency } from '@rocket-logic/rl-xp-bff-models/dist/enums/payment-frequency';
import { distinctUntilChanged, filter, map, startWith, switchMap, take } from 'rxjs';
import { ClientSubscriptionManager } from '../../services/entity-state/client-state/client-subscription-manager.service';
import { ClientControls } from '../../services/entity-state/client-state/form-types';
import { LiabilityFormService } from '../../services/entity-state/liability-state/liability-form.service';
import { LiabilityStateService } from '../../services/entity-state/liability-state/liability-state.service';
import { SubjectPropertyStateService } from '../../services/entity-state/subject-property-state/subject-property-state.service';
import {
  BankruptcyFormControls,
  buildBankruptcyForm,
  buildCreditApplicationForm,
  buildForeclosureForm,
  buildForfeitureForm,
  buildLawsuitForm,
  buildOtherMortgageForm,
  buildPreForeclosureOrShortSaleForm,
  DeclarationInformationFormGroup,
  ForeclosureFormControls,
  ForfeitureFormControls,
  getBankruptcies,
  getCreditApplications,
  getForeclosures,
  getForfeitures,
  getLawsuits,
  getOtherMortgages,
  getPreForeclosureOrShortSales,
  getPropertyInformation,
  getSellerRelationshipInformation,
  LawsuitFormControls,
  transformFormArray,
} from '../../util/build-declaration-information-form';
import { DeclarationMainQuestion } from '../models/declaration/declaration-main-question.enum';
import {
  DeclarationBanners,
  DeclarationTrailQuestion,
  DeclarationTrails,
} from '../models/declaration/declaration-trail-question.enum';
import {
  Condition,
  ConditionalTrailQuestion,
  ConditionCheckerFunc,
} from '../models/question/conditional-question';
import { mapToSelectOptions, QuestionDefinition } from '../models/question/question-definition';
import { QuestionType } from '../models/question/question-type.enum';
import {
  createLiabilityQuestion,
  createLiabilityTrails,
  formArrayAddFunc,
  formArrayDeleteFunc,
  liabilityFormArrayAddFunc,
  LiabilityTrailBuilderFunc,
  replaceAddressPlaceholder,
} from '../models/question/question-utilities';
import { ClientCategorizationFacadeService } from './client-categorization.service';
import { LiabilityDeclarationsIdListenerService } from './liability-declarations-id-listener.service';

export interface Question {
  question: QuestionDefinition;
  name: DeclarationMainQuestion;
}

@Injectable()
export class DecsAndDemsFormFacadeService {
  private readonly clientCategorizationService = inject(ClientCategorizationFacadeService);
  private readonly liabilityService = inject(LiabilityFormService);
  private readonly subjectPropertyStateService = inject(SubjectPropertyStateService);
  private readonly liabilityStateService = inject(LiabilityStateService);
  private readonly destroyRef = inject(DestroyRef);
  private readonly clientSub = inject(ClientSubscriptionManager);
  private readonly fb = inject(FormBuilder);
  private readonly liabilityDecsIdService = inject(LiabilityDeclarationsIdListenerService);

  readonly clientQuestionMap = signal<Map<string, Question[]>>(new Map());

  readonly liabilityStateEmission$ = this.liabilityStateService.state$.pipe(
    filter((state) => !state.fetching),
    map((state) => state.data),
  );

  addDecsDemsListener(clientForm: FormGroup<ClientControls>, clientKey: string) {
    const questions = this.generateQuestions(clientForm, clientKey);
    this.clientQuestionMap.update((map) => {
      const newState = new Map(map);
      newState.set(clientKey, questions);
      return newState;
    });

    this.setupFormArrayObservers(clientForm, clientKey);
    this.setupLiabilityObservers(clientForm, clientKey);
  }

  removeClientQuestionsFromMap(clientKey: string) {
    this.clientQuestionMap.update((map) => {
      const newMap = new Map(map);
      newMap.delete(clientKey);
      return newMap;
    });
  }

  private setupLiabilityObservers(clientForm: FormGroup<ClientControls>, clientKey: string) {
    const liabilityConfigs: {
      liabilityType: LiabilityType;
      questionName: DeclarationMainQuestion;
      controlName: keyof DeclarationInformationFormGroup['value'];
      generateQuestion: () => Question | null;
    }[] = [
      {
        liabilityType: LiabilityType.MonetaryJudgement,
        questionName: DeclarationMainQuestion.HasOutstandingJudgments,
        controlName: 'hasOutstandingJudgments',
        generateQuestion: () => this.generateJudgmentQuestion(clientForm, clientKey),
      },
      {
        liabilityType: LiabilityType.TaxesOwed,
        questionName: DeclarationMainQuestion.HasDelinquentFederalDebt,
        controlName: 'hasDelinquentFederalDebt',
        generateQuestion: () => this.generateFederalDebtQuestion(clientForm, clientKey),
      },
      // TODO: Seperate into own liability formArray
      {
        liabilityType: LiabilityType.MiscellaneousInstallment,
        questionName: DeclarationMainQuestion.IsBorrowingMoney,
        controlName: 'hasBorrowedFundsNotPreviouslyDisclosed',
        generateQuestion: () => this.generateBorrowingMoneyQuestion(clientForm, clientKey),
      },
      {
        liabilityType: LiabilityType.MiscellaneousInstallment,
        questionName: DeclarationMainQuestion.IsCoSignerOrGuarantor,
        controlName: 'isCosignerOrGuarantorOnDebtsNotPreviouslyDisclosed',
        generateQuestion: () => this.generateCoSignerQuestion(clientForm, clientKey),
      },
      {
        liabilityType: LiabilityType.Alimony,
        questionName: DeclarationMainQuestion.IsPayingAlimonyOrChildSupport,
        controlName: 'hasChildSupportOrAlimony',
        generateQuestion: () => this.generateAlimoneyChildSupportQuestion(clientForm, clientKey),
      },
    ];

    liabilityConfigs.forEach((config) => {
      const control = clientForm.controls.declarationInformation?.get(config.controlName);

      if (!control) {
        return;
      }

      const sub = this.liabilityStateEmission$
        .pipe(
          take(1),
          switchMap(() => {
            return control.valueChanges.pipe(
              startWith(control.value),
              distinctUntilChanged(),
              takeUntilDestroyed(this.destroyRef),
            );
          }),
        )
        .subscribe(() => {
          const question = config.generateQuestion();
          if (!question) {
            return;
          }

          this.updateClientQuestionMap(clientKey, config.questionName, question);
        });

      this.clientSub.addSubscription(clientKey, sub);
    });

    const liabilityStateSub = this.liabilityService.formMapEntries$
      .pipe(
        map((formMapEntries) => formMapEntries.length),
        distinctUntilChanged(),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe(() => {
        liabilityConfigs.forEach((config) => {
          const question = config.generateQuestion();
          if (!question) {
            return;
          }

          this.updateClientQuestionMap(clientKey, config.questionName, question);
        });
      });

    this.clientSub.addSubscription(clientKey, liabilityStateSub);
  }

  private updateClientQuestionMap(
    clientKey: string,
    questionName: DeclarationMainQuestion,
    question: Question,
  ): void {
    this.clientQuestionMap.update((map) => {
      const newState = new Map(map);
      const currentQuestions = newState.get(clientKey) ?? [];
      const questionIndex = currentQuestions.findIndex((q) => q.name === questionName);
      const newQuestions = [...currentQuestions];

      if (questionIndex >= 0) {
        newQuestions[questionIndex] = question;
      } else {
        newQuestions.push(question);
      }

      newState.set(clientKey, newQuestions);
      return newState;
    });
  }

  private setupFormArrayObservers(clientForm: FormGroup<ClientControls>, clientKey: string) {
    const formArrayConfigs = [
      {
        getFormArray: (clientForm: FormGroup<ClientControls>) =>
          getBankruptcies(clientForm.controls.declarationInformation),
        generateQuestion: () => this.generateBankruptcyQuestions(clientForm),
      },
      {
        getFormArray: (clientForm: FormGroup<ClientControls>) =>
          getForeclosures(clientForm.controls.declarationInformation),
        generateQuestion: () => this.generateForeclosureQuestions(clientForm),
      },
      {
        getFormArray: (clientForm: FormGroup<ClientControls>) =>
          getLawsuits(clientForm.controls.declarationInformation),
        generateQuestion: () => this.generateLawsuitQuestions(clientForm),
      },
      {
        getFormArray: (clientForm: FormGroup<ClientControls>) =>
          getForfeitures(clientForm.controls.declarationInformation),
        generateQuestion: () => this.generateForfeitedTitleQuestions(clientForm),
      },
      {
        getFormArray: (clientForm: FormGroup<ClientControls>) =>
          getPreForeclosureOrShortSales(clientForm.controls.declarationInformation),
        generateQuestion: () => this.generatePreForeclosureOrShortSaleQuestions(clientForm),
      },
      {
        getFormArray: (clientForm: FormGroup<ClientControls>) =>
          getOtherMortgages(clientForm.controls.declarationInformation),
        generateQuestion: () => this.generateOtherMortgageQuestions(clientForm),
      },
      {
        getFormArray: (clientForm: FormGroup<ClientControls>) =>
          getCreditApplications(clientForm.controls.declarationInformation),
        generateQuestion: () => this.generateCreditApplicationQuestions(clientForm),
      },
    ];

    formArrayConfigs.forEach((config) => {
      const formArray = config.getFormArray(clientForm) as FormArray;
      if (!formArray) {
        return;
      }

      const sub = formArray.valueChanges
        .pipe(
          startWith(formArray),
          map((array: FormArray) => array.length),
          distinctUntilChanged(),
          takeUntilDestroyed(this.destroyRef),
        )
        .subscribe(() => {
          const question = config.generateQuestion();
          if (!question) {
            return;
          }

          this.clientQuestionMap.update((map) => {
            const newState = new Map(map);
            const currentQuestions = newState.get(clientKey) ?? [];
            const questionIndex = currentQuestions.findIndex((q) => q.name === question.name);
            const newQuestions = [...currentQuestions];

            if (questionIndex >= 0) {
              newQuestions[questionIndex] = question;
            } else {
              newQuestions.push(question);
            }

            newState.set(clientKey, newQuestions);
            return newState;
          });
        });

      this.clientSub.addSubscription(clientKey, sub);
    });
  }

  private generateQuestions(clientForm: FormGroup<ClientControls>, clientKey: string) {
    return [
      this.generateJudgmentQuestion(clientForm, clientKey),
      this.generateBankruptcyQuestions(clientForm),
      this.generateForeclosureQuestions(clientForm),
      this.generateLawsuitQuestions(clientForm),
      this.generateForfeitedTitleQuestions(clientForm),
      this.generatePreForeclosureOrShortSaleQuestions(clientForm),
      this.generateFederalDebtQuestion(clientForm, clientKey),
      this.generateAlimoneyChildSupportQuestion(clientForm, clientKey),
      this.generateBorrowingMoneyQuestion(clientForm, clientKey),
      this.generateCoSignerQuestion(clientForm, clientKey),
      this.generateCitizenshipResidencyTypeQuestion(clientForm),
      this.generatePrimaryResidenceQuestion(clientForm),
      this.generateOtherMortgageQuestions(clientForm),
      this.generateCreditApplicationQuestions(clientForm),
      this.generateSubjectToAPriorityLienQuestion(clientForm),
      this.sellerRelationshipInformation(clientForm),
      this.loansInForbearanceQuestion(clientForm),
    ].filter((value): value is Question => !!value);
  }

  private readonly subjectProperty = toSignal(this.subjectPropertyStateService.nonNullableData$);
  private readonly subjectPropertyAddress = computed(() => this.subjectProperty()?.address);

  private buildHasForfeitedTitleTrailQuestions(
    condition: Condition,
    forfeitureForm: FormGroup<ForfeitureFormControls>,
    parentFormArray: FormArray,
  ): ConditionalTrailQuestion {
    const controls = forfeitureForm.controls;

    const trails = [
      new QuestionDefinition(
        QuestionType.Date,
        controls.propertyForfeitedDate as FormControl<string>,
        signal(DeclarationTrailQuestion.PropertyForfeitedDate),
        'Forfeited Date',
      ),
      new QuestionDefinition(
        QuestionType.Address,
        controls.propertyAddress,
        signal(DeclarationTrailQuestion.PropertyForfeitedAddress),
      ),
      new QuestionDefinition(
        QuestionType.YesNo,
        controls.propertyHasMortgageLender as FormControl<boolean>,
        signal(DeclarationTrailQuestion.PropertyForfeitedHasMortgageLender),
      ).withConditionalTrail(
        new Condition(controls.propertyHasMortgageLender),
        new QuestionDefinition(
          QuestionType.Text,
          controls.propertyMortgageLender as FormControl<string>,
          signal(DeclarationTrailQuestion.WhoWasTheLender),
          'Mortgage Lender',
        ),
      ),
    ];

    return new ConditionalTrailQuestion(condition, trails, {
      sectionTitle: 'Forfeited Title',
    }).attachDeleteFunc(formArrayDeleteFunc(parentFormArray, forfeitureForm));
  }

  private generateBankruptcyQuestions(clientForm: FormGroup<ClientControls>) {
    const declarationInformation = clientForm.controls.declarationInformation;
    const control = declarationInformation?.controls.hasDeclaredBankruptcy;
    if (!control) {
      return null;
    }

    const bankruptcies = getBankruptcies(declarationInformation);
    const trails = transformFormArray(bankruptcies, (bankruptcy) =>
      this.buildBankruptcyTrailQuestions(new Condition(control), bankruptcy, bankruptcies),
    );

    const question = new QuestionDefinition(
      QuestionType.YesNo,
      control,
      signal(DeclarationMainQuestion.HasRecentlyDeclaredBankruptcy),
    );

    question.addConditionalTrails(...trails);
    question.parentFormArray = bankruptcies;
    question.addFunc = formArrayAddFunc(bankruptcies, () => buildBankruptcyForm(this.fb));

    return { question, name: DeclarationMainQuestion.HasRecentlyDeclaredBankruptcy };
  }

  private generateForeclosureQuestions(clientForm: FormGroup<ClientControls>) {
    const declarationInformation = clientForm.controls.declarationInformation;
    const control = declarationInformation?.controls.hasHadForeclosure;

    if (!control) {
      return null;
    }

    const foreclosures = getForeclosures(declarationInformation);
    const trails = transformFormArray(foreclosures, (foreclosure) =>
      this.buildForeclosuresTrailQuestions(new Condition(control), foreclosure, foreclosures),
    );

    const question = new QuestionDefinition(
      QuestionType.YesNo,
      control,
      signal(DeclarationMainQuestion.HasRecentlyHadForeclosure),
    );

    question.addConditionalTrails(...trails);
    question.parentFormArray = foreclosures;
    question.addFunc = formArrayAddFunc(foreclosures, () => buildForeclosureForm(this.fb));

    return { question, name: DeclarationMainQuestion.HasRecentlyHadForeclosure };
  }

  private generateLawsuitQuestions(clientForm: FormGroup<ClientControls>) {
    const declarationInformation = clientForm.controls.declarationInformation;
    const control = declarationInformation?.controls.isCurrentlyPartyToLawsuit;

    if (!control) {
      return null;
    }

    const lawsuits = getLawsuits(declarationInformation);
    const trails = transformFormArray(lawsuits, (lawsuit) =>
      this.buildLawsuitTrailQuestions(new Condition(control), lawsuit, lawsuits),
    );

    const question = new QuestionDefinition(
      QuestionType.YesNo,
      control,
      signal(DeclarationMainQuestion.IsCurrentlyPartyToLawsuit),
    );

    question.addConditionalTrails(...trails);
    question.parentFormArray = lawsuits;
    question.addFunc = formArrayAddFunc(lawsuits, () => buildLawsuitForm(this.fb));

    return { question, name: DeclarationMainQuestion.IsCurrentlyPartyToLawsuit };
  }

  private generateForfeitedTitleQuestions(clientForm: FormGroup<ClientControls>) {
    const declarationInformation = clientForm.controls.declarationInformation;
    const control = declarationInformation?.controls.hasConveyedPropertyAsDeedInLieuOfForeclosure;

    if (!control) {
      return null;
    }

    const forfeitures = getForfeitures(declarationInformation);

    if (!forfeitures) {
      return null;
    }

    const trails = transformFormArray(forfeitures, (forfeiture) =>
      this.buildHasForfeitedTitleTrailQuestions(new Condition(control), forfeiture, forfeitures),
    );

    const question = new QuestionDefinition(
      QuestionType.YesNo,
      control,
      signal(DeclarationMainQuestion.HasForfeitedTitle),
    );

    question.addConditionalTrails(...trails);
    question.parentFormArray = forfeitures;
    question.addFunc = formArrayAddFunc(forfeitures, () => buildForfeitureForm(this.fb));

    return { question, name: DeclarationMainQuestion.HasForfeitedTitle };
  }

  private generatePreForeclosureOrShortSaleQuestions(clientForm: FormGroup<ClientControls>) {
    const declarationInformation = clientForm.controls.declarationInformation;
    const control = declarationInformation?.controls.hasHadPreForeclosureOrShortSale;

    if (!control) {
      return null;
    }

    const preForclosureForms = getPreForeclosureOrShortSales(declarationInformation);
    const trails = transformFormArray(preForclosureForms, (preForeclosureOrShortSale) => {
      return new ConditionalTrailQuestion(
        new Condition(control),
        [
          new QuestionDefinition(
            QuestionType.Date,
            preForeclosureOrShortSale.controls.saleDate,
            signal(DeclarationTrailQuestion.PreForeclosureSaleDate),
            'Forfeited Date',
          ),
          new QuestionDefinition(
            QuestionType.Address,
            preForeclosureOrShortSale.controls.propertyAddress,
            signal(DeclarationTrailQuestion.PreForeclosureProperty),
          ),
          new QuestionDefinition(
            QuestionType.Text,
            preForeclosureOrShortSale.controls.mortgageLender,
            signal(DeclarationTrailQuestion.WhoWasTheLender),
            'Mortgage Lender',
          ),
        ],
        { sectionTitle: 'Pre-Foreclosure Sale or Short Sale' },
      ).attachDeleteFunc(formArrayDeleteFunc(preForclosureForms, preForeclosureOrShortSale));
    });

    const question = new QuestionDefinition(
      QuestionType.YesNo,
      control,
      signal(DeclarationMainQuestion.HasRecentlyHadPreForeclosureOrShortSale),
    );

    question.addConditionalTrails(...trails);
    question.parentFormArray = preForclosureForms;
    question.addFunc = formArrayAddFunc(preForclosureForms, () =>
      buildPreForeclosureOrShortSaleForm(this.fb),
    );

    return { question, name: DeclarationMainQuestion.HasRecentlyHadPreForeclosureOrShortSale };
  }

  private generateOtherMortgageQuestions(clientForm: FormGroup<ClientControls>) {
    const declarationInformation = clientForm.controls.declarationInformation;
    const control = declarationInformation?.controls.hasMortgageApplicationNotPreviouslyDisclosed;

    if (!control) {
      return null;
    }

    const otherMortgages = getOtherMortgages(declarationInformation);

    const trails = transformFormArray(otherMortgages, (otherMortgage) => {
      return new ConditionalTrailQuestion(
        new Condition(control),
        [
          new QuestionDefinition(
            QuestionType.Address,
            otherMortgage.controls.address,
            signal(DeclarationTrailQuestion.ApplyingForAnotherMortgagePropertyAddress),
          ),
          new QuestionDefinition(
            QuestionType.Chip,
            otherMortgage.controls.loanPurpose,
            signal(DeclarationTrailQuestion.ApplyingForAnotherMortgagePurpose),
          ).withSelectOptions([
            { value: LoanPurpose.Purchase, label: 'Purchase' },
            { value: LoanPurpose.Refinance, label: 'Refinance' },
          ]),
          new QuestionDefinition(
            QuestionType.Text,
            otherMortgage.controls.mortgageLender,
            signal(DeclarationTrailQuestion.ApplyingForAnotherMortgageLender),
            'Mortgage Lender',
          ),
          new QuestionDefinition(
            QuestionType.Currency,
            otherMortgage.controls.mortgageBalance,
            signal(DeclarationTrailQuestion.ApplyingForAnotherMortgageBalance),
            'Mortgage Balance',
          ),
          new QuestionDefinition(
            QuestionType.PaymentPlan,
            otherMortgage.controls.paymentPlan,
            signal(DeclarationTrailQuestion.PaymentAmount),
          ).withSelectOptions(mapToSelectOptions(PaymentFrequency)),
        ],
        { sectionTitle: 'Mortgage' },
      ).attachDeleteFunc(formArrayDeleteFunc(otherMortgages, otherMortgage));
    });

    const question = new QuestionDefinition(
      QuestionType.YesNo,
      control,
      computed(() =>
        replaceAddressPlaceholder(
          DeclarationMainQuestion.ApplyingForAnotherMortgage,
          this.subjectPropertyAddress(),
        ),
      ),
    );

    question.addConditionalTrails(...trails);
    question.parentFormArray = otherMortgages;
    question.addFunc = formArrayAddFunc(otherMortgages, () => buildOtherMortgageForm(this.fb));

    return { question, name: DeclarationMainQuestion.ApplyingForAnotherMortgage };
  }

  private generateCreditApplicationQuestions(clientForm: FormGroup<ClientControls>) {
    const declarationInformation = clientForm.controls.declarationInformation;
    const control = declarationInformation?.controls.hasCreditApplicationNotPreviouslyDisclosed;

    if (!control) {
      return null;
    }

    const creditApps = getCreditApplications(declarationInformation);

    const trails = transformFormArray(creditApps, (creditApplication) => {
      return new ConditionalTrailQuestion(
        new Condition(control),
        [
          new QuestionDefinition(
            QuestionType.Text,
            creditApplication.controls.accountLender,
            signal(DeclarationTrailQuestion.NewCreditLender),
            'Account Lender',
          ),
          new QuestionDefinition(
            QuestionType.Currency,
            creditApplication.controls.accountBalance,
            signal(DeclarationTrailQuestion.NewCreditOwedAmount),
            'Account Balance',
          ),
          new QuestionDefinition(
            QuestionType.PaymentPlan,
            creditApplication.controls.paymentPlan,
            signal(DeclarationTrailQuestion.PaymentAmount),
          ).withSelectOptions(mapToSelectOptions(PaymentFrequency)),
        ],
        { sectionTitle: 'Credit Account' },
      ).attachDeleteFunc(formArrayDeleteFunc(creditApps, creditApplication));
    });

    const question = new QuestionDefinition(
      QuestionType.YesNo,
      control,
      computed(() =>
        replaceAddressPlaceholder(
          DeclarationMainQuestion.ApplyingForNewCreditAccount,
          this.subjectPropertyAddress(),
        ),
      ),
    );

    question.addConditionalTrails(...trails);
    question.parentFormArray = creditApps;
    question.addFunc = formArrayAddFunc(creditApps, () => buildCreditApplicationForm(this.fb));

    return { question, name: DeclarationMainQuestion.ApplyingForNewCreditAccount };
  }

  private buildBankruptcyTrailQuestions(
    condition: Condition,
    bankruptcyForm: FormGroup<BankruptcyFormControls>,
    parentFormArray?: FormArray,
  ): ConditionalTrailQuestion {
    const controls = bankruptcyForm.controls;

    const trails = [
      new QuestionDefinition(
        QuestionType.Select,
        controls.type,
        signal(DeclarationTrailQuestion.BankruptcyType),
        'Type',
      ).withSelectOptions(mapToSelectOptions(BankruptcyType)),
      new QuestionDefinition(
        QuestionType.Select,
        controls.status,
        signal(DeclarationTrailQuestion.BankruptcyStatus),
        'Status',
      )
        .withSelectOptions(mapToSelectOptions(BankruptcyStatus))
        .withConditionalTrail(
          new Condition(
            controls.status,
            (status: BankruptcyStatus) =>
              status === BankruptcyStatus.Discharged || status === BankruptcyStatus.Dismissed,
          ),
          new QuestionDefinition(
            QuestionType.Date,
            controls.dismissedOrDischargedDate,
            signal(DeclarationTrailQuestion.BankruptcyDoneDate),
          ),
        ),
      new QuestionDefinition(
        QuestionType.YesNo,
        controls.hasPropertyIncludedInBankruptcy,
        signal(DeclarationTrailQuestion.BankruptcyIsPropertyIncluded),
      ).withConditionalTrail(
        new Condition(controls.hasPropertyIncludedInBankruptcy),
        new QuestionDefinition(
          QuestionType.Address,
          controls.propertyAddress,
          signal(DeclarationTrailQuestion.BankruptcyPropertyAddress),
        ),
      ),
      new QuestionDefinition(
        QuestionType.YesNo,
        controls.propertyHasMortgageLender,
        signal(DeclarationTrailQuestion.BankruptcyHasFinancing),
      ).withConditionalTrail(
        new Condition(controls.propertyHasMortgageLender),
        new QuestionDefinition(
          QuestionType.Text,
          controls.propertyMortgageLender,
          signal(DeclarationTrailQuestion.WhoWasTheLender),
          'Mortgage Lender',
        ),
      ),
    ];

    return new ConditionalTrailQuestion(condition, trails, {
      sectionTitle: 'Bankruptcy',
    }).attachDeleteFunc(formArrayDeleteFunc(parentFormArray, bankruptcyForm));
  }

  private buildForeclosuresTrailQuestions(
    condition: Condition,
    foreClosureForm: FormGroup<ForeclosureFormControls>,
    parentFormArray?: FormArray,
  ): ConditionalTrailQuestion {
    const controls = foreClosureForm.controls;

    const trails = [
      new QuestionDefinition(
        QuestionType.Date,
        controls.foreclosureDate,
        signal(DeclarationTrailQuestion.ForeclosureDate),
        'Finalized Date',
      ),
      new QuestionDefinition(
        QuestionType.Address,
        controls.propertyAddress,
        signal(DeclarationTrailQuestion.ForeclosureAddress),
      ),
      new QuestionDefinition(
        QuestionType.YesNo,
        controls.propertyHasMortgageLender,
        signal(DeclarationTrailQuestion.ForeclosureHasFinancing),
      ).withConditionalTrail(
        new Condition(controls.propertyHasMortgageLender),
        new QuestionDefinition(
          QuestionType.Text,
          controls.propertyMortgageLender,
          signal(DeclarationTrailQuestion.WhoWasTheLender),
          'Mortgage Lender',
        ),
      ),
    ];

    return new ConditionalTrailQuestion(condition, trails, {
      sectionTitle: 'Foreclosure',
    }).attachDeleteFunc(formArrayDeleteFunc(parentFormArray, foreClosureForm));
  }

  private buildLawsuitTrailQuestions(
    condition: Condition,
    lawsuitForm: FormGroup<LawsuitFormControls>,
    parentFormArray: FormArray,
  ): ConditionalTrailQuestion {
    const controls = lawsuitForm.controls;
    const lawsuitCompletionSelectOptions = [
      { value: LawsuitCompletionTimeframe.PriorToClosing, label: 'Yes' },
      {
        value: LawsuitCompletionTimeframe.NotPriorToClosing,
        label: 'No',
      },
      {
        value: LawsuitCompletionTimeframe.Unknown,
        label: "Don't Know",
      },
    ];

    const maxPenaltyAmountQuestion = new QuestionDefinition(
      QuestionType.Currency,
      controls.maxPenaltyAmount,
      signal(DeclarationTrailQuestion.LawsuitMaximumPenalty),
    );

    const onGoingLawsuitTrailQuestion = new QuestionDefinition(
      QuestionType.Chip,
      controls.completionTime,
      signal(DeclarationTrailQuestion.LawsuitCompletionBeforeClosing),
    )
      .withSelectOptions(lawsuitCompletionSelectOptions)
      .withConditionalTrail(
        new Condition(
          controls.completionTime,
          (value: LawsuitCompletionTimeframe) =>
            value === LawsuitCompletionTimeframe.PriorToClosing,
        ),
        new QuestionDefinition(
          QuestionType.Currency,
          controls.maxPenaltyAmount,
          signal(DeclarationTrailQuestion.LawsuitMaximumPenalty),
        ),
        maxPenaltyAmountQuestion,
      );

    const defendantTrailQuestion = new QuestionDefinition(
      QuestionType.Chip,
      controls.status,
      signal(DeclarationTrailQuestion.LawsuitStatus),
    )
      .withSelectOptions(mapToSelectOptions(LawsuitStatus))
      .withConditionalTrail(
        new Condition(controls.status, (value: LawsuitStatus) => value === LawsuitStatus.Ongoing),
        onGoingLawsuitTrailQuestion,
      )
      .withConditionalTrail(
        new Condition(controls.status, (value: LawsuitStatus) => value === LawsuitStatus.Complete),
        maxPenaltyAmountQuestion,
      );

    const trails = [
      new QuestionDefinition(
        QuestionType.Chip,
        controls.party,
        signal(DeclarationTrailQuestion.LawsuitRole),
      )
        .withSelectOptions(mapToSelectOptions(LawsuitPartyType))
        .withConditionalTrail(
          new Condition(
            controls.party,
            (value: LawsuitPartyType) => value === LawsuitPartyType.Defendant,
          ),
          defendantTrailQuestion,
        ),
    ];

    const conditionalQuestion = new ConditionalTrailQuestion(condition, trails, {
      sectionTitle: 'Lawsuit',
    });
    conditionalQuestion.deleteFunc = formArrayDeleteFunc(parentFormArray, lawsuitForm);

    return conditionalQuestion;
  }

  private generateCitizenshipResidencyTypeQuestion(clientForm: FormGroup<ClientControls>) {
    const declarationInformation = clientForm.controls.declarationInformation;
    const control = declarationInformation?.controls.citizenshipResidencyType;
    if (!control) {
      return null;
    }

    const question = new QuestionDefinition(
      QuestionType.Select,
      control,
      signal(DeclarationMainQuestion.IsUSCitizen),
      'Citizenship',
    );

    question.options = [
      { value: CitizenshipResidencyType.USCitizen, label: 'US Citizen' },
      {
        value: CitizenshipResidencyType.PermanentResidentAlien,
        label: 'Permanent Resident Alien',
      },
      {
        value: CitizenshipResidencyType.NonPermanentResidentAlien,
        label: 'Non-Permanent Resident Alien',
      },
    ];

    return { question, name: DeclarationMainQuestion.IsUSCitizen };
  }

  private generatePrimaryResidenceQuestion(clientForm: FormGroup<ClientControls>) {
    const declarationInformation = clientForm.controls.declarationInformation;
    const control = declarationInformation?.controls.intendsToOccupyPropertyAsPrimaryResidence;

    if (!control) {
      return null;
    }

    const propertyInformation = getPropertyInformation(declarationInformation);

    const trail = new ConditionalTrailQuestion(
      new Condition(control, (value: boolean) => value != null),
      [
        new QuestionDefinition(
          QuestionType.YesNo,
          propertyInformation.hasOwnedPropertyInPastThreeYears,
          computed(() =>
            replaceAddressPlaceholder(
              DeclarationTrailQuestion.PrimaryHasOwnershipInterestLastThreeYears,
              this.subjectPropertyAddress(),
            ),
          ),
        ).withConditionalTrail(
          new Condition(
            propertyInformation.hasOwnedPropertyInPastThreeYears,
            (value: boolean) => value === true,
          ),
          new QuestionDefinition(
            QuestionType.Select,
            propertyInformation.previousPropertyOccupancyType,
            signal(DeclarationTrailQuestion.PrimaryType),
            'Property Type',
          ).withSelectOptions(mapToSelectOptions(ClientOccupancyType)),
          new QuestionDefinition(
            QuestionType.Select,
            propertyInformation.previousPropertyTitleHeldBy,
            signal(DeclarationTrailQuestion.PrimaryTitleHolding),
            'Title Held By',
          ).withSelectOptions(mapToSelectOptions(PropertyTitleHeldByType)),
        ),
      ],
    );

    const question = new QuestionDefinition(
      QuestionType.YesNo,
      control,
      computed(() =>
        replaceAddressPlaceholder(
          DeclarationMainQuestion.IsPrimaryResidence,
          this.subjectPropertyAddress(),
        ),
      ),
    );

    question.addConditionalTrails(trail);

    return { question, name: DeclarationMainQuestion.IsPrimaryResidence };
  }

  private generateSubjectToAPriorityLienQuestion(clientForm: FormGroup<ClientControls>) {
    const declarationInformation = clientForm.controls.declarationInformation;
    const control = declarationInformation?.controls.propertyIsSubjectToPriorityLien;

    if (!control) {
      return null;
    }

    const question = new QuestionDefinition(
      QuestionType.YesNo,
      control,
      computed(() =>
        replaceAddressPlaceholder(
          DeclarationMainQuestion.NewLienDetails,
          this.subjectPropertyAddress(),
        ),
      ),
    );

    return { question: question, name: DeclarationMainQuestion.NewLienDetails };
  }

  private sellerRelationshipInformation(clientForm: FormGroup<ClientControls>) {
    const declarationInformation = clientForm.controls.declarationInformation;
    const control = declarationInformation?.controls.hasRelationshipOrBusinessAffiliationWithSeller;

    if (!control) {
      return null;
    }

    const sellerRelationshipInformation = getSellerRelationshipInformation(declarationInformation);
    const relationshipConditionCheckerFunc: ConditionCheckerFunc<ClientSellerRelationship> = (
      value,
    ) => value === ClientSellerRelationship.Family;

    const trail = new ConditionalTrailQuestion(new Condition(control), [
      new QuestionDefinition(
        QuestionType.Select,
        sellerRelationshipInformation.relationshipWithSeller,
        signal(DeclarationTrailQuestion.RelationshipWithSeller),
        'Relationship With Seller',
      )
        .withSelectOptions(mapToSelectOptions(ClientSellerRelationship))
        .withConditionalTrail(
          new Condition(
            sellerRelationshipInformation.relationshipWithSeller,
            relationshipConditionCheckerFunc,
          ),
          new QuestionDefinition(
            QuestionType.Select,
            sellerRelationshipInformation.familyRelationshipType,
            signal(DeclarationTrailQuestion.RelationshipWithSellerFamilyRelationship),
            'Family Relationship',
          ).withSelectOptions(mapToSelectOptions(FamilyRelationshipType)),
        ),
    ]);

    const question = new QuestionDefinition(
      QuestionType.YesNo,
      control,
      signal(DeclarationMainQuestion.RelationshipWithSeller),
    );

    question.addConditionalTrails(trail);

    return { question, name: DeclarationMainQuestion.RelationshipWithSeller };
  }

  private loansInForbearanceQuestion(clientForm: FormGroup<ClientControls>) {
    const declarationInformation = clientForm.controls.declarationInformation;
    const control = declarationInformation?.controls.hasHadForbearance;

    if (!control) {
      return null;
    }

    const trail = new ConditionalTrailQuestion(new Condition(control), [
      new QuestionDefinition(
        QuestionType.HTML,
        null,
        signal(DeclarationTrails.HasLoansInForbearance.ConfirmationText),
      ),
    ]);

    const question = new QuestionDefinition(
      QuestionType.YesNo,
      control,
      signal(DeclarationMainQuestion.HasLoansInForbearance),
    );

    question.addConditionalTrails(trail);

    return { question, name: DeclarationMainQuestion.HasLoansInForbearance };
  }

  private generateJudgmentQuestion(clientForm: FormGroup<ClientControls>, clientKey: string) {
    const leadControl = clientForm.controls.declarationInformation.controls.hasOutstandingJudgments;
    const clientId = this.clientCategorizationService.getClientIdFromFormId(clientKey);
    const judgementIds =
      clientForm.controls.declarationInformation.controls.outstandingJudgmentLiabilityIds;

    const liabilityGroups = this.liabilityService.getLiabilityFormGroups(
      clientId,
      judgementIds.value,
      DeclarationMainQuestion.HasOutstandingJudgments,
    );

    const judgementLiablityIds =
      clientForm.controls.declarationInformation.controls.outstandingJudgmentLiabilityIds;

    const trailBuilderFunc: LiabilityTrailBuilderFunc = (liabilityFormGroup) =>
      createLiabilityTrails(
        leadControl,
        liabilityFormGroup,
        DeclarationTrailQuestion.JudgmentPaymentPlan,
        (liabilityFormGroup) => [
          new QuestionDefinition(
            QuestionType.Text,
            liabilityFormGroup.form.controls.creditorName,
            signal(DeclarationTrailQuestion.JudgmentHolder),
            'Judgment Holder',
          ),
          new QuestionDefinition(
            QuestionType.Currency,
            liabilityFormGroup.form.controls.unpaidBalance,
            signal(DeclarationTrailQuestion.JudgmentAmount),
          ),
        ],
        {
          sectionTitle: 'Judgment',
          bannerText: DeclarationBanners.CreditQualifier,
        },
        this.liabilityService,
        this.liabilityDecsIdService,
        judgementLiablityIds,
        false,
        clientId,
      );

    const liabilityQuestion = createLiabilityQuestion(
      liabilityGroups,
      leadControl,
      DeclarationMainQuestion.HasOutstandingJudgments,
      trailBuilderFunc,
    );

    if (!liabilityQuestion) {
      return null;
    }

    liabilityQuestion.addFunc = liabilityFormArrayAddFunc(
      clientId,
      this.liabilityService,
      LiabilityType.MonetaryJudgement,
      DeclarationMainQuestion.HasOutstandingJudgments,
    );
    liabilityQuestion.parentFormArray = this.liabilityService.formArray;

    return { question: liabilityQuestion, name: DeclarationMainQuestion.HasOutstandingJudgments };
  }

  private generateBorrowingMoneyQuestion(clientForm: FormGroup<ClientControls>, clientKey: string) {
    const leadControl =
      clientForm.controls.declarationInformation?.controls.hasBorrowedFundsNotPreviouslyDisclosed;
    const clientId = this.clientCategorizationService.getClientIdFromFormId(clientKey);
    const borrowedFundsIds =
      clientForm.controls.declarationInformation.controls.borrowedFundsLiabilityIds;

    const liabilityGroups = this.liabilityService.getLiabilityFormGroups(
      clientId,
      borrowedFundsIds.value,
      DeclarationMainQuestion.IsBorrowingMoney,
    );

    const trailBuilderFunc: LiabilityTrailBuilderFunc = (liabilityFormGroup) => {
      const trails = createLiabilityTrails(
        leadControl,
        liabilityFormGroup,
        DeclarationTrailQuestion.BorrowingMoneyMoneyOnPaymentPlan,
        (liabilityFormGroup) => [
          new QuestionDefinition(
            QuestionType.Text,
            liabilityFormGroup.form.controls.creditorName,
            signal(DeclarationTrailQuestion.BorrowingMoneyFrom),
            'Name',
          ),
          new QuestionDefinition(
            QuestionType.Currency,
            liabilityFormGroup.form.controls.unpaidBalance,
            signal(DeclarationTrailQuestion.BorrowingMoneyAmount),
          ),
        ],
        {
          sectionTitle: 'Money Borrowed',
          bannerText: DeclarationBanners.CreditQualifier,
        },
        this.liabilityService,
        this.liabilityDecsIdService,
        borrowedFundsIds,
        false,
        clientId,
      );

      return trails;
    };

    const liabilityQuestion = createLiabilityQuestion(
      liabilityGroups,
      leadControl,
      DeclarationMainQuestion.IsBorrowingMoney,
      trailBuilderFunc,
    );

    if (!liabilityQuestion) {
      return null;
    }

    liabilityQuestion.addFunc = liabilityFormArrayAddFunc(
      clientId,
      this.liabilityService,
      LiabilityType.MiscellaneousInstallment,
      DeclarationMainQuestion.IsBorrowingMoney,
    );
    liabilityQuestion.parentFormArray = this.liabilityService.formArray;

    return { question: liabilityQuestion, name: DeclarationMainQuestion.IsBorrowingMoney };
  }

  private generateFederalDebtQuestion(clientForm: FormGroup<ClientControls>, clientKey: string) {
    const leadControl =
      clientForm.controls.declarationInformation?.controls.hasDelinquentFederalDebt;
    const clientId = this.clientCategorizationService.getClientIdFromFormId(clientKey);

    const federalDebtIds =
      clientForm.controls.declarationInformation.controls.delinquentFederalDebtLiabilityIds;

    const liabilityGroups = this.liabilityService.getLiabilityFormGroups(
      clientId,
      federalDebtIds.value,
      DeclarationMainQuestion.HasDelinquentFederalDebt,
    );

    liabilityGroups.forEach(({ form }) => {
      if (!form.value.creditorName) {
        form.controls.creditorName.setValue('Federal Debt', { emitEvent: false });
      }
    });

    const trailBuilderFunc: LiabilityTrailBuilderFunc = (liabilityFormGroup) =>
      createLiabilityTrails(
        leadControl,
        liabilityFormGroup,
        DeclarationTrailQuestion.DelinquentFederalIsOnDebtPaymentPlan,
        (liabilityFormGroup) => {
          const liabilityTypeConditionCheckerFunc: ConditionCheckerFunc<LiabilityType> = (value) =>
            value === LiabilityType.TaxesOwed;

          return [
            new QuestionDefinition(
              QuestionType.Currency,
              liabilityFormGroup.form.controls.unpaidBalance,
              signal(DeclarationTrailQuestion.DelinquentFederalDebtAmount),
            ),
            new QuestionDefinition(
              QuestionType.Radio,
              liabilityFormGroup.form.controls.liabilityType,
              signal(DeclarationTrailQuestion.DelinquentFederalIsTaxDebt),
            )
              .withSelectOptions([
                { value: LiabilityType.TaxesOwed, label: 'Yes' },
                { value: LiabilityType.MiscellaneousInstallment, label: 'No' },
              ])
              .withConditionalTrail(
                new Condition(
                  liabilityFormGroup.form.controls.liabilityType,
                  liabilityTypeConditionCheckerFunc,
                ),
                new QuestionDefinition(
                  QuestionType.YesNo,
                  liabilityFormGroup.form.controls.isClientOnRepaymentPlan,
                  signal(DeclarationTrailQuestion.DelinquentFederalIsOnDebtPaymentPlan),
                ),
              ),
          ];
        },
        {
          sectionTitle: 'Federal Debt',
          bannerText: DeclarationBanners.CreditQualifier,
        },
        this.liabilityService,
        this.liabilityDecsIdService,
        federalDebtIds,
        true,
        clientId,
      );

    const liabilityQuestion = createLiabilityQuestion(
      liabilityGroups,
      leadControl,
      DeclarationMainQuestion.HasDelinquentFederalDebt,
      trailBuilderFunc,
    );

    if (!liabilityQuestion) {
      return null;
    }

    liabilityQuestion.addFunc = liabilityFormArrayAddFunc(
      clientId,
      this.liabilityService,
      LiabilityType.TaxesOwed,
      DeclarationMainQuestion.HasDelinquentFederalDebt,
    );
    liabilityQuestion.parentFormArray = this.liabilityService.formArray;

    return { question: liabilityQuestion, name: DeclarationMainQuestion.HasDelinquentFederalDebt };
  }

  private generateCoSignerQuestion(clientForm: FormGroup<ClientControls>, clientKey: string) {
    const leadControl =
      clientForm.controls.declarationInformation.controls
        .isCosignerOrGuarantorOnDebtsNotPreviouslyDisclosed;
    const clientId = this.clientCategorizationService.getClientIdFromFormId(clientKey);
    const coSignerIds =
      clientForm.controls.declarationInformation.controls.cosignerOrGuarantorLiabilityIds;

    const liabilityGroups = this.liabilityService.getLiabilityFormGroups(
      clientId,
      coSignerIds.value,
      DeclarationMainQuestion.IsCoSignerOrGuarantor,
    );

    const trailBuilderFunc: LiabilityTrailBuilderFunc = (liabilityFormGroup) =>
      createLiabilityTrails(
        leadControl!,
        liabilityFormGroup,
        DeclarationTrailQuestion.CoSignerOrGuarantorIsDebtOnPaymentPlan,
        (liabilityFormGroup) => [
          new QuestionDefinition(
            QuestionType.Text,
            liabilityFormGroup.form.controls.creditorName,
            signal(DeclarationTrailQuestion.CoSignerOrGuarantorDebtLender),
            'Creditor Name',
          ),
          new QuestionDefinition(
            QuestionType.Currency,
            liabilityFormGroup.form.controls.unpaidBalance,
            signal(DeclarationTrailQuestion.CoSignerOrGuarantorOwedAmount),
          ),
          new QuestionDefinition(
            QuestionType.Select,
            liabilityFormGroup.form.controls.liabilityType,
            signal(DeclarationTrailQuestion.CoSignerOrGuarantorDebtType),
            'Debt Type',
          ).withSelectOptions([
            { value: LiabilityType.AutoLease, label: 'Auto Lease' },
            { value: LiabilityType.AutoLoan, label: 'Auto Loan' },
            { value: LiabilityType.MiscellaneousInstallment, label: 'Miscellaneous Installment' },
            { value: LiabilityType.Open30DayChargeAccount, label: 'Open 30-Day Charge Account' },
            { value: LiabilityType.Revolving, label: 'Revolving' },
            { value: LiabilityType.StudentLoan, label: 'Student Loan' },
          ]),
        ],
        {
          sectionTitle: 'Debt',
        },
        this.liabilityService,
        this.liabilityDecsIdService,
        coSignerIds,
        false,
        clientId,
      );

    const liabilityQuestion = createLiabilityQuestion(
      liabilityGroups,
      leadControl,
      DeclarationMainQuestion.IsCoSignerOrGuarantor,
      trailBuilderFunc,
    );

    if (!liabilityQuestion) {
      return null;
    }

    liabilityQuestion.addFunc = liabilityFormArrayAddFunc(
      clientId,
      this.liabilityService,
      LiabilityType.MiscellaneousInstallment,
      DeclarationMainQuestion.IsCoSignerOrGuarantor,
    );
    liabilityQuestion.parentFormArray = this.liabilityService.formArray;

    return { question: liabilityQuestion, name: DeclarationMainQuestion.IsCoSignerOrGuarantor };
  }

  private generateAlimoneyChildSupportQuestion(
    clientForm: FormGroup<ClientControls>,
    clientKey: string,
  ) {
    const leadControl =
      clientForm?.controls.declarationInformation.controls.hasChildSupportOrAlimony;
    const clientId = this.clientCategorizationService.getClientIdFromFormId(clientKey);

    const childSupportOrAlimonyIds =
      clientForm.controls.declarationInformation.controls.childSupportOrAlimonyLiabilityIds;

    const liabilityGroups = this.liabilityService.getLiabilityFormGroups(
      clientId,
      childSupportOrAlimonyIds.value,
      DeclarationMainQuestion.IsPayingAlimonyOrChildSupport,
    );

    const trailBuilderFunc: LiabilityTrailBuilderFunc = (liabilityFormGroup) =>
      createLiabilityTrails(
        leadControl,
        liabilityFormGroup,
        DeclarationTrailQuestion.PayingAlimonyOrChildSupportArePaymentsPastDue,
        (liabilityFormGroup) => [
          new QuestionDefinition(
            QuestionType.Chip,
            liabilityFormGroup.form.controls.liabilityType,
            signal(DeclarationTrailQuestion.PayingAlimonyOrChildSupportPaymentType),
          ).withSelectOptions([
            { value: LiabilityType.Alimony, label: 'Alimony/Separate Maintenance' },
            { value: LiabilityType.ChildSupport, label: 'Child Support' },
          ]),
          new QuestionDefinition(
            QuestionType.Text,
            liabilityFormGroup.form.controls.creditorName,
            signal(DeclarationTrailQuestion.PayingAlimonyOrChildSupportPayeeName),
            'Paid To',
          ),
          new QuestionDefinition(
            QuestionType.Number,
            liabilityFormGroup.form.controls.monthsRemainingCount,
            signal(DeclarationTrailQuestion.PayingAlimonyOrChildSupportRemainingMonths),
            'Months Remaining',
          ),
          new QuestionDefinition(
            QuestionType.Currency,
            liabilityFormGroup.form.controls.monthlyPaymentAmount,
            signal(DeclarationTrailQuestion.PayingAlimonyOrChildSupportObligationAmount),
            'Monthly Amount',
          ),
        ],
        {
          sectionTitle: 'Obligation',
        },
        this.liabilityService,
        this.liabilityDecsIdService,
        childSupportOrAlimonyIds,
        false,
        clientId,
      );

    const liabilityQuestion = createLiabilityQuestion(
      liabilityGroups,
      leadControl,
      DeclarationMainQuestion.IsPayingAlimonyOrChildSupport,
      trailBuilderFunc,
    );

    if (!liabilityQuestion) {
      return null;
    }

    liabilityQuestion.addFunc = liabilityFormArrayAddFunc(
      clientId,
      this.liabilityService,
      LiabilityType.Alimony,
      DeclarationMainQuestion.IsPayingAlimonyOrChildSupport,
    );
    liabilityQuestion.parentFormArray = this.liabilityService.formArray;

    return {
      question: liabilityQuestion,
      name: DeclarationMainQuestion.IsPayingAlimonyOrChildSupport,
    };
  }
}
