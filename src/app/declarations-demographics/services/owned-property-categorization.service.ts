import { Injectable, computed, effect, inject, untracked } from '@angular/core';
import { CurrentlySelectedPropertyInfoService } from '../../left-nav-content/subject-property/edit/reo-details/currently-selected-property-info.service';
import { EntityStateName } from '../../services/entity-state/abstract-entity-state.service';
import { LiabilityFormRef } from '../../services/entity-state/liability-state/liability-form.service';
import { OwnedPropertyControls } from '../../services/entity-state/owned-property-state/form-types';
import { OwnedPropertyFormService } from '../../services/entity-state/owned-property-state/owned-property-form.service';
import { OwnedPropertyLienAssociationWarningService } from '../../services/entity-state/owned-property-state/owned-property-lien-association-warning.service';
import { ValidationErrorDto } from '../../services/error/validation-error-provider.service';
import { IOwnedPropertyViewModel } from '../models/view-models/view-models.interface';
import { LeftNavTileStatusService } from './abstract-tile-status.service';

@Injectable()
export class OwnedPropertyCategorizationService extends LeftNavTileStatusService<OwnedPropertyControls> {
  readonly currentPropInfoSerivce = inject(CurrentlySelectedPropertyInfoService);
  readonly ownedPropWarningService = inject(OwnedPropertyLienAssociationWarningService);
  readonly ownedPropertyFormService = inject(OwnedPropertyFormService);
  readonly liabilityFormRef = inject(LiabilityFormRef);

  private readonly ownedPropertyForms = computed(
    () => this.ownedPropertyFormService.ownedPropertyMapEntries() ?? [],
  );

  private readonly liabilityForms = computed(() =>
    Array.from(this.liabilityFormRef.entityFormMap()),
  );

  ownedPropInfo = computed<ReadonlyArray<IOwnedPropertyViewModel>>(() =>
    this.ownedPropertyForms().map((formEntry) => {
      const formId = formEntry[0];
      const form = formEntry[1];
      const ownedPropertyAddress = form.getRawValue().address?.addressLine1;
      const isSubjectProperty = form.getRawValue().isSubjectProperty || false;
      const isCurrentResidence = form.getRawValue().isCurrentResidence ?? false;

      const { variant = 'default', icon = '' } = this.presentationMap()[formId] || {};

      return {
        ownedPropertyForm: form,
        ownedPropertyFormId: formId,
        ownedPropertyAddress:
          ownedPropertyAddress && ownedPropertyAddress.length ? ownedPropertyAddress : '--',
        isSubjectProperty: isSubjectProperty,
        isCurrentResidence: isCurrentResidence,
        presentation: {
          variant,
          icon,
        },
      };
    }),
  );

  constructor() {
    super();
    effect(() => {
      const ownedPropertyErrors = this.validationErrorService
        .errors()
        .filter((error) => error.stateName === EntityStateName.OwnedProperty);
      const liabilityErrors = this.validationErrorService
        .errors()
        .filter((error) => error.stateName === EntityStateName.Liability);

      const translatedErrors = this.translateLiabilityErrorToOwnedPropError(liabilityErrors);
      const allErrors = [...ownedPropertyErrors, ...translatedErrors].filter(
        (error): error is ValidationErrorDto => !!error,
      );

      untracked(() => {
        if (ownedPropertyErrors) {
          this.handleStates(allErrors, this.ownedPropertyForms());
        }
      });
    });
  }

  private translateLiabilityErrorToOwnedPropError(liabilityErrors: ValidationErrorDto[]) {
    return liabilityErrors.map((error) => {
      const foundLiablity = this.liabilityForms().find((liablity) => liablity[0] === error.formId);

      const foundOwnedProp = this.ownedPropertyForms().find(
        (ownedProperty) => ownedProperty[1].value.id === foundLiablity?.[1].value.ownedPropertyId,
      );

      if (foundOwnedProp) {
        return {
          formId: foundOwnedProp[0],
        } as ValidationErrorDto;
      }
      return undefined;
    });
  }
}
