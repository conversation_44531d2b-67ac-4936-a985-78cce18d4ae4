import { signal } from '@angular/core';
import {
  AsianRace,
  CollectionType,
  Ethnicity,
  Gender,
  HispanicOrLatinoOrigin,
  NativeHawaiianOrPacificIslanderRace,
  PreferredLanguage,
  Race,
} from '@rocket-logic/rl-xp-bff-models';
import {
  buildEthnicityMetadata,
  buildGenderMetadata,
  buildRaceMetadata,
  DemographicInformationFormControls,
  DemographicInformationFormGroup,
  getCollectionControls,
  getEthnicityControls,
  getRaceControls,
} from '../../util/build-demographic-information-form';
import {
  buildLanguageMetadata,
  SupplementalConsumerFormGroup,
} from '../../util/build-supplemental-consumer-information';
import { DemographicsMainQuestion } from '../models/declaration/demographics-main-question.enum';
import { Condition, ConditionalTrailQuestion } from '../models/question/conditional-question';
import {
  enumValuesExcept,
  mapToSelectOptions,
  Orientation,
  QuestionDefinition,
  SelectOptions,
} from '../models/question/question-definition';
import { QuestionType } from '../models/question/question-type.enum';

export function buildEthnicityQuestion(formGroup: DemographicInformationFormGroup) {
  const { hispanicOrLatinoOrigin, otherHispanicOrLatinoOriginDescription, ethnicities } =
    getEthnicityControls(formGroup);

  const options: SelectOptions = [
    {
      value: Ethnicity.HispanicOrLatino,
      label: 'Hispanic or Latino',
      conditionalTrail: new ConditionalTrailQuestion(new Condition(ethnicities, () => true), [
        buildHispanicOrLatinoOriginQuestion(
          hispanicOrLatinoOrigin,
          otherHispanicOrLatinoOriginDescription,
        ),
      ]),
    },
    {
      value: Ethnicity.NotHispanicOrLatino,
      label: 'Not Hispanic or Latino',
    },
    {
      value: Ethnicity.NotProvided,
      label: 'Not Provided',
    },
  ];

  const question = new QuestionDefinition(
    QuestionType.Checkbox,
    ethnicities,
    signal(DemographicsMainQuestion.Ethnicity),
  ).withSelectOptions(options);

  question.metadata$ = buildEthnicityMetadata(formGroup);

  return question;
}

export function buildCollectionQuestions(formGroup: DemographicInformationFormGroup) {
  const {
    ethnicityCollectedBasedOnVisualObservationOrSurname,
    genderCollectedBasedOnVisualObservationOrSurname,
    raceCollectedBasedOnVisualObservationOrSurname,
    collectionType,
  } = getCollectionControls(formGroup);

  const howEthnicityWasCollectedQuestion = new QuestionDefinition(
    QuestionType.YesNo,
    ethnicityCollectedBasedOnVisualObservationOrSurname,
    signal(DemographicsMainQuestion.HowEthnicityWasCollected),
  );
  const genderCollectedBasedOnVisualObservationOrSurnameQuestion = new QuestionDefinition(
    QuestionType.YesNo,
    genderCollectedBasedOnVisualObservationOrSurname,
    signal(DemographicsMainQuestion.HowGenderWasCollected),
  );
  const raceCollectedBasedOnVisualObservationOrSurnameQuestion = new QuestionDefinition(
    QuestionType.YesNo,
    raceCollectedBasedOnVisualObservationOrSurname,
    signal(DemographicsMainQuestion.HowRaceWasCollected),
  );
  const collectionTypeQuestion = new QuestionDefinition(
    QuestionType.Radio,
    collectionType,
    signal(DemographicsMainQuestion.DemographicProvided),
  ).withSelectOptions([
    { value: CollectionType.FaceToFaceInterview, label: 'Face-to-Face Interview' },
    { value: CollectionType.TelephoneInterview, label: 'Telephone Interview' },
    { value: CollectionType.FaxOrMail, label: 'Fax or Mail' },
    { value: CollectionType.EmailOrInternet, label: 'Email or Internet' },
  ]);

  howEthnicityWasCollectedQuestion.isDisabled = true;
  genderCollectedBasedOnVisualObservationOrSurnameQuestion.isDisabled = true;
  raceCollectedBasedOnVisualObservationOrSurnameQuestion.isDisabled = true;
  collectionTypeQuestion.isDisabled = true;
  collectionTypeQuestion.orientation = Orientation.Vertical;

  return {
    howEthnicityWasCollectedQuestion,
    genderCollectedBasedOnVisualObservationOrSurnameQuestion,
    raceCollectedBasedOnVisualObservationOrSurnameQuestion,
    collectionTypeQuestion,
  };
}

export function buildRaceQuestion(formGroup: DemographicInformationFormGroup) {
  const {
    race,
    asianRace,
    nativeHawaiianOrPacificIslanderRace,
    otherAsianRaceDescription,
    otherPacificIslanderRaceDescription,
    enrolledOrPrincipalTribeName,
  } = getRaceControls(formGroup);

  const options: SelectOptions = [
    {
      value: Race.AmericanIndianOrAlaskaNative,
      label: 'American Indian or Alaskan Native',
      conditionalTrail: new ConditionalTrailQuestion(
        new Condition(race, (value) => value?.includes(Race.AmericanIndianOrAlaskaNative)),
        [
          new QuestionDefinition(
            QuestionType.Text,
            enrolledOrPrincipalTribeName,
            signal('Enrolled or Principal Tribe'),
          ),
        ],
      ),
    },
    {
      value: Race.Asian,
      label: 'Asian',
      conditionalTrail: new ConditionalTrailQuestion(new Condition(race, () => true), [
        buildAsianRaceQuestion(asianRace, otherAsianRaceDescription),
      ]),
    },
    {
      value: Race.BlackOrAfricanAmerican,
      label: 'Black or African American',
    },
    {
      value: Race.NativeHawaiianOrOtherPacificIslander,
      label: 'Native Hawaiian or Other Pacific Islander',
      conditionalTrail: new ConditionalTrailQuestion(new Condition(race, () => true), [
        buildNativeHawaiianOrPacificIslanderRace(
          nativeHawaiianOrPacificIslanderRace,
          otherPacificIslanderRaceDescription,
        ),
      ]),
    },
    {
      value: Race.White,
      label: 'White',
    },
    {
      value: Race.NotProvided,
      label: 'Not Provided',
    },
  ];

  const question = new QuestionDefinition(
    QuestionType.Checkbox,
    race,
    signal(DemographicsMainQuestion.Race),
  ).withSelectOptions(options);

  question.metadata$ = buildRaceMetadata(formGroup);

  return question;
}

export function buildGenderQuestion(formGroup: DemographicInformationFormGroup) {
  const options: SelectOptions = [
    {
      value: Gender.Female,
      label: 'Female',
    },
    {
      value: Gender.Male,
      label: 'Male',
    },
    {
      value: Gender.ApplicantSelectedBothMaleAndFemale,
      label: 'Male and Female',
    },
    {
      value: Gender.NotProvided,
      label: 'Not Provided',
    },
  ];

  const question = new QuestionDefinition(
    QuestionType.Radio,
    formGroup.controls.gender,
    signal(DemographicsMainQuestion.Gender),
  ).withSelectOptions(options);

  question.metadata$ = buildGenderMetadata(formGroup);

  return question;
}

export function buildPreferredLanguageQuestion(formGroup: SupplementalConsumerFormGroup) {
  const languagePreferenceControls = formGroup.controls.languagePreference.controls;
  const preferredLanguage = languagePreferenceControls.preferredLanguage;

  const question = new QuestionDefinition(
    QuestionType.Select,
    preferredLanguage,
    signal(DemographicsMainQuestion.PreferredLanguage),
    'Language',
  )
    .withSelectOptions(mapToSelectOptions(PreferredLanguage))
    .withConditionalTrail(
      new Condition(
        preferredLanguage,
        (value: PreferredLanguage) => value === PreferredLanguage.Other,
      ),
      new QuestionDefinition(
        QuestionType.Text,
        languagePreferenceControls.otherPreferredLanguage,
        signal('Other Preferred Language'),
      ),
    );

  question.metadata$ = buildLanguageMetadata(formGroup);

  return question;
}

function buildAsianRaceQuestion(
  asianRace: DemographicInformationFormControls['asianRace'],
  otherAsianRaceDescription: DemographicInformationFormControls['otherAsianRaceDescription'],
) {
  const options = mapToSelectOptions(enumValuesExcept(AsianRace, AsianRace.OtherAsian));

  options.push({
    value: AsianRace.OtherAsian,
    label: 'Other Asian',
    conditionalTrail: new ConditionalTrailQuestion(
      new Condition(asianRace, () => asianRace.value?.includes(AsianRace.OtherAsian) ?? false),
      [
        new QuestionDefinition(
          QuestionType.Text,
          otherAsianRaceDescription,
          signal('Asian Race Origin'),
        ),
      ],
    ),
  });

  return new QuestionDefinition(QuestionType.Checkbox, asianRace).withSelectOptions(options);
}

function buildNativeHawaiianOrPacificIslanderRace(
  nativeHawaiianOrPacificIslanderRace: DemographicInformationFormControls['nativeHawaiianOrPacificIslanderRace'],
  otherPacificIslanderRaceDescription: DemographicInformationFormControls['otherPacificIslanderRaceDescription'],
) {
  const options = mapToSelectOptions(
    enumValuesExcept(
      NativeHawaiianOrPacificIslanderRace,
      NativeHawaiianOrPacificIslanderRace.OtherPacificIslander,
    ),
  );

  options.push({
    value: NativeHawaiianOrPacificIslanderRace.OtherPacificIslander,
    label: 'Other Pacific Islander',
    conditionalTrail: new ConditionalTrailQuestion(
      new Condition(
        nativeHawaiianOrPacificIslanderRace,
        () =>
          nativeHawaiianOrPacificIslanderRace.value?.includes(
            NativeHawaiianOrPacificIslanderRace.OtherPacificIslander,
          ) ?? false,
      ),
      [
        new QuestionDefinition(
          QuestionType.Text,
          otherPacificIslanderRaceDescription,
          signal('Other Hawaiian Race Origin'),
        ),
      ],
    ),
  });

  return new QuestionDefinition(
    QuestionType.Checkbox,
    nativeHawaiianOrPacificIslanderRace,
  ).withSelectOptions(options);
}

function buildHispanicOrLatinoOriginQuestion(
  hispanicOrLatinoOrigin: DemographicInformationFormControls['hispanicOrLatinoOrigin'],
  otherHispanicOrLatinoOriginDescription: DemographicInformationFormControls['otherHispanicOrLatinoOriginDescription'],
) {
  const options = mapToSelectOptions(
    enumValuesExcept(
      HispanicOrLatinoOrigin,
      HispanicOrLatinoOrigin.OtherHispanicOrLatino,
      HispanicOrLatinoOrigin.PuertoRican,
    ),
  );

  options.push({
    value: HispanicOrLatinoOrigin.OtherHispanicOrLatino,
    label: 'Other Hispanic or Latino',
    conditionalTrail: new ConditionalTrailQuestion(
      new Condition(
        hispanicOrLatinoOrigin,
        () =>
          hispanicOrLatinoOrigin.value?.includes(HispanicOrLatinoOrigin.OtherHispanicOrLatino) ??
          false,
      ),
      [
        new QuestionDefinition(
          QuestionType.Text,
          otherHispanicOrLatinoOriginDescription,
          signal('Other Hispanic or Latino'),
        ),
      ],
    ),
  });

  options.push({
    value: HispanicOrLatinoOrigin.PuertoRican,
    label: 'Puerto Rican',
  });

  return new QuestionDefinition(QuestionType.Checkbox, hispanicOrLatinoOrigin).withSelectOptions(
    options,
  );
}
