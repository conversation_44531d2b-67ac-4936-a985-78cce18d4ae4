import { inject, Injectable } from '@angular/core';
import { FormArray, FormControl } from '@angular/forms';
import { Liability, LiabilityType } from '@rocket-logic/rl-xp-bff-models';
import { filter } from 'rxjs';
import { ClientFormRefService } from '../../services/entity-state/client-state/client-form.service';
import { AllLiabilityGroup } from '../../services/entity-state/liability-state/form-types';
import { LiabilityFormService } from '../../services/entity-state/liability-state/liability-form.service';
import { LiabilitySubscriptionManager } from '../../services/entity-state/liability-state/liability-subscription-manager.service';
import { DeclarationMainQuestion } from '../models/declaration/declaration-main-question.enum';

@Injectable()
export class LiabilityDeclarationsIdListenerService {
  private readonly subManager = inject(LiabilitySubscriptionManager);
  private readonly clientFormService = inject(ClientFormRefService);
  private readonly liabilityFormService = inject(LiabilityFormService);

  addListener(
    liabilityForm: AllLiabilityGroup,
    liabilityKey: string,
    idArrayRef: FormArray,
    clientId: string,
  ) {
    const sub = liabilityForm.valueChanges
      .pipe(filter((liability) => !!liability.id && !!liability.clientIds?.includes(clientId)))
      .subscribe((liability) => {
        if (!idArrayRef.value.includes(liability.id)) {
          idArrayRef.markAsDirty();
          idArrayRef.push(new FormControl(liability.id));
        }

        this.updateBorrowingAmount(liability as Liability, clientId);
      });

    sub.add(() => {
      if (idArrayRef.value.includes(liabilityForm.value.id)) {
        const index = idArrayRef.value.indexOf(liabilityForm.value.id);
        idArrayRef.markAsDirty();
        idArrayRef.removeAt(index);
      }

      this.updateBorrowingAmount(liabilityForm.value as Liability, clientId);
    });

    this.subManager.addSubscription(liabilityKey, sub);
  }

  private updateBorrowingAmount(liability: Liability, clientId: string) {
    if (liability.liabilityType === LiabilityType.MiscellaneousInstallment) {
      const clientForm = this.clientFormService
        .entityValues()
        .find((client) => client.value.id === clientId);

      const borrowedFundsIds =
        clientForm?.controls.declarationInformation.controls.borrowedFundsLiabilityIds.value;
      const borrowingAmountControl =
        clientForm?.controls.declarationInformation.controls.borrowingAmount;

      const liabilityForms = this.liabilityFormService.getLiabilityFormGroups(
        clientId,
        borrowedFundsIds ?? [],
        DeclarationMainQuestion.IsBorrowingMoney,
      );

      const borrowingAmount = liabilityForms
        .map((liabilityGroup) => Number(liabilityGroup.form.controls.unpaidBalance.value) || 0)
        .reduce((acc, val) => acc + val, 0);

      if (borrowingAmountControl?.value === borrowingAmount) {
        return;
      }

      borrowingAmountControl?.markAsDirty();
      borrowingAmountControl?.setValue(borrowingAmount);
    }
  }
}
