import { ChangeDetectionStrategy, Component, computed, input } from '@angular/core';
import { MatIconModule } from '@angular/material/icon';
import { RktTagEnterpriseModule } from '@rocketcentral/rocket-design-system-enterprise-angular';

@Component({
  selector: 'app-section-progress-tag',
  standalone: true,
  imports: [MatIconModule, RktTagEnterpriseModule],
  changeDetection: ChangeDetectionStrategy.OnPush,
  templateUrl: './section-progress-tag.component.html',
  styleUrl: './section-progress-tag.component.scss',
})
export class SectionProgressTagComponent {
  readonly title = input('');
  readonly completedSections = input(0, { transform: (value) => value ?? 0 });
  readonly total = input.required<number>();

  protected readonly tagVariant = computed(() => this.total() === this.completedSections() ? 'success' : undefined);
}
