<h2 class="flex justify-between items-center">
  <span class="rkt-Heading-18">Demographics Collection Method</span>
  <button
    class="rkt-ButtonReset rkt-Dialog__dismiss-button"
    type="button"
    title="Dismiss"
    mat-dialog-close
  >
    <mat-icon svgIcon="close-outlined" aria-hidden="true"></mat-icon>
  </button>
</h2>

<mat-dialog-content>
  @if (collectionQuestions(); as questions) {
    <div class="flex flex-col gap-3">
      @for (question of questions; track $index) {
        <app-question-group
          [isTopLevel]="true"
          [variant]="questionGroupVariant.Accordion"
          [questionInput]="[question]"
          [clientId]="this.clientService.selectedFormId()"
        />
      }
    </div>
  }
</mat-dialog-content>
