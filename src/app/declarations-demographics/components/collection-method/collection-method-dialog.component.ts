import { ChangeDetectionStrategy, Component, computed, inject } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatDialogModule } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { RktAlertEnterpriseModule } from '@rocketcentral/rocket-design-system-enterprise-angular';
import { ClientControls } from '../../../services/entity-state/client-state/form-types';
import { ClientCategorizationFacadeService } from '../../services/client-categorization.service';
import { buildCollectionQuestions } from '../../services/demographics-question-helpers';
import {
  QuestionGroupComponent,
  QuestionGroupVariant,
} from '../question-group/question-group.component';

@Component({
  selector: 'app-collection-method-bar-dialog',
  standalone: true,
  imports: [
    MatProgressBarModule,
    MatCardModule,
    RktAlertEnterpriseModule,
    MatDialogModule,
    QuestionGroupComponent,
    MatButtonModule,
    MatIconModule,
  ],
  templateUrl: './collection-method-dialog.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CollectionMethodDialogComponent {
  protected readonly questionGroupVariant = QuestionGroupVariant;
  protected readonly clientService = inject(ClientCategorizationFacadeService);

  protected readonly collectionQuestions = computed(() =>
    this.generateCollectionQuestions(this.clientService.selectedClientForm()),
  );

  private generateCollectionQuestions(clientForm: FormGroup<ClientControls>) {
    const questions = buildCollectionQuestions(clientForm.controls.demographicInformation);

    return [
      questions.howEthnicityWasCollectedQuestion,
      questions.genderCollectedBasedOnVisualObservationOrSurnameQuestion,
      questions.raceCollectedBasedOnVisualObservationOrSurnameQuestion,
      questions.collectionTypeQuestion,
    ];
  }
}
