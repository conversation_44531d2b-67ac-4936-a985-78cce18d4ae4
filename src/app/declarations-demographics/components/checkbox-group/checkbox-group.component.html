<fieldset class="rkt-Fieldset">
  <div class="flex flex-col" [style.padding-left.px]="level() * 24">
    @for (option of selectOptions(); track option.value) {
      <mat-checkbox
        class="rkt-Checkbox rkt-Checkbox--enterprise"
        [value]="option.value.toString()"
        (change)="onCheckedChange($event)"
        [aria-label]="option.label"
        [disabled]="option.isOptionDisabled || isDisabled()"
        [disableRipple]="true"
      >
        {{ option.label }}
      </mat-checkbox>

      @if (option.conditionalTrail?.condition?.isMet$ | async) {
        @for (trailQuestion of option.conditionalTrail?.trailQuestions; track $index) {
          @if (
            isFormControl(trailQuestion.targetFormControl) &&
            trailQuestion.questionType === questionType.Checkbox
          ) {
            <app-checkbox-group
              [selectOptions]="trailQuestion.options"
              [options]="[]"
              [level]="level() + 1"
              [formControl]="trailQuestion.targetFormControl"
            />
          } @else if (
            isFormControl(trailQuestion.targetFormControl) &&
            trailQuestion.questionType === questionType.Text
          ) {
            <app-text-field
              [control]="trailQuestion.targetFormControl"
              [label]="
                trailQuestion.placeholder ?? (trailQuestion.text ? trailQuestion.text() : '')
              "
              class="ml-2"
            />
          }
        }
      }
    }
  </div>

  @if (errorMessage()) {
    <mat-error role="alert">{{ errorMessage() }}</mat-error>
  }
</fieldset>
