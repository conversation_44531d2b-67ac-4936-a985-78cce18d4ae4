import { AsyncPipe } from '@angular/common';
import { ChangeDetectionStrategy, Component, input } from '@angular/core';
import { isFormControl, ReactiveFormsModule } from '@angular/forms';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatFormFieldModule } from '@angular/material/form-field';
import { TextFieldComponent } from '../../../_shared/components/text-field/text-field.component';
import { CheckboxInputComponent } from '../../../refinance-info/checkbox-input/checkbox-input.component';
import { SelectOptions } from '../../models/question/question-definition';
import { QuestionType } from '../../models/question/question-type.enum';

@Component({
  selector: 'app-checkbox-group',
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: true,
  imports: [
    MatCheckboxModule,
    MatFormFieldModule,
    ReactiveFormsModule,
    AsyncPipe,
    TextFieldComponent,
  ],
  styleUrls: ['./checkbox-group.component.scss'],
  templateUrl: './checkbox-group.component.html',
})
export class CheckboxGroupComponent extends CheckboxInputComponent {
  readonly selectOptions = input<SelectOptions | undefined>([]);
  readonly level = input<number>(0);
  protected readonly isFormControl = isFormControl;
  protected readonly questionType = QuestionType;

  override writeValue(value: string[]): void {
    this.currentValue.clear();
    value?.forEach((val) => this.currentValue.add(val));
    this.setCheckboxDefaults(this.checkboxes());
  }
}
