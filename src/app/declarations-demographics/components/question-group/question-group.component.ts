import { CommonModule } from '@angular/common';
import {
  AfterViewInit,
  ChangeDetectionStrategy,
  Component,
  computed,
  effect,
  ElementRef,
  inject,
  input,
  OnD<PERSON>roy,
  untracked,
  viewChildren,
} from '@angular/core';
import { FormGroup, isFormControl, isFormGroup, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatChipsModule } from '@angular/material/chips';
import { MatDialogModule } from '@angular/material/dialog';
import { MatAccordion, MatExpansionModule } from '@angular/material/expansion';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatRadioModule } from '@angular/material/radio';
import { MatSelectModule } from '@angular/material/select';
import { Address } from '@rocket-logic/rl-xp-bff-models';
import { RktStackModule } from '@rocketcentral/rocket-design-system-angular';
import { RktAlertEnterpriseModule } from '@rocketcentral/rocket-design-system-enterprise-angular';
import { TextFieldComponent } from '../../../_shared/components/text-field/text-field.component';
import { AddressComponent } from '../../../address/address.component';
import { FormattedDateInputComponent } from '../../../question-input/formatted-date-input/formatted-date-input.component';
import { FormattedNumberInputComponent } from '../../../question-input/formatted-number-input/formatted-number-input.component';
import { DarkModeService } from '../../../services/dark-mode/dark-mode.service';
import { isPaymentMethodFormGroup } from '../../../util/build-declaration-information-form';
import { Controlify } from '../../../util/form-utility-types';
import { formatAddress } from '../../../util/format-address';
import { SanitizePipe } from '../../../util/sanitize.pipe';
import { DeclarationMainQuestion } from '../../models/declaration/declaration-main-question.enum';
import { ConditionalTrailQuestion } from '../../models/question/conditional-question';
import { Orientation, QuestionDefinition } from '../../models/question/question-definition';
import { QuestionType } from '../../models/question/question-type.enum';
import { DecsDemsCompletionService } from '../../services/decs-dems-completion.service';
import { CheckboxGroupComponent } from '../checkbox-group/checkbox-group.component';

export enum QuestionGroupVariant {
  Card,
  Accordion,
  None,
}

@Component({
  selector: 'app-question-group',
  standalone: true,
  imports: [
    MatIconModule,
    MatDialogModule,
    ReactiveFormsModule,
    MatButtonModule,
    MatRadioModule,
    MatChipsModule,
    RktStackModule,
    MatIconModule,
    TextFieldComponent,
    MatFormFieldModule,
    ReactiveFormsModule,
    MatSelectModule,
    FormattedNumberInputComponent,
    FormattedDateInputComponent,
    CommonModule,
    MatCardModule,
    AddressComponent,
    SanitizePipe,
    CheckboxGroupComponent,
    MatAccordion,
    MatExpansionModule,
    RktAlertEnterpriseModule,
  ],
  templateUrl: './question-group.component.html',
  styleUrl: './question-group.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class QuestionGroupComponent implements AfterViewInit, OnDestroy {
  private readonly LAST_CONTAINER_CLASS = 'last-form-container';

  private readonly elemRef = inject(ElementRef);
  private readonly decsDemsCompletionService = inject(DecsDemsCompletionService, { optional: true });
  public readonly darkModeService = inject(DarkModeService);
  public readonly questionGroupVariant = QuestionGroupVariant;
  public readonly orientation = Orientation;

  readonly section = input<DeclarationMainQuestion | undefined>();
  readonly isTopLevel = input(false);
  readonly variant = input<QuestionGroupVariant>(QuestionGroupVariant.Card);
  readonly parentQuestionIndex = input<string | undefined>();
  readonly questionInput = input.required<QuestionDefinition[]>();
  readonly clientId = input.required<string | undefined>();
  private previousQuestionSet: QuestionDefinition[] = [];
  readonly sectionKey = computed(() => (this.section() ?? '') + this.clientId());

  protected readonly questions = computed(() => {
    return this.questionInput().map((question) => {
      if (
        this.isFormGroup(question.targetFormControl) &&
        question.questionType === QuestionType.Address
      ) {
        question.addressFormControl = question.targetFormControl as FormGroup<Controlify<Address>>;
      } else {
        question.addressFormControl = null;
      }

      return question;
    });
  });

  readonly isSectionComplete = computed(
    () => this.decsDemsCompletionService?.sectionCompletionStatus().get(this.sectionKey()) ?? false,
  );

  readonly subQuestions = viewChildren(QuestionGroupComponent, { read: ElementRef });
  private readonly observer = new MutationObserver(() => this.updateFormContainers());

  private formContainers: HTMLElement[] = [];

  readonly isFormControl = isFormControl;
  readonly isPaymentMethodFormGroup = isPaymentMethodFormGroup;
  readonly isFormGroup = isFormGroup;
  protected readonly QuestionType = QuestionType;
  protected formatAddressFunc = formatAddress;

  constructor() {
    if (this.decsDemsCompletionService) {
      effect(() => {
        const section = this.section();
        if (!section) return;

        const newQuestions = this.questionInput();
        if (this.previousQuestionSet.length > 0) {
          this.previousQuestionSet.forEach((question) => {
            untracked(() =>
              this.decsDemsCompletionService!.removeControl(this.sectionKey(), question),
            );
          });
        }

        this.previousQuestionSet = newQuestions;

        this.questionInput().forEach((question) =>
          untracked(() => this.decsDemsCompletionService!.addSection(this.sectionKey(), question)),
        );
      });
    }
  }

  onAdd(question: QuestionDefinition) {
    question.addEntity();
  }

  onDelete(trail: ConditionalTrailQuestion) {
    trail.deleteEntity();
  }

  ngAfterViewInit() {
    if (this.isTopLevel()) {
      this.updateFormContainers();
      this.observer.observe(this.elemRef.nativeElement, {
        childList: true,
        subtree: true,
        attributes: false,
      });
    }
  }

  ngOnDestroy() {
    if (this.observer) {
      this.observer.disconnect();
    }

    if (this.decsDemsCompletionService && this.section()) {
      this.questionInput().forEach((question) => {
        this.decsDemsCompletionService!.removeControl(this.sectionKey(), question);
      });
    }
  }

  private updateFormContainers(): void {
    this.formContainers.forEach((container) =>
      container?.classList?.remove(this.LAST_CONTAINER_CLASS),
    );

    const containers = Array.from(
      this.elemRef.nativeElement.getElementsByClassName('form-container'),
    ) as HTMLElement[];

    if (containers.length > 1) {
      containers[containers.length - 1]?.classList.add(this.LAST_CONTAINER_CLASS);
    }

    this.formContainers = containers;
  }
}
