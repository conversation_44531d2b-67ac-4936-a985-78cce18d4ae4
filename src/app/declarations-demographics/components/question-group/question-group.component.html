@if (questions().length > 0) {
  @for (question of questions(); track $index) {
    @if (variant() === questionGroupVariant.Card) {
      <div [class.border]="isTopLevel() && subQuestions().length" class="form-container">
        @if (!isTopLevel()) {
          <span class="spoke"></span>
        }

        <mat-card
          [class.nested-bg]="!this.isTopLevel()"
          [class.ml-6]="!this.isTopLevel()"
          [class.flex-row]="this.isTopLevel()"
          [class.items-center]="this.isTopLevel()"
          [class.justify-between]="this.isTopLevel()"
          [class.green]="this.isTopLevel() && this.isSectionComplete()"
          class="rkt-Card mb-3"
        >
          <ng-container *ngTemplateOutlet="questionTemplate" />
        </mat-card>

        <ng-container *ngTemplateOutlet="conditionalTrail" />
      </div>
    } @else if (variant() === questionGroupVariant.Accordion) {
      @if (question.metadata$ | async; as metadata) {
        <mat-accordion [displayMode]="'flat'">
          <mat-expansion-panel
            class="rkt-AccordionPanel"
            [class.green]="metadata.isCompleted"
            [expanded]="true"
            [disabled]="question.isDisabled"
            #panel
          >
            <mat-expansion-panel-header>
              <mat-panel-title
                class="rkt-AccordionPanel__header-title"
                [class.red]="!!metadata.errors.length"
              >
                <ng-container *ngTemplateOutlet="label" />
              </mat-panel-title>
              @if (!panel.expanded) {
                <mat-chip-listbox>
                  @for (option of metadata.selectedValues; track option) {
                    <mat-chip-option
                      class="rkt-Chip"
                      [class.rkt-Chip--on-dark]="darkModeService.isDarkMode()"
                      [selectable]="false"
                    >
                      {{ option }}
                    </mat-chip-option>
                  }
                </mat-chip-listbox>
              }
            </mat-expansion-panel-header>

            <div class="rkt-AccordionPanel__content">
              <ng-container *ngTemplateOutlet="questionTemplate" />
              <ng-container *ngTemplateOutlet="conditionalTrail" />
            </div>

            @for (error of metadata.errors; track $index) {
              <div class="rkt-Caption-12 error">{{ error }}</div>
            }
          </mat-expansion-panel>
        </mat-accordion>
      }
    } @else {
      <span class="pl-4">
        <ng-container *ngTemplateOutlet="questionTemplate" />
      </span>
    }

    <ng-template #label>
      <span>
        @if (parentQuestionIndex()) {
          {{ parentQuestionIndex() | uppercase }}.
        }
        @if (question.text) {
          <label [for]="question.text()" [innerHTML]="question.text() | sanitize"></label>
        }
      </span>
    </ng-template>

    <ng-template #conditionalTrail>
      @for (trail of question.conditionalTrails; track $index; let first = $first) {
        @if (trail.condition.isMet$ | async) {
          @if (trail.textContent?.bannerText && !!first) {
            <rkt-alert-enterprise variant="info">
              <div class="rkt-Alert__text">
                <span class="rkt-Body-14">{{ trail.textContent?.bannerText }}</span>
              </div>
            </rkt-alert-enterprise>
          }

          @if (trail.deleteFunc) {
            <div class="flex justify-end">
              <button
                (click)="onDelete(trail)"
                mat-icon-button
                class="rkt-ButtonIcon"
                aria-label="Delete"
              >
                <mat-icon svgIcon="delete-outlined"></mat-icon>
              </button>
            </div>
          }

          @if (trail.textContent?.sectionTitle) {
            <header class="section-title mb-2">
              {{ trail.textContent?.sectionTitle }} {{ $index + 1 }}
            </header>
          }
          <app-question-group
            [isTopLevel]="false"
            [questionInput]="trail.trailQuestions"
            [variant]="
              variant() === questionGroupVariant.Card
                ? questionGroupVariant.Card
                : questionGroupVariant.None
            "
            [section]="section()"
            [clientId]="clientId()"
          />
        }
        @if (question.parentFormArray && $index === question.conditionalTrails.length - 1) {
          <div class="mb-3 ml-6">
            <button
              (click)="onAdd(question)"
              mat-stroked-button
              class="rkt-Button rkt-Button--secondary rkt-Button--large rkt-Button--has-icon"
            >
              <mat-icon svgIcon="add-outlined"></mat-icon>
              Add New {{ trail.textContent?.sectionTitle }}
            </button>
          </div>
        }
      }
    </ng-template>

    <ng-template #questionTemplate>
      @if (isFormControl(question.targetFormControl)) {
        @if (variant() === questionGroupVariant.Card) {
          <ng-container *ngTemplateOutlet="label" />
        }

        <div class="input inline-block w-fit">
          @switch (question.questionType) {
            @case (QuestionType.Text) {
              <app-text-field
                [control]="question.targetFormControl"
                [label]="question.placeholder ?? (question.text ? question.text() : '')"
                class="w-full"
              />
            }
            @case (QuestionType.YesNo) {
              <mat-radio-group [formControl]="question.targetFormControl">
                <div class="flex gap-4">
                  <mat-radio-button [value]="true" [disabled]="question.isDisabled"
                    >Yes</mat-radio-button
                  >
                  <mat-radio-button [value]="false" [disabled]="question.isDisabled"
                    >No</mat-radio-button
                  >
                </div>
              </mat-radio-group>
            }
            @case (QuestionType.Chip) {
              <mat-chip-listbox [formControl]="question.targetFormControl">
                @for (option of question.options; track option.value) {
                  <mat-chip-option
                    class="rkt-Chip rkt-Chip--has-selected-icon"
                    [class.rkt-Chip--on-dark]="darkModeService.isDarkMode()"
                    [value]="option.value"
                  >
                    {{ option.label }}
                  </mat-chip-option>
                }
              </mat-chip-listbox>
            }
            @case (QuestionType.Radio) {
              <mat-radio-group [formControl]="question.targetFormControl">
                <div
                  class="flex"
                  [class.gap-3]="question.orientation === orientation.Horizontal"
                  [class.flex-col]="question.orientation === orientation.Vertical"
                >
                  @for (option of question.options; track option) {
                    <mat-radio-button
                      [value]="option.value"
                      class="rkt-Label-14"
                      [disabled]="question.isDisabled"
                      >{{ option.label }}</mat-radio-button
                    >
                  }
                </div>
              </mat-radio-group>
            }
            @case (QuestionType.Checkbox) {
              <app-checkbox-group
                [selectOptions]="question.options"
                [options]="[]"
                [formControl]="question.targetFormControl"
              />
            }
            @case (QuestionType.Currency) {
              <app-formatted-number-input
                prefix="$"
                appNavInput
                [control]="question.targetFormControl"
                label="{{ question.placeholder ?? 'Amount' }}"
                [allowNegative]="false"
                class="w-full"
              />
            }
            @case (QuestionType.Number) {
              <app-formatted-number-input
                appNavInput
                [control]="question.targetFormControl"
                [allowNegative]="false"
                label="{{ question.placeholder ?? question?.text() ?? '' }}"
                class="w-full"
              />
            }
            @case (QuestionType.Date) {
              <app-formatted-date-input
                [control]="question.targetFormControl"
                label="{{ question.placeholder ?? question?.text() ?? '' }}"
                class="w-full min-w-max inline-block"
              />
            }
            @case (QuestionType.Select) {
              <mat-form-field class="rkt-FormField w-full">
                <mat-label>{{ question.placeholder ?? question?.text() ?? '' }}</mat-label>
                <mat-select class="rkt-Input" [formControl]="question.targetFormControl">
                  @for (option of question?.options ?? []; track option) {
                    <mat-option [value]="option.value">{{ option.label }}</mat-option>
                  }
                </mat-select>
              </mat-form-field>
            }
          }
        </div>
      } @else if (isFormGroup(question.targetFormControl)) {
        <div class="flex flex-col gap-3">
          <ng-container *ngTemplateOutlet="label"></ng-container>
          @switch (question.questionType) {
            @case (QuestionType.PaymentPlan) {
              @if (isPaymentMethodFormGroup(question.targetFormControl)) {
                <span class="flex flex-row gap-3">
                  <app-formatted-number-input
                    prefix="$"
                    appNavInput
                    [control]="question.targetFormControl.controls.amount"
                    label="{{ question.placeholder ?? 'Amount' }}"
                    [allowNegative]="false"
                    class="w-full"
                  />

                  <mat-form-field class="rkt-FormField w-full">
                    <mat-label>{{ question.placeholder ?? 'Frequency' }}</mat-label>
                    <mat-select
                      class="rkt-Input"
                      [formControl]="question.targetFormControl.controls.frequency"
                      [disabled]="question.isDisabled"
                    >
                      @for (option of question?.options ?? []; track option) {
                        <mat-option [value]="option.value" [disabled]="question.isDisabled">{{
                          option.label
                        }}</mat-option>
                      }
                    </mat-select>
                  </mat-form-field>
                </span>
              }
            }
            @case (QuestionType.Address) {
              @if (question.addressFormControl) {
                <app-address [showCounty]="false" [addressForm]="question.addressFormControl" />
              }
            }
          }
        </div>
      } @else if (question.questionType === QuestionType.HTML) {
        <span
          class="rkt-Body-14"
          [innerHTML]="question.text ? (question.text() | sanitize) : ''"
        ></span>
      }
    </ng-template>
  }
}
