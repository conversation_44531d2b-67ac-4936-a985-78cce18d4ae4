@use '@rocketcentral/rocket-design-system-styles/base/typography' as rkt-typography;
@use '@rocketcentral/rocket-design-system-styles/web/scss/color' as rkt-colors;

:host {
  --section-title-color: #{rkt-colors.$rkt-gray-800};

  ::ng-deep {
    .rkt-AccordionPanel .mat-expansion-panel-body {
      padding: 0 12px 12px;
    }

    .mdc-label {
      @extend %rkt-Label-14;
      color: var(--section-title-color);
      font-weight: 400;
    }

    .mat-expansion-indicator {
      padding-bottom: 6px;
    }

    .mat-expansion-panel-header {
      &:hover {
        background-color: currentColor;
      }
    }

    .rkt-AccordionPanel:hover:not(.Mui-expanded) {
      background-color: unset;
    }

    .rkt-AccordionPanel {
      &.green,
      &.green:hover {
        background-color: var(--rlxp-highlight-background-color);
      }
    }

    .mat-expansion-panel-header[aria-disabled='true'] .mat-expansion-panel-header-title {
      color: var(--section-title-color);
    }

    a {
      color: var(--rlxp-link-color);
      text-decoration-line: underline;
      text-decoration-style: solid;
      cursor: pointer;
    }

    .rkt-Alert {
      margin-left: 1.5rem;
      margin-bottom: 12px;
    }
  }

  .section-title {
    @extend %rkt-Label-14;
    margin-left: 1.5rem;
    color: var(--section-title-color);
  }

  mat-panel-title {
    @extend %rkt-Body-14;
    color: var(--section-title-color);

    &.red {
      color: var(--rlxp-rose-light-700);
    }
  }

  .error {
    color: var(--rlxp-rose-light-700);
  }

  .rkt-AccordionPanel .mat-expansion-panel-header {
    padding: 12px;
    gap: 16px;

    &:hover {
      background-color: unset;
    }
  }

  .mat-expansion-panel {
    border: 1px solid var(--Light-gray-200, #d3d3d3);
    box-shadow: none;
  }

  .mat-expansion-panel-header-title {
    flex: unset;
    padding-right: 16px;
  }

  .mdc-evolution-chip-set {
    display: flex;
    flex: 1;
  }

  .mdc-evolution-chip-set .mdc-evolution-chip-set__chips {
    gap: 8px;
  }

  .mat-mdc-form-field,
  .rkt-FormField.mat-mdc-form-field {
    width: 310px;
  }
}

:host-context(.rkt-DarkMode) {
  --section-title-color: #{rkt-colors.$rkt-white};
}

app-formatted-date-input {
  min-width: 25rem;
}

.form-container {
  position: relative;

  &.border {
    &.form-container:after {
      content: '';
      height: 100%;
      width: 1px;
      position: absolute;
      top: 10px;
      background-color: rgba(#d3d3d3, 0.82);
    }
  }

  &.last-form-container {
    &::after {
      content: '';
      width: 1px;
      position: absolute;
      top: 17px;
      background-color: var(--rlxp-application-island-background-color);
      z-index: 2;
      height: 150%;
    }
  }
}

.spoke {
  position: absolute;
  width: 50px;
  border-bottom: 1px solid var(--rlxp-gray-200);
  position: relative;
}

.spoke::after {
  content: '';
  position: absolute;
  height: 60px;
  width: 60px;
  border: 1px solid transparent;
  border-bottom-color: var(--rlxp-gray-200);
  top: -21px;
  border-radius: 50%;
  transform: rotate(45deg);
}

mat-card.rkt-Card {
  border: 1px solid var(--rlxp-nav-divider-color);
  box-shadow: none;
  overflow: hidden;
  padding: 12px;
  gap: 12px;

  &.green {
    background-color: var(--rlxp-highlight-background-color);
  }

  &.flex-row {
    flex-direction: row;
  }

  &.nested-bg.nested-bg {
    background-color: var(--rlxp-long-form-background-color);
  }
}

.mat-expansion-panel-body {
  padding: 0;
  margin: 0;
  background-color: var(--rlxp-long-form-background-color);
}
