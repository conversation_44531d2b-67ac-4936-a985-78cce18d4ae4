import { FormControl } from '@angular/forms';
import { Address } from '@rocket-logic/rl-xp-bff-models';
import { Condition } from './conditional-question';
import { ConditionalQuestionBuilder } from './conditional-question-builder';
import { QuestionDefinition, SelectOptions } from './question-definition';
import { QuestionType } from './question-type.enum';
import { buildYesNoOptions } from './question-utilities';

export class QuestionDefinitionBuilder {
  private question: QuestionDefinition;

  constructor(questionType: QuestionType, targetFormControl: FormControl) {
    this.question = new QuestionDefinition(questionType, targetFormControl);
  }

  static create(
    targetFormControl: FormControl,
    questionType: QuestionType = QuestionType.YesNo,
  ): QuestionDefinitionBuilder {
    return new QuestionDefinitionBuilder(questionType, targetFormControl);
  }

  withText(text: string): QuestionDefinitionBuilder {
    this.question.text = text;
    return this;
  }

  withPlaceholder(placeholder: string): QuestionDefinitionBuilder {
    this.question.placeholder = placeholder;
    return this;
  }

  withConditionalTrail(
    condition: Condition,
    ...questions: QuestionDefinition[]
  ): QuestionDefinitionBuilder {
    const conditionalQuestion = new ConditionalQuestionBuilder(condition)
      .withQuestions(...questions)
      .build();

    this.question.conditionalTrails?.push(conditionalQuestion);

    return this;
  }

  withYesNoOptions(): QuestionDefinitionBuilder {
    this.question.options = buildYesNoOptions();
    return this;
  }

  withOptions(options: SelectOptions): QuestionDefinitionBuilder {
    this.question.options = options;
    return this;
  }

  withAddressOptions(addressOptions: Readonly<Address>): QuestionDefinitionBuilder {
    this.question.addressOptions = addressOptions;
    return this;
  }

  build(): Readonly<QuestionDefinition> {
    return this.question;
  }
}
