import { Signal } from '@angular/core';
import { AbstractControl, FormArray, FormControl, FormGroup, FormRecord } from '@angular/forms';
import { Address } from '@rocket-logic/rl-xp-bff-models';
import { Observable, of } from 'rxjs';
import { SelectOption } from '../../../refinance-info/checkbox-input/checkbox-input.component';
import { ControlMetadata } from '../../../util/build-demographic-information-form';
import { Controlify } from '../../../util/form-utility-types';
import { pascalCaseSplit } from '../../../util/formatting-helpers';
import { Condition, ConditionalTrailQuestion } from './conditional-question';
import { QuestionType } from './question-type.enum';

export type QuestionFormControl<T = any> =
  | FormControl<T>
  | FormArray<FormControl<T>>
  | FormRecord<FormControl<T>>
  | FormGroup<{ [K in keyof T]: AbstractControl<any, any> }>;

export class QuestionDefinition {
  constructor(
    public questionType: QuestionType,
    public targetFormControl: QuestionFormControl | null,
    public text?: Signal<string>,
    public placeholder?: string,
  ) {}

  conditionalTrails: Array<ConditionalTrailQuestion> = [];
  options?: SelectOptions;
  addressOptions?: Address;
  parentFormArray?: FormArray;
  addressFormControl?: FormGroup<Controlify<Address>> | null;
  addFunc?: () => void;
  isDisabled = false;
  orientation: Orientation = Orientation.Horizontal;
  metadata$: Observable<ControlMetadata> = of({
    isValid: true,
    errors: [],
    isCompleted: false,
    selectedValues: [],
  });

  addEntity() {
    if (this.addFunc) {
      this.addFunc();
    }
  }

  addConditionalTrail(condition: Condition, ...questions: Array<QuestionDefinition>) {
    const conditionalQuestion = new ConditionalTrailQuestion(condition, questions);

    this.conditionalTrails.push(conditionalQuestion);
  }

  addConditionalTrails(...conditionalTrail: ConditionalTrailQuestion[]) {
    this.conditionalTrails.push(...conditionalTrail);
  }

  withConditionalTrail(...options: Parameters<typeof this.addConditionalTrail>) {
    this.addConditionalTrail(...options);
    return this;
  }

  withSelectOptions(options: SelectOptions) {
    this.options = options;
    return this;
  }
}

export enum Orientation {
  Horizontal,
  Vertical,
}

export type SelectOptions = Array<
  {
    conditionalTrail?: ConditionalTrailQuestion;
    value: string | boolean | number;
  } & Omit<SelectOption, 'value'>
>;

export function mapToSelectOptions<T extends { [key: string]: string }>(enumObj: T): SelectOptions {
  return Object.keys(enumObj)
    .filter((key) => isNaN(Number(key)))
    .map((key) => ({
      value: enumObj[key],
      label: pascalCaseSplit(key),
    }));
}

export function enumValuesExcept<T extends { [key: string]: string }>(
  enumObj: T,
  ...excluded: T[keyof T][]
): { [key: string]: string } {
  return Object.fromEntries(
    Object.entries(enumObj).filter(([_, v]) => !excluded.includes(v as any)),
  );
}
