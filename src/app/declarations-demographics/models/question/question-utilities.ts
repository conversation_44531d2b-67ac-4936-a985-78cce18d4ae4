import { signal } from '@angular/core';
import { FormArray, FormControl, FormGroup } from '@angular/forms';
import { Address, LiabilityType } from '@rocket-logic/rl-xp-bff-models';
import { AllLiabilityGroup } from '../../../services/entity-state/liability-state/form-types';
import { LiabilityFormService } from '../../../services/entity-state/liability-state/liability-form.service';
import { formatAddress } from '../../../util/format-address';
import { LiabilityDeclarationsIdListenerService } from '../../services/liability-declarations-id-listener.service';
import {
  DeclarationMainQuestion,
  SUBJECT_PROPERTY_PLACEHOLDER,
} from '../declaration/declaration-main-question.enum';
import { DeclarationTrailQuestion } from '../declaration/declaration-trail-question.enum';
import { Condition, ConditionalTrailQuestion, IQuestionTextContent } from './conditional-question';
import { QuestionDefinition, SelectOptions } from './question-definition';
import { QuestionType } from './question-type.enum';

export function buildYesNoOptions(): SelectOptions {
  return [
    { value: true, label: 'Yes' },
    { value: false, label: 'No' },
  ];
}

export function replaceAddressPlaceholder(
  target: string,
  address?: Address,
  searchValue: string = SUBJECT_PROPERTY_PLACEHOLDER,
): string {
  return target.replaceAll(searchValue, `[${formatAddress(address)}]`);
}

export type LiabilityTrailBuilderFunc = (liabilityFormGroup: {
  formKey: string;
  form: AllLiabilityGroup;
}) => ConditionalTrailQuestion[];

export function createLiabilityQuestion(
  liabilityGroups: { formKey: string; form: AllLiabilityGroup }[],
  targetControl: FormControl | undefined,
  mainQuestionText: DeclarationMainQuestion,
  trailBuilderFunc: LiabilityTrailBuilderFunc,
): QuestionDefinition | null {
  if (!targetControl) {
    return null;
  }

  const trails = liabilityGroups.flatMap((liabilityGroup) => trailBuilderFunc(liabilityGroup));

  const question = new QuestionDefinition(
    QuestionType.YesNo,
    targetControl,
    signal(mainQuestionText),
  );
  question.addConditionalTrails(...trails);

  return question;
}

export type LiabilityQuestionBuilderFunc = (liabilityFormGroup: {
  formKey: string;
  form: AllLiabilityGroup;
}) => QuestionDefinition[];

export function createLiabilityTrails(
  parentControl: FormControl,
  liabilityFormGroup: { formKey: string; form: AllLiabilityGroup },
  trailQuestionText: DeclarationTrailQuestion,
  questionBuilderFunc: LiabilityQuestionBuilderFunc,
  questionText: IQuestionTextContent,
  liabilityFormService: LiabilityFormService,
  idListener: LiabilityDeclarationsIdListenerService,
  idArrayRef: FormArray,
  skipPaymentPlanTrail = false,
  clientId: string,
): ConditionalTrailQuestion[] {
  const { form, formKey } = liabilityFormGroup;
  const paymentPlanControl = form.controls.hasPaymentPlan;

  initializeHasPaymentPlanControl(form, trailQuestionText);

  const baseTrails = questionBuilderFunc(liabilityFormGroup);
  const { text, placeholder } =
    trailQuestionText === DeclarationTrailQuestion.PayingAlimonyOrChildSupportArePaymentsPastDue
      ? {
          text: DeclarationTrailQuestion.PayingAlimonyOrChildSupportPastDueBalance,
          placeholder: 'Past Due Balance',
        }
      : {
          text: DeclarationTrailQuestion.PaymentAmount,
          placeholder: 'Monthly Amount',
        };

  const paymentPlanTrail = new QuestionDefinition(
    QuestionType.YesNo,
    paymentPlanControl,
    signal(trailQuestionText),
  ).withConditionalTrail(
    new Condition(paymentPlanControl, (value) => value),
    new QuestionDefinition(
      QuestionType.Currency,
      trailQuestionText === DeclarationTrailQuestion.PayingAlimonyOrChildSupportArePaymentsPastDue
        ? form.controls.unpaidBalance
        : form.controls.monthlyPaymentAmount,
      signal(text),
      placeholder,
    ),
  );

  const conditionalQuestion = [
    new ConditionalTrailQuestion(
      new Condition(parentControl),
      skipPaymentPlanTrail ? [...baseTrails] : [...baseTrails, paymentPlanTrail],
      questionText,
    ),
  ];

  conditionalQuestion.forEach((question) => {
    question.deleteFunc = liabilityDeleteFunc(formKey, liabilityFormService);
  });

  idListener.addListener(liabilityFormGroup.form, liabilityFormGroup.formKey, idArrayRef, clientId);

  return conditionalQuestion;
}

export function formArrayAddFunc(formArray: FormArray, buildFormFunc: () => FormGroup) {
  return () => {
    if (formArray) {
      formArray.push(buildFormFunc());
    }
  };
}

export function liabilityFormArrayAddFunc(
  clientId: string,
  liabilityService: LiabilityFormService,
  liabilityType: LiabilityType,
  decsQuestion: DeclarationMainQuestion,
) {
  return () => {
    if (clientId && liabilityService) {
      liabilityService.addLiability(liabilityType, undefined, [clientId], decsQuestion);
    }
  };
}

export function liabilityDeleteFunc(formKey: string, liabilityService: LiabilityFormService) {
  return () => {
    if (formKey && liabilityService) {
      liabilityService.deleteLiability(formKey);
    }
  };
}

export function formArrayDeleteFunc(formArray: FormArray | undefined, form: FormGroup) {
  return () => {
    if (formArray && form) {
      const indexInParentArray = formArray.controls.indexOf(form);
      if (indexInParentArray !== -1) {
        formArray.markAsDirty();
        formArray.removeAt(indexInParentArray);
      }
    }
  };
}

export function initializeHasPaymentPlanControl(
  liabilityFormGroup: AllLiabilityGroup,
  trailQuestionText: DeclarationTrailQuestion,
) {
  const paymentPlanControl = liabilityFormGroup.controls.hasPaymentPlan;
  if (paymentPlanControl.value != null) {
    return;
  }

  if (
    trailQuestionText === DeclarationTrailQuestion.PayingAlimonyOrChildSupportArePaymentsPastDue
  ) {
    const unpaidBalanceControl = liabilityFormGroup.controls.unpaidBalance;
    const hasPaymentPlan =
      unpaidBalanceControl.value != null ? unpaidBalanceControl.value > 0 : false;

    paymentPlanControl.setValue(hasPaymentPlan, { emitEvent: false });
    return;
  }

  const monthlyPaymentAmountControl = liabilityFormGroup.controls.monthlyPaymentAmount;
  const hasPaymentPlan =
    monthlyPaymentAmountControl.value != null ? monthlyPaymentAmountControl.value > 0 : false;

  paymentPlanControl.setValue(hasPaymentPlan, { emitEvent: false });
}
