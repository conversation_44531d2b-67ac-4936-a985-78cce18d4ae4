import { Condition, ConditionalTrailQuestion, IQuestionTextContent } from './conditional-question';
import { QuestionDefinition } from './question-definition';

export class ConditionalQuestionBuilder {
  private conditionalTrailQuestion: ConditionalTrailQuestion;

  constructor(condition: Condition, textContent: IQuestionTextContent = {}) {
    this.conditionalTrailQuestion = new ConditionalTrailQuestion(condition, [], textContent);
  }

  withQuestions(...questions: <PERSON>onlyA<PERSON>y<QuestionDefinition>): ConditionalQuestionBuilder {
    this.conditionalTrailQuestion.trailQuestions.push(...questions);
    return this;
  }

  build(): ConditionalTrailQuestion {
    return this.conditionalTrailQuestion;
  }
}
