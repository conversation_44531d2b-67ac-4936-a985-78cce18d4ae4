import { ConditionalTrailQuestion } from './conditional-question';

export class ConditionalTrailBuilder {
  private readonly conditionalTrails: Array<ConditionalTrailQuestion>;

  constructor() {
    this.conditionalTrails = [];
  }

  static create(): ConditionalTrailBuilder {
    return new ConditionalTrailBuilder();
  }

  addTrail(...conditionalQuestions: ConditionalTrailQuestion[]): Readonly<ConditionalTrailBuilder> {
    this.conditionalTrails.push(...conditionalQuestions);
    return this;
  }

  build(): ReadonlyArray<ConditionalTrailQuestion> {
    return this.conditionalTrails;
  }
}
