import { AbstractControl, FormControl } from '@angular/forms';
import { distinctUntilChanged, map, shareReplay, startWith } from 'rxjs';
import { QuestionDefinition } from './question-definition';

export type ConditionCheckerFunc<T = any> = (targetControl: FormControl<T> | T) => boolean;
export class Condition {
  constructor(
    public readonly targetControl: AbstractControl,
    public readonly conditionCheckerFunc: ConditionCheckerFunc = <T>(value: T) => !!value,
  ) {}

  isMet$ = this.targetControl.valueChanges.pipe(
    startWith(this.targetControl.value),
    distinctUntilChanged(),
    map((value) => this.conditionCheckerFunc(value)),
    shareReplay(1),
  );
}

export class ConditionalTrailQuestion {
  constructor(
    public readonly condition: Condition,
    public trailQuestions: Array<QuestionDefinition>,
    public readonly textContent?: IQuestionTextContent,
  ) {}

  deleteFunc?: () => void;

  deleteEntity() {
    if (this.deleteFunc) {
      this.deleteFunc();
    }
  }

  attachDeleteFunc(delteFunc: () => void) {
    this.deleteFunc = delteFunc;
    return this;
  }

  addTrail(...trailQuestions: QuestionDefinition[]) {
    this.trailQuestions.push(...trailQuestions);
  }
}

export interface IQuestionTextContent {
  readonly text?: string;
  readonly bannerText?: string;
  readonly sectionTitle?: string;
}
