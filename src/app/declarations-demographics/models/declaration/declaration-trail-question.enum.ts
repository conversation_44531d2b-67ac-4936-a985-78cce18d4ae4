import { environment } from '../../../../environments/environment';
import { SUBJECT_PROPERTY_PLACEHOLDER } from './declaration-main-question.enum';

export const DeclarationTrails = {
  HasLoansInForbearance: {
    ConfirmationText: `Confirm if the client meets forbearance guidelines using the <a target="_blank" href=${environment.declarations.forbearanceDecisionToolUrl}>Forbearance Decisioning Tool</a>.`,
  },
} as const;

export const DeclarationBanners = {
  CreditQualifier:
    'Information provided will be entered in Credit Qualifier. If already added here, please exclude the item from Credit Qualifier.',
} as const satisfies Record<string, string>;

export enum DeclarationTrailQuestion {
  JudgmentHolder = 'Who holds the judgment against you?',
  JudgmentAmount = 'How much is the judgment for?',
  JudgmentPaymentPlan = 'Are you on a payment plan for the judgment?',

  BankruptcyType = 'What type of bankruptcy?',
  BankruptcyStatus = 'What is the current status of your bankruptcy?',
  BankruptcyDoneDate = 'When was your bankruptcy discharged or dismissed?',
  BankruptcyIsPropertyIncluded = 'Do you currently own any properties that were included in the bankruptcy that you have not already disclosed?',
  BankruptcyHasFinancing = 'Do you have financing on the property?',
  BankruptcyPropertyAddress = 'What was the address of the property that was included in the bankruptcy?',

  ForeclosureDate = 'When was the foreclosure finalized?',
  ForeclosureAddress = 'What was the address of the property that was included in the foreclosure?',
  ForeclosureHasFinancing = 'Did you have financing on the property when the home was foreclosed?',

  LawsuitRole = 'Are you the plaintiff or defendant in the lawsuit?',
  LawsuitStatus = 'Is the lawsuit on-going or complete?',
  LawsuitCompletionBeforeClosing = 'Will the lawsuit be completed prior to this loan closing?',
  LawsuitMaximumPenalty = 'Approximately what is your maximum penalty?',

  PropertyForfeitedDate = 'When was the title to the property forfeited?',
  PropertyForfeitedAddress = 'What was the address of the property that was forfeited?',
  PropertyForfeitedHasMortgageLender = 'Did you have financing on the property when it was forfeited?',

  PreForeclosureSaleDate = 'When was the sale finalized?',
  PreForeclosureProperty = 'What was the address of the property that was on pre-foreclosure or short sale?',

  DelinquentFederalDebtAmount = 'How much do you owe to the federal government?',
  DelinquentFederalIsTaxDebt = 'Is this a tax debt?',
  DelinquentFederalIsOnDebtPaymentPlan = 'Are you on a payment plan for the federal debt?',

  PayingAlimonyOrChildSupportPaymentType = 'Which are you paying, alimony, child support, or separate maintenance?',
  PayingAlimonyOrChildSupportPayeeName = 'Who is the debt paid to?',
  PayingAlimonyOrChildSupportRemainingMonths = 'How many months are remaining?',
  PayingAlimonyOrChildSupportObligationAmount = 'How much are you obligated to pay?',
  PayingAlimonyOrChildSupportArePaymentsPastDue = 'Do you have any payments considered past due or in arrears?',
  PayingAlimonyOrChildSupportPastDueBalance = 'You must bring any past due payments current prior to or at closing. Approximately, what is the past due balance?',

  BorrowingMoneyFrom = 'Who are you borrowing from?',
  BorrowingMoneyAmount = 'How much are you borrowing?',
  BorrowingMoneyMoneyOnPaymentPlan = 'Are you on a payment plan for the borrowing money?',

  CoSignerOrGuarantorDebtLender = 'Who is the debt or loan with?',
  CoSignerOrGuarantorOwedAmount = 'Approximately how much is owed on the account?',
  CoSignerOrGuarantorDebtType = 'What type of debt is it?',
  CoSignerOrGuarantorIsDebtOnPaymentPlan = 'Are you on a payment plan for the owned amount?',

  PrimaryHasOwnershipInterestLastThreeYears = `Have you had an ownership interest in another property besides ${SUBJECT_PROPERTY_PLACEHOLDER} in the last three years?`,
  PrimaryType = 'What type of property did you own?',
  PrimaryTitleHolding = 'How did you hold title to the property?',

  ApplyingForAnotherMortgagePropertyAddress = 'What is the address of the other property?',
  ApplyingForAnotherMortgagePurpose = 'Are you purchasing or refinancing the property?',
  ApplyingForAnotherMortgageLender = 'Who is the new mortgage lender?',
  ApplyingForAnotherMortgageBalance = 'What is the new balance for the loan?',

  NewCreditLender = 'Who is the new account lender?',
  NewCreditOwedAmount = 'How much is or will be owed on the account?',

  RelationshipWithSeller = 'What is your relationship with the seller?',
  RelationshipWithSellerFamilyRelationship = 'What is your family relationship?',

  // Shared Questions
  PaymentAmount = 'How much are you paying?',
  PaymentFrequency = 'What is the payment frequency?',
  WhoWasTheLender = 'Who was the lender on the property?',
}
