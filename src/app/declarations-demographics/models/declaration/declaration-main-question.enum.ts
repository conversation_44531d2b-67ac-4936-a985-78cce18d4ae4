export const SUBJECT_PROPERTY_PLACEHOLDER = '{Subject Address}';

export enum DeclarationMainQuestion {
  HasOutstandingJudgments = 'Are there any <strong>outstanding judgments</strong> against you that you have not already disclosed?',
  HasRecentlyDeclaredBankruptcy = 'Have you declared <strong>bankruptcy</strong> within the past 7 years?',
  HasRecentlyHadForeclosure = 'Have you had <strong>property foreclosed</strong> upon in the last 7 years?',
  IsCurrentlyPartyToLawsuit = 'Are you a party to a lawsuit in which you potentially have any <strong>personal financial liability</strong>?',
  HasForfeitedTitle = 'Have you <strong>forfeited title to a property</strong> instead of foreclosing in the past 7 years?',
  HasRecentlyHadPreForeclosureOrShortSale = 'Within the past 7 years, have you completed a <strong>pre-foreclosure sale or short sale</strong>, whereby the property was sold to a third party and the Lender agreed to accept less than the outstanding mortgage balance due?</strong',
  HasDelinquentFederalDebt = 'Are you currently <strong>delinquent</strong> or in default on a federal debt? <br /><br /> Examples: Direct loans, Student loans, Small business administration loans, or judgements/liens.',
  IsPayingAlimonyOrChildSupport = 'Are you obligated to pay <strong>alimony, child support, or support maintenance</strong>?',
  IsBorrowingMoney = 'Excluding gifts already disclosed, are you <strong>borrowing any money</strong> for this real estate transaction (e.g. money for your closing costs or down payment) or obtaining any money from another party, such as the seller or realtor, that you have not disclosed on this loan application?',
  IsCoSignerOrGuarantor = 'Are you a <strong>co-signer or guarantor</strong> on any debt or loan that is not disclosed on this application?',
  IsUSCitizen = 'What is your <strong>citizenship</strong>?',
  IsPrimaryResidence = `Will you occupy ${SUBJECT_PROPERTY_PLACEHOLDER} as your <strong>primary residence</strong>?`,
  ApplyingForAnotherMortgage = `Excluding this transaction, have you or will you be <strong>applying for a mortgage</strong> for another property or or before closing on ${SUBJECT_PROPERTY_PLACEHOLDER}.`,
  ApplyingForNewCreditAccount = `Have you or will you be <strong>applying for any new credit accounts</strong> on or before closing on ${SUBJECT_PROPERTY_PLACEHOLDER}. <br /><br /> Examples: Auto loans, Credit cards, Installment loans, etc.`,
  NewLienDetails = `Will ${SUBJECT_PROPERTY_PLACEHOLDER} be subject to new lien that could take priority over the first mortgage lien, such as a clean energy lien paid through your property taxes? <br /><br /> Examples: HERO liens, PACE liens or clean energy liens.`,
  RelationshipWithSeller = 'Do you have a direct relationship (friend, family, business, etc.) with the seller of the property?',
  HasLoansInForbearance = 'Do you have any <strong>mortgage loans that are currently in forbearance</strong> or were in forbearance in the last 12 months? <br /><br /> This includes mortgages that have had payments deferred, in workout plans or modified.',
}
