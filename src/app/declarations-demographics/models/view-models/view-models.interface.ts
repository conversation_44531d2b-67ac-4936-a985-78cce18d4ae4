import { FormGroup } from '@angular/forms';
import { Client } from '@rocket-logic/rl-xp-bff-models';
import { ClientControls } from '../../../services/entity-state/client-state/form-types';
import { OwnedPropertyControls } from '../../../services/entity-state/owned-property-state/form-types';
import { TileIcon, TileVariant } from '../../../tile/tile.component';

export type ClientPresentationViewModel = IPresentation<TileVariant, TileIcon>;
export type OwnedPropertyPresentationViewModel = IPresentation<TileVariant, TileIcon>;

export interface IClientsViewModel {
  readonly primaryClient: Readonly<IClientViewModel> | null;
  readonly coClients: ReadonlyArray<Readonly<IClientViewModel>>;
}

export interface IClientViewModel
  extends Pick<Client, 'id' | 'isPrimaryBorrower'>,
    ISelectionState {
  readonly fullName: string;
  role: string;
  presentation: ClientPresentationViewModel;
  clientForm: FormGroup<ClientControls> | undefined;
  clientFormId: string;
}

export interface IOwnedPropertyViewModel {
  presentation: OwnedPropertyPresentationViewModel;
  ownedPropertyForm: FormGroup<OwnedPropertyControls> | undefined;
  ownedPropertyFormId: string;
  ownedPropertyAddress: string;
  isSubjectProperty: boolean;
  isCurrentResidence: boolean;
}

export interface IPresentation<out TVariant, out TIcon extends string> extends IVariant<TVariant> {
  readonly icon: TIcon;
}

export interface ISelectionState {
  readonly isSelected: boolean;
}

export interface IVariant<out TVariant> {
  readonly variant: TVariant;
}
