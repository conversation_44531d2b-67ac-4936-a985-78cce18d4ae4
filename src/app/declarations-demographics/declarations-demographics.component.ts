import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, inject } from '@angular/core';
import { ClientTileDisplayComponent } from '../left-nav-content/shared/client-tile-display/client-tile-display.component';
import { AppLeftNavErrorsComponent } from '../left-nav-content/shared/form-errors/left-nav-errors.component';
import { EntityStateName } from '../services/entity-state/abstract-entity-state.service';
import { DeclarationsComponent } from './declarations/declarations.component';
import { DemographicsComponent } from './demographics/demographics.component';
import { ClientCategorizationFacadeService } from './services/client-categorization.service';

@Component({
  selector: 'app-declarations-demographics',
  standalone: true,
  imports: [
    CommonModule,
    ClientTileDisplayComponent,
    DeclarationsComponent,
    DemographicsComponent,
    AppLeftNavErrorsComponent,
  ],
  templateUrl: './declarations-demographics.component.html',
  styleUrl: './declarations-demographics.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DeclarationsDemographicsComponent {
  readonly entityName = [EntityStateName.Client];
  readonly selectedFormId = inject(ClientCategorizationFacadeService).selectedFormId;
}
