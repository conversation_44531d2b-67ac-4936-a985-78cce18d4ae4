@if (sectionStats(); as sectionStats) {
  <app-section-progress-tag
    [title]="'Declarations'"
    [completedSections]="sectionStats.completedSections"
    [total]="sectionStats.total"
  />
}

@if (clientQuestions(); as questions) {
  @for (questionSignal of questions; track $index) {
    @if (questionSignal.question; as question) {
      <app-question-group
        [parentQuestionIndex]="$index + 1 | numberToLetter"
        [isTopLevel]="true"
        [questionInput]="[question]"
        [section]="questionSignal.name"
        [clientId]="this.clientService.selectedFormId()"
      />
    }
  }
}
