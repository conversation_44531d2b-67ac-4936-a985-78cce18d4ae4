import { Component, computed, inject, Signal } from '@angular/core';
import { MatIconModule } from '@angular/material/icon';
import { RktTagEnterpriseModule } from '@rocketcentral/rocket-design-system-enterprise-angular';
import { NumberToLetterPipe } from '../../util/number-to-letter.pipe';
import { QuestionGroupComponent } from '../components/question-group/question-group.component';
import { SectionProgressTagComponent } from '../components/section-progress-tag/section-progress-tag.component';
import { ClientCategorizationFacadeService } from '../services/client-categorization.service';
import { DecsDemsCompletionService } from '../services/decs-dems-completion.service';
import { DecsAndDemsFormFacadeService } from '../services/decs-dems-form.service';

@Component({
  selector: 'app-declarations',
  standalone: true,
  imports: [
    QuestionGroupComponent,
    MatIconModule,
    RktTagEnterpriseModule,
    NumberToLetterPipe,
    SectionProgressTagComponent,
  ],
  providers: [DecsDemsCompletionService],
  templateUrl: './declarations.component.html',
  styleUrl: './declarations.component.scss',
})
export class DeclarationsComponent {
  readonly formFacade = inject(DecsAndDemsFormFacadeService);
  readonly clientService = inject(ClientCategorizationFacadeService);  
  private readonly decsDemsCompletionService = inject(DecsDemsCompletionService);

  readonly clientQuestions = computed(() =>
    this.formFacade.clientQuestionMap().get(this.clientService.selectedFormId() ?? ''),
  );

  readonly sectionStats: Signal<{ completedSections: number; total: number }> = computed(() => {
    const selectedClientId = this.clientService.selectedFormId();
    if (!selectedClientId) {
      return { completedSections: 0, total: 0 };
    }

    const sections = Array.from(
      this.decsDemsCompletionService.sectionCompletionStatus().entries(),
    ).filter(([sectionKey]) => sectionKey.includes(selectedClientId));

    const total = sections.length;
    const completed = sections.filter(([_, complete]) => complete).length;

    return { completedSections: completed, total };
  });
}
