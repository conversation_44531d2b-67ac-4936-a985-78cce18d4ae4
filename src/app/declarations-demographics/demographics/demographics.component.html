@if (questionsMetadata(); as questionsMetadata) {
  <app-section-progress-tag
    [title]="'Demographics'"
    [completedSections]="questionsMetadata.completedSections | async"
    [total]="questionsMetadata.total"
  />
  <section class="flex flex-col gap-3">
    <rkt-alert-enterprise variant="info" [isDismissible]="false">
      <div class="rkt-Alert__text">
        <div class="rkt-Body-14"><strong>Disclaimer For Client</strong></div>
        <span class="rkt-Body-14"
          >"The following questions are required to be asked to comply with Federal Lending Laws
          that prohibit creditors from discrimination against applicants. Answering these questions
          is optional but encouraged."
        </span>
      </div>
    </rkt-alert-enterprise>
    @if (questionsMetadata.questions; as questions) {
      @for (question of questionsMetadata.questions; track $index) {
        <app-question-group
          [parentQuestionIndex]="$index + 1 | numberToLetter"
          [isTopLevel]="true"
          [variant]="questionGroupVariant.Accordion"
          [questionInput]="[question]"
          [clientId]="this.clientService.selectedFormId()"
        />
      }
    }
  </section>
  <div class="flex items-center gap-2 rkt-Spacing--mt12">
    <mat-icon svgIcon="help-outlined" />
    <a
      rktLink
      class="rkt-Caption-12 rkt-Color--blue-500 underline cursor-pointer"
      (click)="openDialog()"
      role="button"
      tabindex="0"
      (keydown.enter)="openDialog()"
      (keydown.space)="openDialog()"
    >
      How demographic information is collected?
    </a>
  </div>
}
