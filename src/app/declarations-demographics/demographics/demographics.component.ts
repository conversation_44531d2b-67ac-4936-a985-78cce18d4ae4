import { CommonModule } from '@angular/common';
import {
  ChangeDetectionStrategy,
  Component,
  computed,
  DestroyRef,
  inject,
  Injector,
} from '@angular/core';
import { takeUntilDestroyed, toObservable } from '@angular/core/rxjs-interop';
import { FormGroup } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import {
  RktAlertEnterpriseModule,
  RktTagEnterpriseModule,
} from '@rocketcentral/rocket-design-system-enterprise-angular';
import { combineLatest, distinctUntilChanged, EMPTY, map, merge, switchMap } from 'rxjs';
import { ClientControls } from '../../services/entity-state/client-state/form-types';
import { handleDemographicFormValueChanges } from '../../util/build-demographic-information-form';
import { handleLanguagePreferenceFormValueChanges } from '../../util/build-supplemental-consumer-information';
import { NumberToLetterPipe } from '../../util/number-to-letter.pipe';
import { CollectionMethodDialogComponent } from '../components/collection-method/collection-method-dialog.component';
import {
  QuestionGroupComponent,
  QuestionGroupVariant,
} from '../components/question-group/question-group.component';
import { SectionProgressTagComponent } from '../components/section-progress-tag/section-progress-tag.component';
import { ClientCategorizationFacadeService } from '../services/client-categorization.service';
import { DecsAndDemsFormFacadeService } from '../services/decs-dems-form.service';
import {
  buildEthnicityQuestion,
  buildGenderQuestion,
  buildPreferredLanguageQuestion,
  buildRaceQuestion,
} from '../services/demographics-question-helpers';

@Component({
  selector: 'app-demographics',
  standalone: true,
  imports: [
    QuestionGroupComponent,
    MatIconModule,
    RktTagEnterpriseModule,
    NumberToLetterPipe,
    CommonModule,
    RktAlertEnterpriseModule,
    SectionProgressTagComponent,
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
  templateUrl: './demographics.component.html',
  styleUrl: './demographics.component.scss',
})
export class DemographicsComponent {
  protected readonly formFacade = inject(DecsAndDemsFormFacadeService);
  protected readonly questionGroupVariant = QuestionGroupVariant;
  protected readonly dialog = inject(MatDialog);
  protected readonly injector = inject(Injector);
  protected readonly clientService = inject(ClientCategorizationFacadeService);
  protected readonly destroyRef = inject(DestroyRef);

  protected readonly clientForm = computed(() => this.clientService.selectedClientForm());

  constructor() {
    this.handleDemographicFormValueChanges();
  }

  protected readonly questionsMetadata = computed(() => {
    const questions = this.generateDemographicsQuestions(this.clientForm());
    const questionsMetadata$ = questions.map((question) => question.metadata$);

    return {
      questions,
      completedSections: combineLatest(questionsMetadata$).pipe(
        map(
          (questionMetadata) => questionMetadata.filter((metadata) => metadata.isCompleted).length,
        ),
      ),
      total: questions.length,
    };
  });

  protected openDialog(): void {
    this.dialog.open(CollectionMethodDialogComponent, {
      panelClass: ['rkt-Dialog', 'rkt-Dialog--enterprise'],
      backdropClass: 'rkt-Backdrop',
      minWidth: '50%',
      disableClose: false,
      injector: this.injector,
    });
  }

  private handleDemographicFormValueChanges() {
    toObservable(this.clientForm)
      .pipe(
        distinctUntilChanged(),
        switchMap((form) => {
          if (!form) return EMPTY;
          return merge(
            handleLanguagePreferenceFormValueChanges(
              form.controls.supplementalConsumerInformation.controls.languagePreference,
            ),
            handleDemographicFormValueChanges(form.controls.demographicInformation),
          );
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }

  private generateDemographicsQuestions(clientForm: FormGroup<ClientControls>) {
    const { demographicInformation, supplementalConsumerInformation } = clientForm.controls;
    if (!demographicInformation || !supplementalConsumerInformation) return [];
    return [
      buildEthnicityQuestion(demographicInformation),
      buildGenderQuestion(demographicInformation),
      buildRaceQuestion(demographicInformation),
      buildPreferredLanguageQuestion(supplementalConsumerInformation),
    ];
  }
}
