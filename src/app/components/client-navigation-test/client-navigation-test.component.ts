import { Component, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { RktAlertEnterpriseModule } from '@rocketcentral/rocket-design-system-enterprise-angular';
import { PricingErrorNavigationService } from '../../services/pricing-navigation/pricing-error-navigation.service';
import { AutoPricingLinksDirective } from '../../directives/auto-pricing-links.directive';
import { PricingFieldLinkDirective } from '../../directives/pricing-field-link.directive';

/**
 * Test component to verify client popover navigation functionality
 */
@Component({
  selector: 'app-client-navigation-test',
  standalone: true,
  imports: [
    CommonModule,
    MatButtonModule,
    MatCardModule,
    RktAlertEnterpriseModule,
    AutoPricingLinksDirective,
    PricingFieldLinkDirective
  ],
  template: `
    <div class="test-container">
      <h2>Client Popover Navigation Test</h2>
      
      <!-- Test Error Message with Auto Links -->
      <mat-card class="test-card">
        <mat-card-header>
          <mat-card-title>Auto-Enhanced Error Message</mat-card-title>
          <mat-card-subtitle>Error message with automatic First Name link detection</mat-card-subtitle>
        </mat-card-header>
        <mat-card-content>
          <rkt-alert-enterprise variant="warn">
            <div class="rkt-Alert__text" appAutoPricingLinks>
              To save, fix 1 error:
              <ul>
                <li>Client information: First Name is required</li>
              </ul>
            </div>
          </rkt-alert-enterprise>
        </mat-card-content>
      </mat-card>

      <!-- Test Manual Field Link -->
      <mat-card class="test-card">
        <mat-card-header>
          <mat-card-title>Manual Field Link</mat-card-title>
          <mat-card-subtitle>Manually specified First Name link</mat-card-subtitle>
        </mat-card-header>
        <mat-card-content>
          <rkt-alert-enterprise variant="warn">
            <div class="rkt-Alert__text">
              To save, fix 1 error:
              <ul>
                <li appPricingFieldLink="firstName" linkText="First Name">
                  Client information: First Name is required
                </li>
              </ul>
            </div>
          </rkt-alert-enterprise>
        </mat-card-content>
      </mat-card>

      <!-- Test Programmatic Navigation -->
      <mat-card class="test-card">
        <mat-card-header>
          <mat-card-title>Programmatic Navigation</mat-card-title>
          <mat-card-subtitle>Test navigation via service calls</mat-card-subtitle>
        </mat-card-header>
        <mat-card-content>
          <div class="button-grid">
            <button 
              mat-raised-button 
              color="primary"
              (click)="testNavigation('firstName')">
              Navigate to First Name
            </button>
            
            <button 
              mat-raised-button 
              color="primary"
              (click)="testNavigation('lastName')">
              Navigate to Last Name
            </button>
            
            <button 
              mat-raised-button 
              color="primary"
              (click)="testNavigation('personalInformation.firstName')">
              Navigate to First Name (Full Path)
            </button>
          </div>
        </mat-card-content>
      </mat-card>

      <!-- Test Different Error Formats -->
      <mat-card class="test-card">
        <mat-card-header>
          <mat-card-title>Various Error Message Formats</mat-card-title>
          <mat-card-subtitle>Test different ways the error might appear</mat-card-subtitle>
        </mat-card-header>
        <mat-card-content>
          <div class="error-examples" appAutoPricingLinks>
            <div class="error-item">
              <strong>Format 1:</strong> First Name is required
            </div>
            <div class="error-item">
              <strong>Format 2:</strong> Client information: First Name cannot be empty
            </div>
            <div class="error-item">
              <strong>Format 3:</strong> Please provide a valid First Name
            </div>
            <div class="error-item">
              <strong>Format 4:</strong> The First Name field must be completed
            </div>
          </div>
        </mat-card-content>
      </mat-card>

      <!-- Navigation Status -->
      <mat-card class="test-card">
        <mat-card-header>
          <mat-card-title>Navigation Status</mat-card-title>
          <mat-card-subtitle>Check which client fields can be navigated to</mat-card-subtitle>
        </mat-card-header>
        <mat-card-content>
          <div class="status-grid">
            @for (field of clientFields; track field.path) {
              <div class="status-item" [class.navigable]="canNavigate(field.path)">
                <span class="field-name">{{ field.name }}</span>
                <span class="status-indicator">
                  {{ canNavigate(field.path) ? '✅ Navigable' : '❌ Not Available' }}
                </span>
              </div>
            }
          </div>
        </mat-card-content>
      </mat-card>

      <!-- Instructions -->
      <mat-card class="test-card">
        <mat-card-header>
          <mat-card-title>Test Instructions</mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <ol>
            <li>Click on any "First Name" link in the error messages above</li>
            <li>The system should automatically open the client popover</li>
            <li>Navigate to the "Client Info" tab if not already there</li>
            <li>Scroll to and focus the First Name input field</li>
            <li>The field should be highlighted temporarily</li>
          </ol>
          <p><strong>Note:</strong> Make sure you have client data loaded and the left panel is available for this test to work properly.</p>
        </mat-card-content>
      </mat-card>
    </div>
  `,
  styles: [`
    .test-container {
      max-width: 800px;
      margin: 20px auto;
      padding: 20px;
    }
    
    .test-card {
      margin-bottom: 24px;
    }
    
    .button-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 12px;
    }
    
    .error-examples {
      background: #fff3cd;
      border: 1px solid #ffeaa7;
      border-radius: 4px;
      padding: 16px;
    }
    
    .error-item {
      margin: 8px 0;
      padding: 4px 0;
      border-bottom: 1px solid #ffeaa7;
    }
    
    .error-item:last-child {
      border-bottom: none;
    }
    
    .status-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 12px;
    }
    
    .status-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px 12px;
      border-radius: 4px;
      background: #f8f9fa;
      border: 1px solid #dee2e6;
    }
    
    .status-item.navigable {
      background: #d4edda;
      border-color: #c3e6cb;
    }
    
    .field-name {
      font-weight: 500;
    }
    
    .status-indicator {
      font-size: 12px;
    }
    
    :host ::ng-deep .pricing-field-link {
      color: #007bff !important;
      font-weight: 500;
    }
    
    :host ::ng-deep .pricing-field-link:hover {
      color: #0056b3 !important;
      background-color: rgba(0, 123, 255, 0.1);
    }
  `]
})
export class ClientNavigationTestComponent {
  private readonly errorNavService = inject(PricingErrorNavigationService);

  // Test client fields
  readonly clientFields = [
    { name: 'First Name', path: 'firstName' },
    { name: 'Last Name', path: 'lastName' },
    { name: 'Middle Name', path: 'middleName' },
    { name: 'First Name (Full Path)', path: 'personalInformation.firstName' },
    { name: 'Last Name (Full Path)', path: 'personalInformation.lastName' },
    { name: 'Middle Name (Full Path)', path: 'personalInformation.middleName' }
  ];

  canNavigate(fieldPath: string): boolean {
    return this.errorNavService.canNavigateToField(fieldPath);
  }

  async testNavigation(fieldPath: string): Promise<void> {
    console.log(`Testing navigation to: ${fieldPath}`);
    
    try {
      const success = await this.errorNavService.navigateToFieldFromError(fieldPath, {
        focus: true,
        highlight: true,
        scrollBehavior: 'smooth'
      });

      if (success) {
        console.log(`✅ Successfully navigated to ${fieldPath}`);
      } else {
        console.warn(`❌ Failed to navigate to ${fieldPath}`);
      }
    } catch (error) {
      console.error(`❌ Error navigating to ${fieldPath}:`, error);
    }
  }
}
