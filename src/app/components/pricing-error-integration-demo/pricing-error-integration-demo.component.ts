import { Component, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { RktAlertEnterpriseModule } from '@rocketcentral/rocket-design-system-enterprise-angular';
import { EnhancedErrorDisplayComponent } from '../enhanced-error-display/enhanced-error-display.component';
import { AutoPricingLinksDirective } from '../../directives/auto-pricing-links.directive';
import { PricingFieldLinkDirective } from '../../directives/pricing-field-link.directive';
import { PricingErrorNavigationService } from '../../services/pricing-navigation/pricing-error-navigation.service';
import { ValidationErrorDto } from '../../services/error/validation-error-provider.service';
import { EntityStateName } from '../../services/entity-state/abstract-entity-state.service';

/**
 * Demo component showing all methods of integrating pricing navigation with error messages
 */
@Component({
  selector: 'app-pricing-error-integration-demo',
  standalone: true,
  imports: [
    CommonModule,
    MatButtonModule,
    MatCardModule,
    RktAlertEnterpriseModule,
    EnhancedErrorDisplayComponent,
    AutoPricingLinksDirective,
    PricingFieldLinkDirective
  ],
  template: `
    <div class="demo-container">
      <h2>Pricing Error Message Integration Demo</h2>
      
      <!-- Method 1: Enhanced Error Display Component -->
      <mat-card class="demo-card">
        <mat-card-header>
          <mat-card-title>Method 1: Enhanced Error Display Component</mat-card-title>
          <mat-card-subtitle>Automatically detects and enhances pricing-related errors</mat-card-subtitle>
        </mat-card-header>
        <mat-card-content>
          <app-enhanced-error-display [errors]="sampleErrors" />
        </mat-card-content>
      </mat-card>

      <!-- Method 2: Auto Pricing Links Directive -->
      <mat-card class="demo-card">
        <mat-card-header>
          <mat-card-title>Method 2: Auto Pricing Links Directive</mat-card-title>
          <mat-card-subtitle>Automatically scans text content for field names</mat-card-subtitle>
        </mat-card-header>
        <mat-card-content>
          <rkt-alert-enterprise variant="warn">
            <div class="rkt-Alert__text">
              To save, fix the following errors:
              <ul appAutoPricingLinks>
                <li>Purchase Price is required and must be greater than zero</li>
                <li>LTV cannot exceed 100% for this loan type</li>
                <li>Seller Concessions amount is invalid</li>
                <li>Anticipated Closing Date must be in the future</li>
                <li>Lender Paid Credit cannot be negative</li>
              </ul>
            </div>
          </rkt-alert-enterprise>
        </mat-card-content>
      </mat-card>

      <!-- Method 3: Manual Field Link Directive -->
      <mat-card class="demo-card">
        <mat-card-header>
          <mat-card-title>Method 3: Manual Field Link Directive</mat-card-title>
          <mat-card-subtitle>Manually specify which text should be clickable</mat-card-subtitle>
        </mat-card-header>
        <mat-card-content>
          <rkt-alert-enterprise variant="warn">
            <div class="rkt-Alert__text">
              Please fix the following validation errors:
              <ul>
                <li appPricingFieldLink="purchasePrice" linkText="Purchase Price">
                  Purchase Price is required for loan processing
                </li>
                <li appPricingFieldLink="credits.manualLenderPaidCredit" linkText="Lender Paid Credit">
                  Lender Paid Credit must be a positive number
                </li>
                <li appPricingFieldLink="qualificationDetails.ltv" linkText="LTV">
                  LTV ratio exceeds maximum allowed for this product
                </li>
                <li appPricingFieldLink="anticipatedClosingDate">
                  Click anywhere in this message to navigate to Anticipated Closing Date
                </li>
              </ul>
            </div>
          </rkt-alert-enterprise>
        </mat-card-content>
      </mat-card>

      <!-- Method 4: Programmatic Integration -->
      <mat-card class="demo-card">
        <mat-card-header>
          <mat-card-title>Method 4: Programmatic Integration</mat-card-title>
          <mat-card-subtitle>Use the service directly in component logic</mat-card-subtitle>
        </mat-card-header>
        <mat-card-content>
          <div class="programmatic-errors">
            @for (error of programmticErrors; track error.field) {
              <div class="error-item">
                @if (canNavigate(error.field)) {
                  <button 
                    class="error-field-button"
                    (click)="navigateToField(error.field)">
                    {{ getFieldLabel(error.field) }}
                  </button>
                  <span class="error-message">{{ error.message }}</span>
                } @else {
                  <span class="error-full">{{ error.field }}: {{ error.message }}</span>
                }
              </div>
            }
          </div>
        </mat-card-content>
      </mat-card>

      <!-- Integration Status -->
      <mat-card class="demo-card">
        <mat-card-header>
          <mat-card-title>Integration Status</mat-card-title>
          <mat-card-subtitle>Check which fields can be navigated to</mat-card-subtitle>
        </mat-card-header>
        <mat-card-content>
          <div class="status-grid">
            @for (field of testFields; track field.path) {
              <div class="status-item" [class.navigable]="canNavigate(field.path)">
                <span class="field-name">{{ field.name }}</span>
                <span class="status-indicator">
                  {{ canNavigate(field.path) ? '✅ Navigable' : '❌ Not Available' }}
                </span>
              </div>
            }
          </div>
        </mat-card-content>
      </mat-card>
    </div>
  `,
  styles: [`
    .demo-container {
      max-width: 1000px;
      margin: 20px auto;
      padding: 20px;
    }
    
    .demo-card {
      margin-bottom: 24px;
    }
    
    .programmatic-errors {
      background: #fff3cd;
      border: 1px solid #ffeaa7;
      border-radius: 4px;
      padding: 16px;
    }
    
    .error-item {
      margin: 8px 0;
      display: flex;
      align-items: center;
      gap: 8px;
    }
    
    .error-field-button {
      background: none;
      border: none;
      color: #007bff;
      text-decoration: underline;
      cursor: pointer;
      font-weight: 500;
      padding: 0;
    }
    
    .error-field-button:hover {
      color: #0056b3;
      background-color: rgba(0, 123, 255, 0.1);
    }
    
    .error-message {
      color: #721c24;
    }
    
    .error-full {
      color: #721c24;
    }
    
    .status-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 12px;
    }
    
    .status-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px 12px;
      border-radius: 4px;
      background: #f8f9fa;
      border: 1px solid #dee2e6;
    }
    
    .status-item.navigable {
      background: #d4edda;
      border-color: #c3e6cb;
    }
    
    .field-name {
      font-weight: 500;
    }
    
    .status-indicator {
      font-size: 12px;
    }
    
    :host ::ng-deep .pricing-field-link {
      color: #007bff !important;
      font-weight: 500;
    }
    
    :host ::ng-deep .pricing-field-link:hover {
      color: #0056b3 !important;
      background-color: rgba(0, 123, 255, 0.1);
    }
  `]
})
export class PricingErrorIntegrationDemoComponent {
  private readonly errorNavService = inject(PricingErrorNavigationService);

  // Sample validation errors for demo
  readonly sampleErrors: ValidationErrorDto[] = [
    {
      path: 'purchasePrice',
      message: 'is required',
      stateName: EntityStateName.Product,
      onDismiss: () => console.log('Dismissed purchase price error')
    },
    {
      path: 'credits.manualLenderPaidCredit',
      message: 'must be a positive number',
      stateName: EntityStateName.Product,
      onDismiss: () => console.log('Dismissed lender credit error')
    },
    {
      path: 'qualificationDetails.ltv',
      message: 'cannot exceed 100%',
      stateName: EntityStateName.Product,
      onDismiss: () => console.log('Dismissed LTV error')
    }
  ];

  // Sample errors for programmatic demo
  readonly programmticErrors = [
    { field: 'purchasePrice', message: 'is required and must be greater than zero' },
    { field: 'credits.sellerConcessions', message: 'amount is invalid' },
    { field: 'anticipatedClosingDate', message: 'must be in the future' },
    { field: 'nonNavigableField', message: 'this field cannot be navigated to' }
  ];

  // Test fields for status check
  readonly testFields = [
    { name: 'Purchase Price', path: 'purchasePrice' },
    { name: 'LTV', path: 'qualificationDetails.ltv' },
    { name: 'Lender Paid Credit', path: 'credits.manualLenderPaidCredit' },
    { name: 'Seller Concessions', path: 'credits.sellerConcessions' },
    { name: 'Anticipated Closing Date', path: 'anticipatedClosingDate' },
    { name: 'Insurance Amount', path: 'insuranceAmount' },
    { name: 'Non-Pricing Field', path: 'someOtherField' }
  ];

  canNavigate(fieldPath: string): boolean {
    return this.errorNavService.canNavigateToField(fieldPath);
  }

  getFieldLabel(fieldPath: string): string {
    return this.errorNavService.getFieldLabel(fieldPath) || fieldPath;
  }

  navigateToField(fieldPath: string): void {
    const success = this.errorNavService.navigateToFieldFromError(fieldPath, {
      focus: true,
      highlight: true,
      scrollBehavior: 'smooth'
    });

    if (!success) {
      console.warn(`Failed to navigate to field: ${fieldPath}`);
    }
  }
}
