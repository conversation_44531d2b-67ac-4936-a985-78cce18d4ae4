import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { RktAlertEnterpriseModule } from '@rocketcentral/rocket-design-system-enterprise-angular';
import { AutoPricingLinksDirective } from '../../directives/auto-pricing-links.directive';
import { EntityStateName } from '../../services/entity-state/abstract-entity-state.service';
import { ValidationErrorDto } from '../../services/error/validation-error-provider.service';
import { EnhancedErrorDisplayComponent } from '../enhanced-error-display/enhanced-error-display.component';

/**
 * Demo component showing client error message enhancement
 */
@Component({
  selector: 'app-client-error-demo',
  standalone: true,
  imports: [
    CommonModule,
    RktAlertEnterpriseModule,
    AutoPricingLinksDirective,
    EnhancedErrorDisplayComponent
  ],
  templateUrl: './client-error-demo.component.html',
  styleUrl: './client-error-demo.component.scss'
})
export class ClientErrorDemoComponent {
  // Sample client error for testing
  readonly clientErrors: ValidationErrorDto[] = [
    {
      path: 'firstName',
      message: 'is required',
      stateName: EntityStateName.Client,
      onDismiss: () => console.log('Dismissed First Name error')
    }
  ];
}
