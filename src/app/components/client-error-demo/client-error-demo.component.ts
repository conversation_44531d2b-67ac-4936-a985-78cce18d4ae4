import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RktAlertEnterpriseModule } from '@rocketcentral/rocket-design-system-enterprise-angular';
import { AutoPricingLinksDirective } from '../../directives/auto-pricing-links.directive';
import { EnhancedErrorDisplayComponent } from '../enhanced-error-display/enhanced-error-display.component';
import { ValidationErrorDto } from '../../services/error/validation-error-provider.service';
import { EntityStateName } from '../../services/entity-state/abstract-entity-state.service';

/**
 * Demo component showing client error message enhancement
 */
@Component({
  selector: 'app-client-error-demo',
  standalone: true,
  imports: [
    CommonModule,
    RktAlertEnterpriseModule,
    AutoPricingLinksDirective,
    EnhancedErrorDisplayComponent
  ],
  template: `
    <div class="demo-container">
      <h3>Client Error Message Navigation Demo</h3>
      
      <div class="demo-section">
        <h4>Method 1: Enhanced Error Display Component</h4>
        <app-enhanced-error-display [errors]="clientErrors" />
      </div>
      
      <div class="demo-section">
        <h4>Method 2: Auto Pricing Links Directive</h4>
        <rkt-alert-enterprise variant="warn">
          <div class="rkt-Alert__text" appAutoPricingLinks>
            To save, fix 1 error:
            <ul>
              <li>Client information: First Name is required</li>
            </ul>
          </div>
        </rkt-alert-enterprise>
      </div>
      
      <div class="demo-section">
        <h4>Expected Behavior</h4>
        <ul class="behavior-list">
          <li>✅ "First Name" text should be clickable (blue and underlined)</li>
          <li>✅ Clicking shows console message if client panel is not open</li>
          <li>✅ If client panel is open, navigates to the First Name field</li>
          <li>✅ Does not break existing client popover functionality</li>
        </ul>
      </div>
      
      <div class="demo-section">
        <h4>Test Instructions</h4>
        <ol class="instructions-list">
          <li>Click on "First Name" in either error message above</li>
          <li>Check browser console for navigation messages</li>
          <li>Open client information panel manually</li>
          <li>Click "First Name" again - should navigate to field</li>
          <li>Verify normal client popover still works</li>
        </ol>
      </div>
    </div>
  `,
  styles: [`
    .demo-container {
      max-width: 800px;
      margin: 20px;
      padding: 20px;
      font-family: Arial, sans-serif;
    }
    
    .demo-section {
      margin-bottom: 32px;
      padding: 16px;
      border: 1px solid #e0e0e0;
      border-radius: 8px;
      background: #fafafa;
    }
    
    .demo-section h4 {
      margin-top: 0;
      color: #333;
    }
    
    .behavior-list, .instructions-list {
      margin: 12px 0;
      padding-left: 20px;
    }
    
    .behavior-list li, .instructions-list li {
      margin: 8px 0;
      line-height: 1.4;
    }
    
    :host ::ng-deep .pricing-field-link {
      color: #007bff !important;
      font-weight: 500;
      text-decoration: underline;
      cursor: pointer;
    }
    
    :host ::ng-deep .pricing-field-link:hover {
      color: #0056b3 !important;
      background-color: rgba(0, 123, 255, 0.1);
    }
  `]
})
export class ClientErrorDemoComponent {
  // Sample client error for testing
  readonly clientErrors: ValidationErrorDto[] = [
    {
      path: 'firstName',
      message: 'is required',
      stateName: EntityStateName.Client,
      onDismiss: () => console.log('Dismissed First Name error')
    }
  ];
}
