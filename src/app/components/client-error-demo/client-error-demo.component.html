<div class="demo-container">
  <h3>Client Error Message Navigation Demo</h3>
  
  <div class="demo-section">
    <h4>Method 1: Enhanced Error Display Component</h4>
    <app-enhanced-error-display [errors]="clientErrors" />
  </div>
  
  <div class="demo-section">
    <h4>Method 2: Auto Pricing Links Directive</h4>
    <rkt-alert-enterprise variant="warn">
      <div class="rkt-Alert__text" appAutoPricingLinks>
        To save, fix 1 error:
        <ul>
          <li>Client information: First Name is required</li>
        </ul>
      </div>
    </rkt-alert-enterprise>
  </div>
  
  <div class="demo-section">
    <h4>Expected Behavior</h4>
    <ul class="behavior-list">
      <li>✅ "First Name" text should be clickable (blue and underlined)</li>
      <li>✅ Clicking shows console message if client panel is not open</li>
      <li>✅ If client panel is open, navigates to the First Name field</li>
      <li>✅ Does not break existing client popover functionality</li>
    </ul>
  </div>
  
  <div class="demo-section">
    <h4>Test Instructions</h4>
    <ol class="instructions-list">
      <li>Click on "First Name" in either error message above</li>
      <li>Check browser console for navigation messages</li>
      <li>Open client information panel manually</li>
      <li>Click "First Name" again - should navigate to field</li>
      <li>Verify normal client popover still works</li>
    </ol>
  </div>
</div>
