<div class="test-container">
  <h3>✅ Final Client Field Navigation Test</h3>
  
  <div class="status-section">
    <h4>Navigation Status</h4>
    <ul>
      <li>firstName: {{ canNavigateToFirstName ? '✅ CAN NAVIGATE' : '❌ CANNOT NAVIGATE' }}</li>
      <li>lastName: {{ canNavigateToLastName ? '✅ CAN NAVIGATE' : '❌ CANNOT NAVIGATE' }}</li>
    </ul>
  </div>
  
  <div class="test-section">
    <h4>Your Original Error Message (Enhanced)</h4>
    <rkt-alert-enterprise variant="warn">
      <div class="rkt-Alert__text" appAutoPricingLinks>
        To save, fix 1 error:
        <ul>
          <li>Client information: First Name is required</li>
        </ul>
      </div>
    </rkt-alert-enterprise>
  </div>
  
  <div class="test-section">
    <h4>Manual Test Buttons</h4>
    <div class="button-group">
      <button class="test-button" (click)="testNavigation('firstName')">
        Test First Name Navigation
      </button>
      <button class="test-button" (click)="testNavigation('lastName')">
        Test Last Name Navigation
      </button>
    </div>
  </div>
  
  <div class="results-section">
    <h4>Expected Results</h4>
    <ul>
      <li>✅ Both navigation status should show "CAN NAVIGATE"</li>
      <li>✅ "First Name" in the error message should be <strong style="color: #007bff; text-decoration: underline;">blue and underlined</strong></li>
      <li>✅ Clicking "First Name" should show console message</li>
      <li>✅ Test buttons should log navigation attempts</li>
    </ul>
  </div>
  
  <div class="instructions-section">
    <h4>🔍 Check Browser Console</h4>
    <p>Open Developer Tools (F12) and check the Console tab for test results and navigation logs.</p>
  </div>
</div>
