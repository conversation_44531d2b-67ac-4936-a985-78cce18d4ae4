import { Component, inject, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RktAlertEnterpriseModule } from '@rocketcentral/rocket-design-system-enterprise-angular';
import { AutoPricingLinksDirective } from '../../directives/auto-pricing-links.directive';
import { PricingErrorNavigationService } from '../../services/pricing-navigation/pricing-error-navigation.service';

/**
 * Final test component to verify client field navigation is working
 */
@Component({
  selector: 'app-final-client-test',
  standalone: true,
  imports: [
    CommonModule,
    RktAlertEnterpriseModule,
    AutoPricingLinksDirective
  ],
  templateUrl: './final-client-test.component.html',
  styleUrl: './final-client-test.component.scss'
})
export class FinalClientTestComponent implements OnInit {
  private readonly errorNavService = inject(PricingErrorNavigationService);

  canNavigateToFirstName = false;
  canNavigateToLastName = false;

  ngOnInit() {
    // Test if client fields are recognized as navigable
    this.canNavigateToFirstName = this.errorNavService.canNavigateToField('firstName');
    this.canNavigateToLastName = this.errorNavService.canNavigateToField('lastName');
    
    console.log('🧪 Final Client Test Results:');
    console.log('Can navigate to firstName:', this.canNavigateToFirstName);
    console.log('Can navigate to lastName:', this.canNavigateToLastName);
  }

  async testNavigation(fieldPath: string) {
    console.log(`Testing navigation to: ${fieldPath}`);
    try {
      const success = await this.errorNavService.navigateToFieldFromError(fieldPath);
      console.log(`Navigation result: ${success ? 'SUCCESS' : 'FAILED'}`);
    } catch (error) {
      console.error('Navigation error:', error);
    }
  }
}
