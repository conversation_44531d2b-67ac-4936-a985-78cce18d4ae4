.test-container {
  max-width: 800px;
  margin: 20px;
  padding: 20px;
  font-family: Arial, sans-serif;
  border: 2px solid #28a745;
  border-radius: 8px;
  background: #f8fff9;
}

.status-section, .test-section, .results-section, .instructions-section {
  margin-bottom: 24px;
  padding: 16px;
  border: 1px solid #ddd;
  border-radius: 8px;
  background: white;

  h4 {
    margin-top: 0;
    color: #333;
  }
}

.status-section {
  background: #e7f3ff;
  border-color: #007bff;
}

.button-group {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.test-button {
  background: #007bff;
  color: white;
  border: none;
  padding: 10px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;

  &:hover {
    background: #0056b3;
  }
}

.results-section {
  background: #fff3cd;
  border-color: #ffc107;

  li {
    margin: 8px 0;
    line-height: 1.4;
  }
}

.instructions-section {
  background: #d1ecf1;
  border-color: #bee5eb;
  text-align: center;

  p {
    margin: 8px 0;
    font-weight: 500;
  }
}

:host ::ng-deep .pricing-field-link {
  color: #007bff !important;
  font-weight: 500;
  text-decoration: underline;
  cursor: pointer;
  background: rgba(255, 255, 0, 0.3) !important; /* Yellow highlight to make it obvious */

  &:hover {
    color: #0056b3 !important;
    background-color: rgba(0, 123, 255, 0.2) !important;
  }
}
