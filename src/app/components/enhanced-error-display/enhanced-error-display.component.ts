import { CommonModule } from '@angular/common';
import { Component, Input, computed, inject } from '@angular/core';
import { RktAlertEnterpriseModule } from '@rocketcentral/rocket-design-system-enterprise-angular';
import { FormPathLabelPipe } from '../../form-path-label.pipe';
import { EntityStateName } from '../../services/entity-state/abstract-entity-state.service';
import { ValidationErrorDto } from '../../services/error/validation-error-provider.service';
import { PricingErrorNavigationService } from '../../services/pricing-navigation/pricing-error-navigation.service';
import { EntityNamePipe } from '../../util/entity-name.pipe';

/**
 * Enhanced error display component that automatically adds navigation links
 * for pricing-related errors
 */
@Component({
  selector: 'app-enhanced-error-display',
  standalone: true,
  imports: [
    CommonModule,
    RktAlertEnterpriseModule,
    FormPathLabelPipe,
    EntityNamePipe
  ],
  template: `
    <rkt-alert-enterprise variant="warn" [isDismissible]="true" (dismissEvent)="dismissAllErrors()">
      <div class="rkt-Alert__text">
        To save, fix {{ errors.length }} {{ errors.length > 1 ? 'errors' : 'error' }}:
        <ul>
          @for (error of enhancedErrors; track error.path) {
            <li>
              @if (error.canNavigate) {
                <span [innerHTML]="error.enhancedMessage"></span>
              } @else {
                {{ error.stateName | entityName }}: {{ error.path | formPathLabel:error.stateName }} {{ error.message }}
              }
            </li>
          }
        </ul>
      </div>
    </rkt-alert-enterprise>
  `,
  styles: [`
    :host ::ng-deep .pricing-field-link {
      color: #007bff !important;
      font-weight: 500;
    }

    :host ::ng-deep .pricing-field-link:hover {
      color: #0056b3 !important;
    }

    :host ::ng-deep .pricing-field-link-full:hover {
      background-color: rgba(0, 123, 255, 0.1);
    }
  `]
})
export class EnhancedErrorDisplayComponent {
  @Input() errors: ValidationErrorDto[] = [];

  private readonly errorNavService = inject(PricingErrorNavigationService);

  /**
   * Enhanced errors with navigation capabilities
   */
  private enhancedErrorsSignal = computed(() => {
    return this.errors.map(error => {
      const canNavigate = this.isPricingError(error) && this.errorNavService.canNavigateToField(error.path);

      let enhancedMessage = '';
      if (canNavigate) {
        const entityName = this.getEntityDisplayName(error.stateName);
        const fieldLabel = error.path; // Will be processed by formPathLabel pipe
        const fullMessage = `${entityName}: ${fieldLabel} ${error.message}`;

        // Create enhanced message with navigation
        enhancedMessage = this.createEnhancedMessage(fullMessage, error.path, fieldLabel);
      }

      return {
        ...error,
        canNavigate,
        enhancedMessage
      };
    });
  });

  /**
   * Getter for enhanced errors to use in template
   */
  get enhancedErrors() {
    return this.enhancedErrorsSignal();
  }

  /**
   * Check if an error is related to pricing fields
   */
  private isPricingError(error: ValidationErrorDto): boolean {
    // Check if the error is from Product entity (pricing-related)
    if (error.stateName === EntityStateName.Product) {
      return true;
    }

    // Check if the field path matches known pricing fields
    const pricingFieldPatterns = [
      'purchasePrice',
      'baseLoanAmount',
      'baseRate',
      'buydown',
      'buydownSources',
      'lenderPaidBuydownOptOutReason',
      'collectedDiscountPoints',
      'downPayment',
      'credits.',
      'qualificationDetails.ltv',
      'insuranceAmount',
      'escrowWaiver',
      'anticipatedClosingDate',
      'commitmentPeriodInDays',
      'closingDetails.'
    ];

    return pricingFieldPatterns.some(pattern => error.path.includes(pattern));
  }

  /**
   * Create enhanced error message with navigation link
   */
  private createEnhancedMessage(fullMessage: string, fieldPath: string, fieldLabel: string): string {
    const fieldDisplayLabel = this.errorNavService.getFieldLabel(fieldPath) || fieldLabel;

    // Try to make the field label clickable
    if (fullMessage.includes(fieldDisplayLabel)) {
      const linkHtml = `<button
        class="pricing-field-link rkt-Link--inline-12"
        style="background: none; border: none; padding: 0; color: inherit; text-decoration: underline; cursor: pointer; font: inherit;"
        onclick="window.navigateToPricingField('${fieldPath}')"
        title="Click to navigate to ${fieldDisplayLabel}"
      >${fieldDisplayLabel}</button>`;

      return fullMessage.replace(fieldDisplayLabel, linkHtml);
    }

    // If we can't find the exact field label, make the entire message clickable
    return `<span
      class="pricing-field-link-full"
      style="cursor: pointer; text-decoration: underline;"
      onclick="window.navigateToPricingField('${fieldPath}')"
      title="Click to navigate to ${fieldDisplayLabel}"
    >${fullMessage}</span>`;
  }

  /**
   * Get display name for entity
   */
  private getEntityDisplayName(stateName: EntityStateName): string {
    const entityNames: Record<EntityStateName, string> = {
      [EntityStateName.Loan]: 'Loan',
      [EntityStateName.Product]: 'Product',
      [EntityStateName.Client]: 'Client',
      [EntityStateName.Income]: 'Income',
      [EntityStateName.Assets]: 'Assets',
      [EntityStateName.Liability]: 'Credit',
      [EntityStateName.SubjectProperty]: 'Subject Property',
      [EntityStateName.OwnedProperty]: 'Owned Property',
      [EntityStateName.Metadata]: 'Metadata',
      [EntityStateName.Task]: 'Task',
      [EntityStateName.Insurance]: 'Insurance',
      [EntityStateName.RateLock]: 'Rate Lock',
      [EntityStateName.ProductSnapshot]: 'Product Snapshot'
    };

    return entityNames[stateName] || stateName;
  }

  /**
   * Dismiss all errors
   */
  dismissAllErrors(): void {
    this.errors.forEach(error => error.onDismiss());
  }
}
