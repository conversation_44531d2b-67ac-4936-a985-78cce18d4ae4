<rkt-alert-enterprise variant="warn" [isDismissible]="true" (dismissEvent)="dismissAllErrors()">
  <div class="rkt-Alert__text">
    To save, fix {{ errors.length }} {{ errors.length > 1 ? 'errors' : 'error' }}:
    <ul>
      @for (error of enhancedErrors; track error.path) {
        <li>
          @if (error.canNavigate) {
            <span [innerHTML]="error.enhancedMessage"></span>
          } @else {
            {{ error.stateName | entityName }}: {{ error.path | formPathLabel:error.stateName }} {{ error.message }}
          }
        </li>
      }
    </ul>
  </div>
</rkt-alert-enterprise>
