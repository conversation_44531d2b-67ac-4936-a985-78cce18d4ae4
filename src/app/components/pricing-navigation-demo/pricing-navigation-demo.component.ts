import { Component, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatListModule } from '@angular/material/list';
import { PricingNavigationService } from '../../services/pricing-navigation/pricing-navigation.service';
import { PricingErrorNavigationService } from '../../services/pricing-navigation/pricing-error-navigation.service';
import { PricingFieldLinkDirective } from '../../directives/pricing-field-link.directive';

/**
 * Demo component to test the pricing navigation system
 * This component can be used for testing and demonstration purposes
 */
@Component({
  selector: 'app-pricing-navigation-demo',
  standalone: true,
  imports: [
    CommonModule,
    MatButtonModule,
    MatCardModule,
    MatListModule,
    PricingFieldLinkDirective
  ],
  template: `
    <mat-card class="demo-card">
      <mat-card-header>
        <mat-card-title>Pricing Navigation Demo</mat-card-title>
        <mat-card-subtitle>Test anchor point navigation to pricing fields</mat-card-subtitle>
      </mat-card-header>
      
      <mat-card-content>
        <h3>Direct Navigation Tests</h3>
        <div class="button-grid">
          @for (anchor of allAnchors; track anchor.id) {
            <button 
              mat-raised-button 
              color="primary"
              (click)="navigateToField(anchor.fieldName)"
              class="nav-button">
              Go to {{ anchor.label }}
            </button>
          }
        </div>

        <h3>Error Message Navigation Tests</h3>
        <div class="error-messages">
          <div class="error-item">
            <span appPricingFieldLink="purchasePrice" linkText="Purchase Price">
              Purchase Price is required
            </span>
          </div>
          
          <div class="error-item">
            <span appPricingFieldLink="credits.manualLenderPaidCredit" linkText="Lender Paid Credit">
              Lender Paid Credit must be a positive number
            </span>
          </div>
          
          <div class="error-item">
            <span appPricingFieldLink="qualificationDetails.ltv" linkText="LTV">
              LTV cannot exceed 100%
            </span>
          </div>
          
          <div class="error-item">
            <span appPricingFieldLink="anticipatedClosingDate" linkText="Anticipated Closing Date">
              Anticipated Closing Date is required
            </span>
          </div>
          
          <div class="error-item">
            <span appPricingFieldLink="buydown">
              Click anywhere in this message to navigate to Buydown Options
            </span>
          </div>
        </div>

        <h3>Programmatic Error Navigation</h3>
        <div class="button-grid">
          <button 
            mat-raised-button 
            color="accent"
            (click)="testErrorNavigation('purchasePrice')">
            Test Purchase Price Error
          </button>
          
          <button 
            mat-raised-button 
            color="accent"
            (click)="testErrorNavigation('credits.sellerConcessions')">
            Test Seller Concessions Error
          </button>
          
          <button 
            mat-raised-button 
            color="accent"
            (click)="testErrorNavigation('escrowWaiver')">
            Test Escrow Waiver Error
          </button>
        </div>

        <h3>Available Field Anchors</h3>
        <mat-list>
          @for (anchor of allAnchors; track anchor.id) {
            <mat-list-item>
              <div matListItemTitle>{{ anchor.label }}</div>
              <div matListItemLine>ID: {{ anchor.id }}</div>
              <div matListItemLine>Section: {{ anchor.section }}</div>
            </mat-list-item>
          }
        </mat-list>
      </mat-card-content>
    </mat-card>
  `,
  styles: [`
    .demo-card {
      max-width: 800px;
      margin: 20px auto;
    }
    
    .button-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 12px;
      margin: 16px 0;
    }
    
    .nav-button {
      white-space: normal;
      height: auto;
      padding: 12px;
    }
    
    .error-messages {
      background: #fff3cd;
      border: 1px solid #ffeaa7;
      border-radius: 4px;
      padding: 16px;
      margin: 16px 0;
    }
    
    .error-item {
      margin: 8px 0;
      padding: 4px 0;
      border-bottom: 1px solid #ffeaa7;
    }
    
    .error-item:last-child {
      border-bottom: none;
    }
    
    :host ::ng-deep .pricing-field-link {
      color: #007bff !important;
      font-weight: 500;
    }
    
    :host ::ng-deep .pricing-field-link:hover {
      color: #0056b3 !important;
    }
    
    h3 {
      margin-top: 24px;
      margin-bottom: 12px;
      color: #333;
    }
  `]
})
export class PricingNavigationDemoComponent {
  private readonly pricingNavService = inject(PricingNavigationService);
  private readonly errorNavService = inject(PricingErrorNavigationService);

  readonly allAnchors = this.pricingNavService.getAllAnchors();

  navigateToField(fieldPath: string): void {
    const success = this.pricingNavService.navigateToField(fieldPath, {
      focus: true,
      highlight: true,
      scrollBehavior: 'smooth'
    });

    if (!success) {
      alert(`Failed to navigate to field: ${fieldPath}`);
    }
  }

  testErrorNavigation(fieldPath: string): void {
    const success = this.errorNavService.navigateToFieldFromError(fieldPath, {
      focus: true,
      highlight: true,
      scrollBehavior: 'smooth'
    });

    if (success) {
      console.log(`Successfully navigated to field: ${fieldPath}`);
    } else {
      alert(`Failed to navigate to field: ${fieldPath}`);
    }
  }
}
