<div class="test-container">
  <h3>Client Field Link Test</h3>
  
  <div class="test-section">
    <h4>Navigation Status Check</h4>
    <ul>
      <li>firstName: {{ canNavigate('firstName') ? '✅ Can Navigate' : '❌ Cannot Navigate' }}</li>
      <li>lastName: {{ canNavigate('lastName') ? '✅ Can Navigate' : '❌ Cannot Navigate' }}</li>
      <li>middleName: {{ canNavigate('middleName') ? '✅ Can Navigate' : '❌ Cannot Navigate' }}</li>
    </ul>
  </div>
  
  <div class="test-section">
    <h4>Auto-Enhanced Error Message</h4>
    <rkt-alert-enterprise variant="warn">
      <div class="rkt-Alert__text" appAutoPricingLinks>
        To save, fix 1 error:
        <ul>
          <li>Client information: First Name is required</li>
        </ul>
      </div>
    </rkt-alert-enterprise>
  </div>
  
  <div class="test-section">
    <h4>Multiple Client Fields</h4>
    <div appAutoPricingLinks>
      <p>Please complete the following client fields:</p>
      <ul>
        <li>First Name is required</li>
        <li>Last Name cannot be empty</li>
        <li>Middle Name is optional</li>
      </ul>
    </div>
  </div>
  
  <div class="test-section">
    <h4>Expected Results</h4>
    <ul class="results-list">
      <li>✅ All navigation status checks should show "Can Navigate"</li>
      <li>✅ "First Name", "Last Name", and "Middle Name" should be blue and underlined</li>
      <li>✅ Clicking them should show console messages</li>
      <li>✅ Hovering should show light blue background</li>
    </ul>
  </div>
</div>
