import { Component, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RktAlertEnterpriseModule } from '@rocketcentral/rocket-design-system-enterprise-angular';
import { AutoPricingLinksDirective } from '../../directives/auto-pricing-links.directive';
import { PricingErrorNavigationService } from '../../services/pricing-navigation/pricing-error-navigation.service';

/**
 * Test component to verify client field links are working
 */
@Component({
  selector: 'app-test-client-links',
  standalone: true,
  imports: [
    CommonModule,
    RktAlertEnterpriseModule,
    AutoPricingLinksDirective
  ],
  template: `
    <div class="test-container">
      <h3>Client Field Link Test</h3>
      
      <div class="test-section">
        <h4>Navigation Status Check</h4>
        <ul>
          <li>firstName: {{ canNavigate('firstName') ? '✅ Can Navigate' : '❌ Cannot Navigate' }}</li>
          <li>lastName: {{ canNavigate('lastName') ? '✅ Can Navigate' : '❌ Cannot Navigate' }}</li>
          <li>middleName: {{ canNavigate('middleName') ? '✅ Can Navigate' : '❌ Cannot Navigate' }}</li>
        </ul>
      </div>
      
      <div class="test-section">
        <h4>Auto-Enhanced Error Message</h4>
        <rkt-alert-enterprise variant="warn">
          <div class="rkt-Alert__text" appAutoPricingLinks>
            To save, fix 1 error:
            <ul>
              <li>Client information: First Name is required</li>
            </ul>
          </div>
        </rkt-alert-enterprise>
      </div>
      
      <div class="test-section">
        <h4>Multiple Client Fields</h4>
        <div appAutoPricingLinks>
          <p>Please complete the following client fields:</p>
          <ul>
            <li>First Name is required</li>
            <li>Last Name cannot be empty</li>
            <li>Middle Name is optional</li>
          </ul>
        </div>
      </div>
      
      <div class="test-section">
        <h4>Expected Results</h4>
        <ul class="results-list">
          <li>✅ All navigation status checks should show "Can Navigate"</li>
          <li>✅ "First Name", "Last Name", and "Middle Name" should be blue and underlined</li>
          <li>✅ Clicking them should show console messages</li>
          <li>✅ Hovering should show light blue background</li>
        </ul>
      </div>
    </div>
  `,
  styles: [`
    .test-container {
      max-width: 700px;
      margin: 20px;
      padding: 20px;
      font-family: Arial, sans-serif;
    }
    
    .test-section {
      margin-bottom: 24px;
      padding: 16px;
      border: 1px solid #ddd;
      border-radius: 8px;
      background: #f9f9f9;
    }
    
    .test-section h4 {
      margin-top: 0;
      color: #333;
    }
    
    .results-list li {
      margin: 8px 0;
      line-height: 1.4;
    }
    
    :host ::ng-deep .pricing-field-link {
      color: #007bff !important;
      font-weight: 500;
      text-decoration: underline;
      cursor: pointer;
    }
    
    :host ::ng-deep .pricing-field-link:hover {
      color: #0056b3 !important;
      background-color: rgba(0, 123, 255, 0.1);
    }
  `]
})
export class TestClientLinksComponent {
  private readonly errorNavService = inject(PricingErrorNavigationService);

  canNavigate(fieldPath: string): boolean {
    return this.errorNavService.canNavigateToField(fieldPath);
  }
}
