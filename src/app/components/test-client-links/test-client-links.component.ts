import { CommonModule } from '@angular/common';
import { Component, inject } from '@angular/core';
import { RktAlertEnterpriseModule } from '@rocketcentral/rocket-design-system-enterprise-angular';
import { AutoPricingLinksDirective } from '../../directives/auto-pricing-links.directive';
import { PricingErrorNavigationService } from '../../services/pricing-navigation/pricing-error-navigation.service';

/**
 * Test component to verify client field links are working
 */
@Component({
  selector: 'app-test-client-links',
  standalone: true,
  imports: [
    CommonModule,
    RktAlertEnterpriseModule,
    AutoPricingLinksDirective
  ],
  templateUrl: './test-client-links.component.html',
  styleUrl: './test-client-links.component.scss'
})
export class TestClientLinksComponent {
  private readonly errorNavService = inject(PricingErrorNavigationService);

  canNavigate(fieldPath: string): boolean {
    return this.errorNavService.canNavigateToField(fieldPath);
  }
}
