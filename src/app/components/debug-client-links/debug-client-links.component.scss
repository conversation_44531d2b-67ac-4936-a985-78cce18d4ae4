.debug-container {
  max-width: 800px;
  margin: 20px;
  padding: 20px;
  font-family: Arial, sans-serif;
  border: 2px solid #007bff;
  border-radius: 8px;
}

.debug-section {
  margin-bottom: 24px;
  padding: 16px;
  border: 1px solid #ddd;
  border-radius: 8px;
  background: #f8f9fa;

  h4 {
    margin-top: 0;
    color: #333;
  }
}

.console-output {
  background: #1e1e1e;
  color: #d4d4d4;
  padding: 12px;
  border-radius: 4px;
  font-size: 12px;
  overflow-x: auto;
}

button {
  background: #007bff;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;

  &:hover {
    background: #0056b3;
  }
}

.test-results {
  margin-top: 12px;
  padding: 8px;
  background: #fff3cd;
  border-radius: 4px;
}

:host ::ng-deep .pricing-field-link {
  color: #007bff !important;
  font-weight: 500;
  text-decoration: underline;
  cursor: pointer;
  background: yellow !important; /* Make it super obvious if it works */

  &:hover {
    color: #0056b3 !important;
    background-color: rgba(0, 123, 255, 0.3) !important;
  }
}
