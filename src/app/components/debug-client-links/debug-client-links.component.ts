import { CommonModule } from '@angular/common';
import { Component, inject, OnInit } from '@angular/core';
import { RktAlertEnterpriseModule } from '@rocketcentral/rocket-design-system-enterprise-angular';
import { AutoPricingLinksDirective } from '../../directives/auto-pricing-links.directive';
import { ModalNavigationService } from '../../services/pricing-navigation/modal-navigation.service';
import { PricingErrorNavigationService } from '../../services/pricing-navigation/pricing-error-navigation.service';

/**
 * Debug component to test client field links
 */
@Component({
  selector: 'app-debug-client-links',
  standalone: true,
  imports: [
    CommonModule,
    RktAlertEnterpriseModule,
    AutoPricingLinksDirective
  ],
  templateUrl: './debug-client-links.component.html',
  styleUrl: './debug-client-links.component.scss'
})
export class DebugClientLinksComponent implements OnInit {
  readonly errorNavService = inject(PricingErrorNavigationService);
  readonly modalNavService = inject(ModalNavigationService);

  ngOnInit() {
    console.log('🔍 DebugClientLinksComponent initialized');
    console.log('ErrorNavService:', this.errorNavService);
    console.log('ModalNavService:', this.modalNavService);
  }

  testFieldNavigation() {
    console.log('🧪 Running field navigation tests...');

    const testFields = ['firstName', 'lastName', 'middleName'];

    testFields.forEach(field => {
      console.log(`\n--- Testing ${field} ---`);

      // Test modal field detection
      const isModal = this.modalNavService.isModalField(field);
      console.log(`isModalField(${field}):`, isModal);

      // Test navigation capability
      const canNavigate = this.errorNavService.canNavigateToField(field);
      console.log(`canNavigateToField(${field}):`, canNavigate);

      // Test field label
      const label = this.errorNavService.getFieldLabel(field);
      console.log(`getFieldLabel(${field}):`, label);
    });

    console.log('\n🧪 Tests complete - check results above');
  }
}
