import { Component, inject, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RktAlertEnterpriseModule } from '@rocketcentral/rocket-design-system-enterprise-angular';
import { AutoPricingLinksDirective } from '../../directives/auto-pricing-links.directive';
import { PricingErrorNavigationService } from '../../services/pricing-navigation/pricing-error-navigation.service';
import { ModalNavigationService } from '../../services/pricing-navigation/modal-navigation.service';

/**
 * Debug component to test client field links
 */
@Component({
  selector: 'app-debug-client-links',
  standalone: true,
  imports: [
    CommonModule,
    RktAlertEnterpriseModule,
    AutoPricingLinksDirective
  ],
  template: `
    <div class="debug-container">
      <h3>🔍 Debug Client Field Links</h3>
      
      <div class="debug-section">
        <h4>Service Status</h4>
        <ul>
          <li>PricingErrorNavigationService: {{ errorNavService ? '✅ Available' : '❌ Not Available' }}</li>
          <li>ModalNavigationService: {{ modalNavService ? '✅ Available' : '❌ Not Available' }}</li>
        </ul>
      </div>
      
      <div class="debug-section">
        <h4>Field Navigation Tests</h4>
        <button (click)="testFieldNavigation()">🧪 Run Navigation Tests</button>
        <div class="test-results">
          <p><strong>Check browser console for detailed logs</strong></p>
        </div>
      </div>
      
      <div class="debug-section">
        <h4>Auto-Enhanced Error Message</h4>
        <p><em>Watch console for directive logs when this renders</em></p>
        <rkt-alert-enterprise variant="warn">
          <div class="rkt-Alert__text" appAutoPricingLinks>
            To save, fix 1 error:
            <ul>
              <li>Client information: First Name is required</li>
            </ul>
          </div>
        </rkt-alert-enterprise>
      </div>
      
      <div class="debug-section">
        <h4>Manual Test</h4>
        <div appAutoPricingLinks>
          <p>Test text: First Name should be clickable</p>
        </div>
      </div>
      
      <div class="debug-section">
        <h4>Expected Console Output</h4>
        <pre class="console-output">
AutoPricingLinksDirective: starting enhancement
Processing text node: [text content]
Checking pattern /First Name/gi for field firstName
PricingErrorNavigationService.canNavigateToField(firstName)
ModalNavigationService.isModalField(firstName): true
firstName is a modal field - returning true
Pattern /First Name/gi matches: ["First Name"]
Creating link for match: First Name
        </pre>
      </div>
    </div>
  `,
  styles: [`
    .debug-container {
      max-width: 800px;
      margin: 20px;
      padding: 20px;
      font-family: Arial, sans-serif;
      border: 2px solid #007bff;
      border-radius: 8px;
    }
    
    .debug-section {
      margin-bottom: 24px;
      padding: 16px;
      border: 1px solid #ddd;
      border-radius: 8px;
      background: #f8f9fa;
    }
    
    .debug-section h4 {
      margin-top: 0;
      color: #333;
    }
    
    .console-output {
      background: #1e1e1e;
      color: #d4d4d4;
      padding: 12px;
      border-radius: 4px;
      font-size: 12px;
      overflow-x: auto;
    }
    
    button {
      background: #007bff;
      color: white;
      border: none;
      padding: 8px 16px;
      border-radius: 4px;
      cursor: pointer;
    }
    
    button:hover {
      background: #0056b3;
    }
    
    .test-results {
      margin-top: 12px;
      padding: 8px;
      background: #fff3cd;
      border-radius: 4px;
    }
    
    :host ::ng-deep .pricing-field-link {
      color: #007bff !important;
      font-weight: 500;
      text-decoration: underline;
      cursor: pointer;
      background: yellow !important; /* Make it super obvious if it works */
    }
    
    :host ::ng-deep .pricing-field-link:hover {
      color: #0056b3 !important;
      background-color: rgba(0, 123, 255, 0.3) !important;
    }
  `]
})
export class DebugClientLinksComponent implements OnInit {
  readonly errorNavService = inject(PricingErrorNavigationService);
  readonly modalNavService = inject(ModalNavigationService);

  ngOnInit() {
    console.log('🔍 DebugClientLinksComponent initialized');
    console.log('ErrorNavService:', this.errorNavService);
    console.log('ModalNavService:', this.modalNavService);
  }

  testFieldNavigation() {
    console.log('🧪 Running field navigation tests...');
    
    const testFields = ['firstName', 'lastName', 'middleName'];
    
    testFields.forEach(field => {
      console.log(`\n--- Testing ${field} ---`);
      
      // Test modal field detection
      const isModal = this.modalNavService.isModalField(field);
      console.log(`isModalField(${field}):`, isModal);
      
      // Test navigation capability
      const canNavigate = this.errorNavService.canNavigateToField(field);
      console.log(`canNavigateToField(${field}):`, canNavigate);
      
      // Test field label
      const label = this.errorNavService.getFieldLabel(field);
      console.log(`getFieldLabel(${field}):`, label);
    });
    
    console.log('\n🧪 Tests complete - check results above');
  }
}
