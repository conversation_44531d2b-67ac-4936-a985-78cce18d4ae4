<div class="debug-container">
  <h3>🔍 Debug Client Field Links</h3>
  
  <div class="debug-section">
    <h4>Service Status</h4>
    <ul>
      <li>PricingErrorNavigationService: {{ errorNavService ? '✅ Available' : '❌ Not Available' }}</li>
      <li>ModalNavigationService: {{ modalNavService ? '✅ Available' : '❌ Not Available' }}</li>
    </ul>
  </div>
  
  <div class="debug-section">
    <h4>Field Navigation Tests</h4>
    <button (click)="testFieldNavigation()">🧪 Run Navigation Tests</button>
    <div class="test-results">
      <p><strong>Check browser console for detailed logs</strong></p>
    </div>
  </div>
  
  <div class="debug-section">
    <h4>Auto-Enhanced Error Message</h4>
    <p><em>Watch console for directive logs when this renders</em></p>
    <rkt-alert-enterprise variant="warn">
      <div class="rkt-Alert__text" appAutoPricingLinks>
        To save, fix 1 error:
        <ul>
          <li>Client information: First Name is required</li>
        </ul>
      </div>
    </rkt-alert-enterprise>
  </div>
  
  <div class="debug-section">
    <h4>Manual Test</h4>
    <div appAutoPricingLinks>
      <p>Test text: First Name should be clickable</p>
    </div>
  </div>
  
  <div class="debug-section">
    <h4>Expected Console Output</h4>
    <pre class="console-output">
AutoPricingLinksDirective: starting enhancement
Processing text node: [text content]
Checking pattern /First Name/gi for field firstName
PricingErrorNavigationService.canNavigateToField(firstName)
ModalNavigationService.isModalField(firstName): true
firstName is a modal field - returning true
Pattern /First Name/gi matches: ["First Name"]
Creating link for match: First Name
    </pre>
  </div>
</div>
