<div class="test-container">
  <h3>Client Error Message Test</h3>
  
  <!-- This should automatically make "First Name" clickable -->
  <rkt-alert-enterprise variant="warn">
    <div class="rkt-Alert__text" appAutoPricingLinks>
      To save, fix 1 error:
      <ul>
        <li>Client information: First Name is required</li>
      </ul>
    </div>
  </rkt-alert-enterprise>
  
  <p class="instructions">
    The "First Name" text above should be clickable. When clicked, it will:
    <br>1. Try to find the field in the current page
    <br>2. If not found, show a helpful message about opening the client panel
  </p>
</div>
