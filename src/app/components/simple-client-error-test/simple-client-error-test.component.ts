import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RktAlertEnterpriseModule } from '@rocketcentral/rocket-design-system-enterprise-angular';
import { AutoPricingLinksDirective } from '../../directives/auto-pricing-links.directive';

/**
 * Simple test component to verify client error message enhancement
 */
@Component({
  selector: 'app-simple-client-error-test',
  standalone: true,
  imports: [
    CommonModule,
    RktAlertEnterpriseModule,
    AutoPricingLinksDirective
  ],
  template: `
    <div class="test-container">
      <h3>Client Error Message Test</h3>
      
      <!-- This should automatically make "First Name" clickable -->
      <rkt-alert-enterprise variant="warn">
        <div class="rkt-Alert__text" appAutoPricingLinks>
          To save, fix 1 error:
          <ul>
            <li>Client information: First Name is required</li>
          </ul>
        </div>
      </rkt-alert-enterprise>
      
      <p class="instructions">
        The "First Name" text above should be clickable. When clicked, it will:
        <br>1. Try to find the field in the current page
        <br>2. If not found, show a helpful message about opening the client panel
      </p>
    </div>
  `,
  styles: [`
    .test-container {
      max-width: 600px;
      margin: 20px;
      padding: 20px;
      border: 1px solid #ddd;
      border-radius: 8px;
    }
    
    .instructions {
      margin-top: 16px;
      font-size: 14px;
      color: #666;
    }
    
    :host ::ng-deep .pricing-field-link {
      color: #007bff !important;
      font-weight: 500;
    }
    
    :host ::ng-deep .pricing-field-link:hover {
      color: #0056b3 !important;
      background-color: rgba(0, 123, 255, 0.1);
    }
  `]
})
export class SimpleClientErrorTestComponent {
}
