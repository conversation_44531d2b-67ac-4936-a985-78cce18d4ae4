import { OverlayModule } from '@angular/cdk/overlay';
import { CommonModule } from '@angular/common';
import {
  AfterViewInit,
  Component,
  ComponentRef,
  computed,
  inject,
  viewChild,
  viewChildren,
} from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import { MatBadgeModule } from '@angular/material/badge';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatDividerModule } from '@angular/material/divider';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { ActivatedRoute, RouterModule } from '@angular/router';
import { AuthService } from '@auth0/auth0-angular';
import { AnalyticsBrowser } from '@segment/analytics-next';
import { HeaderBannerComponent } from '../[containers]/header-banner/header-banner.component';
import { LeftPanelEditStateListenerService } from '../[containers]/left-panel/edit-state-listener.service';
import { LeftPanelComponent } from '../[containers]/left-panel/left-panel.component';
import { RightPanelTabIconDirective } from '../[containers]/right-panel/right-panel-tab/right-panel-tab-icon.directive';
import { RightPanelTabComponent } from '../[containers]/right-panel/right-panel-tab/right-panel-tab.component';
import { RightPanelComponent } from '../[containers]/right-panel/right-panel.component';
import { RlxpLayoutComponent } from '../[layout]/rlxp-layout/rlxp-layout.component';
import { AvoService } from '../analytics/avo/avo.service';
import { AssistantComponent } from '../assistant/assistant.component';
import { provideRla } from '../assistant/util/provide-rla';
import { BannerMessagesComponent } from '../banner-messages/banner-messages.component';
import { CreditReportManagerComponent } from '../credit-report-manager/credit-report-manager.component';
import { DecsDemsValidationService } from '../declarations-demographics/services/decs-dems-validation.service';
import { LeftNavAssetsContainerComponent } from '../left-nav-content/assets/left-nav-assets-container.component';
import { LeftNavClientContainerComponent } from '../left-nav-content/client/left-nav-client-container.component';
import { LeftNavCreditContainerComponent } from '../left-nav-content/credit/left-nav-credit-container.component';
import { LeftNavIncomeContainerComponent } from '../left-nav-content/income/left-nav-income-container.component';
import { LeftNavSubjectPropertyContainerComponent } from '../left-nav-content/subject-property/left-nav-subject-property-container.component';
import { LoanDetailsComponent } from '../loan-details/loan-details.component';
import { LoanSearchComponent } from '../loan-search/loan-search.component';
import { MessageCenterComponent } from '../message-center/message-center.component';
import { PrefillComponent } from '../prefill/prefill.component';
import { OpenPopupHandler } from '../pricing-table/util/open-popup-handler.component';
import { ProductSnapshotsComponent } from '../product-snapshots/product-snapshots.component';
import { RocketFindingsComponent } from '../rocket-findings/rocket-findings.component';
import { ActiveActionService } from '../services/active-action.service';
import {
  ActiveSidenavScreenService,
  SidenavScreen,
} from '../services/active-sidenav-screen/active-sidenav-screen.service';
import { AddressDataService } from '../services/address/address-data.service';
import { AssetDataPullService } from '../services/asset/asset-data-pull.service';
import { AssetNotificationListenerService } from '../services/asset/asset-notification-listener.service';
import { AssumptionSignedPurchaseHandlerService } from '../services/assumption/assumption-signed-purchase';
import { BankerAssignmentService } from '../services/banker-assignment/banker-assignment.service';
import { CommitmentPeriodService } from '../services/commitment-period/commitment-period.service';
import { CompletedLoanFormBuilderService } from '../services/completed-loan-form-builder/completed-loan-form.service';
import { CreditAuthorizationService } from '../services/credit/credit-authorization.service';
import { CreditService } from '../services/credit/credit.service';
import { SelectedCreditClientService } from '../services/credit/selected-credit-client.service';
import { DarkModeService } from '../services/dark-mode/dark-mode.service';
import { EntityStateName } from '../services/entity-state/abstract-entity-state.service';
import { provideAssetState } from '../services/entity-state/asset-state/provide-asset-state';
import { ClientFormService } from '../services/entity-state/client-state/client-form.service';
import { provideClientState } from '../services/entity-state/client-state/provide-client-state';
import { provideIncomeState } from '../services/entity-state/income-state/provide-income-state';
import { InsuranceStateService } from '../services/entity-state/insurance-state/insurance-state.service';
import { provideLiabilityState } from '../services/entity-state/liability-state/provide-liability-state';
import { provideLoanState } from '../services/entity-state/loan-state/provide-loan-state';
import { provideOwnedPropertyState } from '../services/entity-state/owned-property-state/provide-owned-property-state';
import { LoanDeactivationHandlerService } from '../services/entity-state/product-state/loan-deactivation-handler.service';
import { provideProductState } from '../services/entity-state/product-state/provide-product-state';
import { provideRateLockState } from '../services/entity-state/rate-lock-state/provide-rate-lock-state';
import { provideSubjectPropertyState } from '../services/entity-state/subject-property-state/provide-subject-property-state';
import { ValidationErrorProviderService } from '../services/error/validation-error-provider.service';
import { ExclusionService } from '../services/exclusion/exclusion.service';
import { FormNavSectionService } from '../services/form-nav/form-nav-section.service';
import { provideFormNav } from '../services/form-nav/provide-form-nav';
import { HelocEscrowWaiverHandlerService } from '../services/heloc/heloc-escrow-waiver.service';
import { KeyPressService } from '../services/key-press/key-press.service';
import { LeadService } from '../services/lead/lead.service';
import { LoanApplicationStateService } from '../services/loan-application-state/loan-application-state.service';
import { LoanDetailsChangedService } from '../services/loan-details-changed/loan-details-changed.service';
import { LoanIdService } from '../services/loan-id/loan-id.service';
import { LoanLastSavedService } from '../services/loan-last-saved/loan-last-saved.service';
import { LoanStatusLoadingService } from '../services/loan-status-loading/loan-status-loading.service';
import { LoanSummaryService } from '../services/loan-summary/loan-summary.service';
import { MessageCenterService } from '../services/message-center/message-center.service';
import { NewConstructionListenerService } from '../services/new-construction/new-construction-listener.service';
import { LoanNotificationService } from '../services/notification/loan-notification.service';
import { OverlayService } from '../services/overlay/overlay.service';
import { PrefillStateService } from '../services/prefill-state/prefill-state.service';
import { ProductLoadStatusService } from '../services/product-load-status.service.ts/product-load-status.service';
import { ProductSaveFailureListenerService } from '../services/product-save-listener/product-save-failure-listener.service';
import { MortgageCalculatorService } from '../services/products/mortgage-calculator.service';
import { PropertyEstimatesService } from '../services/property-estimates/property-estimates.service';
import { QfrService } from '../services/qfr/qfr.service';
import { RateLockService } from '../services/rate-lock/rate-lock.service';
import { RecentLoansService } from '../services/recent-loans/recent-loans.service';
import { RedisclosureService } from '../services/redisclosure/redisclosure.service';
import { RocketFindingsService } from '../services/rocket-findings/rocket-findings.service';
import { RouteDataService } from '../services/route-data/route-data.service';
import { ManualSaveService } from '../services/save-trigger/manual-save.service';
import { SchwabService } from '../services/schwab/schwab.service';
import { TabRestrictionService } from '../services/tab-restriction/tab-restriction.service';
import { PollCreditTasksListenerService } from '../services/task/poll-credit-tasks-listener.service';
import { TaskNotificationListenerService } from '../services/task/task-notification-listener.service';
import { TaskStateService } from '../services/task/task-state.service';
import { UserAuthorizationService } from '../services/user-authorization/user-authorization.service';
import { SettingsComponent } from '../settings/settings.component';
import { SuggestionsService } from '../suggestions/suggestions.service';
import { SupportComponent } from '../support/support.component';
import { RouteType } from '../util/route-type';
import { PanelContentItem } from './panel-content-item.type';

@Component({
  selector: 'app-rlxp-root',
  standalone: true,
  imports: [
    MatIconModule,
    MessageCenterComponent,
    RlxpLayoutComponent,
    RouterModule,
    LeftPanelComponent,
    RightPanelComponent,
    MatCardModule,
    MatIconModule,
    MatDividerModule,
    OverlayModule,
    CommonModule,
    MatButtonModule,
    RightPanelTabComponent,
    SupportComponent,
    LoanDetailsComponent,
    MatIconModule,
    RightPanelTabIconDirective,
    AssistantComponent,
    CreditReportManagerComponent,
    PrefillComponent,
    RocketFindingsComponent,
    HeaderBannerComponent,
    BannerMessagesComponent,
    MatTooltipModule,
    ProductSnapshotsComponent,
    MatBadgeModule,
  ],
  providers: [
    LoanIdService,
    CreditService,
    CreditAuthorizationService,
    ManualSaveService,
    provideLoanState(),
    provideClientState(),
    provideIncomeState(),
    provideSubjectPropertyState(),
    provideAssetState(),
    provideLiabilityState(),
    provideOwnedPropertyState(),
    provideRateLockState(),
    provideFormNav(),
    TaskStateService,
    ActiveSidenavScreenService,
    LoanNotificationService,
    provideRla(),
    TaskNotificationListenerService,
    AssetNotificationListenerService,
    UserAuthorizationService,
    LoanStatusLoadingService,
    LeadService,
    ExclusionService,
    ClientFormService,
    AddressDataService,
    LoanApplicationStateService,
    InsuranceStateService,
    TabRestrictionService,
    CompletedLoanFormBuilderService,
    PrefillStateService,
    PollCreditTasksListenerService,
    NewConstructionListenerService,
    HelocEscrowWaiverHandlerService,
    LoanDeactivationHandlerService,
    AssumptionSignedPurchaseHandlerService,
    SelectedCreditClientService,
    ActiveActionService,
    AvoService,
    BankerAssignmentService,
    PropertyEstimatesService,
    QfrService,
    AssetDataPullService,
    SchwabService,
    SuggestionsService,
    FormNavSectionService,
    KeyPressService,
    ValidationErrorProviderService,
    OverlayService,
    RocketFindingsComponent,
    OpenPopupHandler,
    MortgageCalculatorService,
    RocketFindingsService,
    provideProductState(),
    TabRestrictionService,
    MessageCenterService,
    LoanSummaryService,
    LoanDetailsChangedService,
    RouteDataService,
    LoanLastSavedService,
    CommitmentPeriodService,
    RateLockService,
    ProductLoadStatusService,
    LeftPanelEditStateListenerService,
    RedisclosureService,
    ProductSaveFailureListenerService,
  ],
  templateUrl: './rlxp-root.component.html',
  styleUrl: './rlxp-root.component.scss',
})
export class RlxpRootComponent implements AfterViewInit {
  LoanSearchComponent = LoanSearchComponent;
  SettingsComponent = SettingsComponent;

  recentLoanService = inject(RecentLoansService);
  darkModeService = inject(DarkModeService);
  keyPressService = inject(KeyPressService);
  mortgageCalculator = inject(MortgageCalculatorService);
  tabRestrictionService = inject(TabRestrictionService);
  analyticsBrowser = inject(AnalyticsBrowser);
  route = inject(ActivatedRoute);
  servicedDataStateService = inject(PrefillStateService);
  routeData = inject(RouteDataService).aggregateRouteData;
  hasPrefillData = toSignal(this.servicedDataStateService.hasPrefillData$);
  authService = inject(AuthService);
  loanLastSavedService = inject(LoanLastSavedService);
  taskListenerService = inject(TaskNotificationListenerService);
  assetListenerService = inject(AssetNotificationListenerService);
  decsDemsValidationService = inject(DecsDemsValidationService);
  readonly messageCenterService = inject(MessageCenterService);
  readonly validationErrorService = inject(ValidationErrorProviderService);
  readonly leftPanelEditStateListenerService = inject(LeftPanelEditStateListenerService);
  readonly redisclosureService = inject(RedisclosureService);
  readonly totalNumberOfMessages = this.messageCenterService.totalNumberOfMessages;

  readonly routeType = computed(() => this.routeData().routeType);
  readonly RouteType = RouteType;

  readonly SidenavScreen = SidenavScreen;

  isEditScreen = computed(() => this.leftPanelEditStateListenerService.isEditState());

  leftPanelRef = viewChild(LeftPanelComponent, { read: ComponentRef });
  leftPanelSectionButtonRefs = viewChildren('sectionButton', { read: Component });

  isProductRoute = computed(() => this.routeData().isProductRoute);
  leftPanelContent = computed<Array<PanelContentItem>>(() => {
    if (this.isProductRoute()) {
      return [
        {
          component: LeftNavClientContainerComponent,
          icon: 'group-outlined',
          validationErrors: computed(() =>
            this.validationErrorService
              .errors()
              .filter((error) => error.stateName === EntityStateName.Client),
          ),
        },
        {
          component: LeftNavSubjectPropertyContainerComponent,
          icon: 'house-outlined',
          validationErrors: computed(() =>
            this.validationErrorService
              .errors()
              .filter(
                (error) =>
                  error.stateName === EntityStateName.SubjectProperty ||
                  error.stateName === EntityStateName.OwnedProperty ||
                  error.stateName === EntityStateName.Liability,
              ),
          ),
        },
        {
          component: LeftNavCreditContainerComponent,
          icon: 'rl-crm',
          validationErrors: computed(() =>
            this.validationErrorService
              .errors()
              .filter((error) => error.stateName === EntityStateName.Liability),
          ),
        },
        {
          component: LeftNavIncomeContainerComponent,
          icon: 'real_estate_agent-outlined',
          validationErrors: computed(() =>
            this.validationErrorService
              .errors()
              .filter((error) => error.stateName === EntityStateName.Income),
          ),
        },
        {
          component: LeftNavAssetsContainerComponent,
          icon: 'savings-outlined',
          validationErrors: computed(() =>
            this.validationErrorService
              .errors()
              .filter((error) => error.stateName === EntityStateName.Assets),
          ),
        },
      ];
    }

    return [];
  });

  getButtonClass() {
    return this.isEditScreen() ? 'hide-hover' : '';
  }

  readonly loanId = this.route.snapshot.paramMap.get('loanId');

  constructor() {
    this.recentLoanService.handleStoreRecentLoan(this.loanId!);
  }

  ngAfterViewInit() {
    this.tabRestrictionService.onNewTab(this.route.snapshot.routeConfig?.path);
    this.analyticsBrowser?.page('Loan Page', 'Loan Form', { loanNumber: this.loanId });
  }
}
