import { Routes } from '@angular/router';
import { LongFormComponent } from '../long-form/long-form.component';
import { appStateRedirectGuardFactory } from '../route-guards/app-state-redirect.guard';
import { authorizationGuard } from '../route-guards/authorization.guard';
import {
  pilotRedirectGuard,
  pricingPilotRedirectGuard,
} from '../route-guards/pilot-redirect.guard';
import { RouteType } from '../util/route-type';
import { RlxpRootComponent } from './rlxp-root.component';

export const rlxpRoutes: Routes = [
  {
    path: ':loanId',
    component: RlxpRootComponent,
    canActivate: [pilotRedirectGuard, authorizationGuard],
    children: [
      {
        path: 'add-product',
        canActivate: [
          pricingPilotRedirectGuard,
          appStateRedirectGuardFactory(RouteType.AddProduct),
        ],
        loadComponent: () =>
          import('../product-form/product-form.component').then((m) => m.ProductFormComponent),
        data: { routeType: RouteType.AddProduct, isProductRoute: true },
      },
      {
        path: 'edit-product',
        canActivate: [
          pricingPilotRedirectGuard,
          appStateRedirectGuardFactory(RouteType.EditProduct),
        ],
        loadComponent: () =>
          import('../product-form/product-form.component').then((m) => m.ProductFormComponent),
        data: { routeType: RouteType.EditProduct, isProductRoute: true },
      },
      {
        path: 'pricing-table',
        canActivate: [
          pricingPilotRedirectGuard,
          appStateRedirectGuardFactory(RouteType.PricingTable),
        ],
        loadComponent: () =>
          import('../pricing-table/pricing-table.component').then((m) => m.PricingTableComponent),
        data: { routeType: RouteType.PricingTable, isProductRoute: true },
      },
      {
        path: 'present-loan',
        canActivate: [
          pricingPilotRedirectGuard,
          appStateRedirectGuardFactory(RouteType.PresentLoan),
        ],
        loadComponent: () =>
          import('../present-loan/present-loan.component').then((m) => m.PresentLoanComponent),
        data: { isProductRoute: true, routeType: RouteType.PresentLoan },
      },
      {
        path: '**',
        component: LongFormComponent,
        data: { isProductRoute: false, routeType: RouteType.LoanApplication },
        canActivate: [appStateRedirectGuardFactory(RouteType.LoanApplication, true)],
      },
    ],
  },
];
