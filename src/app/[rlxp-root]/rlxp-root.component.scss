:host {
  flex: 1 1 0;
  height: 0px;

  ::ng-deep {
    .mat-badge-medium.mat-badge-overlap .mat-badge-content {
      color: transparent;
      height: 12px;
      width: 12px;
      background: var(--rlxp-rose-light-700);
    }
  }
}

#logo {
  display: flex;
  width: fit-content;
  align-self: center;
  justify-self: center;
}

.button-container {
  flex-grow: 0;
  padding: 8px;
  display: flex;
  flex-direction: column;
  gap: 8px;

  button.mat-mdc-icon-button {
    border-radius: 12px;
    padding: 12px;
    align-self: center;
    --mdc-icon-button-state-layer-size: 48px;

    ::ng-deep {
      .mat-mdc-button-persistent-ripple {
        border-radius: 12px;
      }

      mat-icon:not(.mat-icon-no-color) {
        color: var(--mat-icon-color);
      }
    }
  }

  button.cdk-focused {
    ::ng-deep .mat-mdc-button-persistent-ripple::before {
      opacity: var(--mat-icon-button-pressed-state-layer-opacity);
    }
  }
}

mat-divider {
  margin: 0 -8px;
}
