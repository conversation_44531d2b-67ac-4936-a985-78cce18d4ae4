<app-rlxp-layout>
  <ng-container header>
    <mat-icon id="logo" [svgIcon]="darkModeService.isDarkMode() ? 'rl-icon-dark' : 'rl-icon'" />
    <app-header-banner />
  </ng-container>

  <ng-container banner>
    <app-banner-messages />
  </ng-container>

  <ng-container logo>
    <mat-icon id="logo" [svgIcon]="darkModeService.isDarkMode() ? 'rl-icon-dark' : 'rl-icon'" />
  </ng-container>

  <ng-container left-menu>
    <app-left-panel #leftPanel>
      <div class="flex flex-col h-full" #editPlacement>
        <div class="button-container">
          <button
            mat-icon-button
            matTooltip="Loan Search"
            class="rkt-ButtonIcon flex justify-self-center"
            (click)="leftPanel.togglePopoutWithFallbackPosition(LoanSearchComponent, $event.target)"
          >
            <mat-icon class="rkt-Icon" svgIcon="rl-loan-search" />
          </button>
          <mat-divider />
        </div>
        <div class="grow-1 flex h-full button-container">
          @for (item of leftPanelContent(); track item.component) {
            <button
              #sectionButton
              [id]="item"
              mat-icon-button
              [class.active]="leftPanel.popoutContent().component === item.component"
              class="rkt-ButtonIcon flex justify-self-center"
              [matBadge]="item.validationErrors?.()?.length ? '-' : ''"
              (mouseenter)="leftPanel.togglePopout(item.component, $event.target, editPlacement)"
              (mouseleave)="
                leftPanel.togglePopoutFromButton(item.component, $event.target, editPlacement)
              "
              (click)="leftPanel.toggleEditPopout(item.component, $event.target, editPlacement)"
            >
              <mat-icon class="rkt-Icon" [svgIcon]="item.icon" />
            </button>
          }
        </div>
        <div class="button-container">
          <mat-divider />
          <button
            mat-icon-button
            matTooltip="Settings"
            class="rkt-ButtonIcon flex justify-self-center"
            (click)="leftPanel.togglePopoutWithFallbackPosition(SettingsComponent, $event.target)"
          >
            <mat-icon class="rkt-Icon" svgIcon="settings-outlined" />
          </button>
        </div>
      </div>
    </app-left-panel>
  </ng-container>

  <ng-container main-content>
    <router-outlet />
  </ng-container>

  <ng-container right-menu>
    <app-right-panel>
      <app-right-panel-tab
        class="header"
        title="Rocket Logic Assistant"
        [icon]="darkModeService.isDarkMode() ? 'rl-assistant-sparkle-dark' : 'rl-assistant-sparkle'"
        [screen]="SidenavScreen.RocketLogicAssistant"
      >
        <app-assistant />
      </app-right-panel-tab>

      @if (hasPrefillData()) {
        <app-right-panel-tab title="Serviced Loan Data" [screen]="SidenavScreen.PreFill">
          <ng-template appRightPanelTabIcon>
            <mat-icon class="rkt-Icon material-icons-outlined" fontIcon="note_add" />
          </ng-template>
          @defer (when hasPrefillData()) {
            <app-prefill />
          } @placeholder {
            <div></div>
          }
        </app-right-panel-tab>
      }

      <app-right-panel-tab
        title="Loan Details"
        icon="description-outlined"
        [screen]="SidenavScreen.LoanDetails"
      >
        <app-loan-details />
      </app-right-panel-tab>

      <app-right-panel-tab
        title="Credit Request Manager"
        icon="rl-crm"
        [screen]="SidenavScreen.CreditReportManager"
      >
        <app-credit-report-manager />
      </app-right-panel-tab>

      @if (routeType() === RouteType.PricingTable) {
        <app-right-panel-tab
          title="Rocket Findings"
          icon="rocket_launch-outlined"
          [screen]="SidenavScreen.RocketFindings"
        >
          <app-rocket-findings />
        </app-right-panel-tab>
      }

      <app-right-panel-tab
        title="Loan Option History"
        icon="history-outlined"
        [screen]="SidenavScreen.ProductSnapshots"
      >
        <app-product-snapshots />
      </app-right-panel-tab>

      <app-right-panel-tab
        [screen]="SidenavScreen.MessageCenter"
        title="Message Center"
        icon="notification_important-outlined"
        [badge]="totalNumberOfMessages()"
      >
        <app-message-center />
      </app-right-panel-tab>

      <app-right-panel-tab title="Support" [screen]="SidenavScreen.Support">
        <ng-template appRightPanelTabIcon>
          <mat-icon svgIcon="help-outlined" color="primary" />
        </ng-template>
        @defer (on viewport) {
          <app-support />
        } @placeholder {
          <div></div>
        }
      </app-right-panel-tab>
    </app-right-panel>
  </ng-container>
</app-rlxp-layout>
