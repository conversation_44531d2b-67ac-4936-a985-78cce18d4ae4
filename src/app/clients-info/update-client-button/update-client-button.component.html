@if (showMenuButton()) {
  <button
    mat-icon-button
    class="rkt-ButtonIcon"
    [disabled]="isLoanEditingDisabled()"
    [class.rkt-ButtonIcon--is-disabled]="isLoanEditingDisabled()"
    [matMenuTriggerFor]="menu"
  >
    <mat-icon class="rkt-Icon" svgIcon="more_vert-outlined" />
  </button>

  <mat-menu #menu="matMenu">
    @if (canDeleteClient()) {
      <button
        class="rkt-Menu__item"
        mat-menu-item
        (click)="onDeleteClick()"
        [disabled]="saveInProgress()"
      >
        <mat-icon class="rkt-Icon" svgIcon="delete-outlined" />
        <span class="rkt-Menu__item-text">
          @if (saveInProgress()) {
            Save In Progress
          } @else if (clientHasCredit()) {
            Deactivate
          } @else {
            Delete
          }
        </span>
      </button>
    }
    @if (!isPrimaryBorrower() && isClientSaved()) {
      <button class="rkt-Menu__item" mat-menu-item (click)="onMakePrimary()">
        <mat-icon class="rkt-Icon" svgIcon="groups-outlined" />
        <span class="rkt-Menu__item-text"> Make Primary </span>
      </button>
    }
  </mat-menu>
}
