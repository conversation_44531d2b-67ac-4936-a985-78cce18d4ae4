import { Component, computed, inject, input, output } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { Client, Income } from '@rocket-logic/rl-xp-bff-models';
import { CreditService } from '../../services/credit/credit.service';
import { ClientStateService } from '../../services/entity-state/client-state/client-state.service';
import { ClientControls } from '../../services/entity-state/client-state/form-types';
import { IncomeStateService } from '../../services/entity-state/income-state/income-state.service';
import { toValueChangesSignal } from '../../util/value-changes-signal';

@Component({
  selector: 'app-update-client-button',
  standalone: true,
  imports: [MatButtonModule, MatIconModule, MatMenuModule],
  templateUrl: './update-client-button.component.html',
  styleUrl: './update-client-button.component.scss',
})
export class UpdateClientButtonComponent {
  private readonly incomeStateService = inject(IncomeStateService);
  private readonly clientStateService = inject(ClientStateService);
  private readonly creditService = inject(CreditService);

  clientForm = input.required<FormGroup<ClientControls>>();
  clients = input.required<Client[]>();
  isLoanEditingDisabled = input<boolean | undefined>(false);

  delete = output<void>();

  isPrimaryBorrower = toValueChangesSignal<boolean>(this.clientForm, 'isPrimaryBorrower');
  saveInProgress = this.clientStateService.isUpdating;
  isPullingCredit = computed(() => this.creditService.isPullingCredit());

  clientHasCredit = computed(() =>
    this.creditService.clientHasCredit(this.clientForm().value?.id ?? ''),
  );

  isClientSaved = computed(() =>
    this.clients().some((client) => client.id === this.clientForm().value.id),
  );

  /**
   * List of clients excluding the current client
   */
  otherClients = computed(() =>
    this.clients().filter((client) => client.id !== this.clientForm().value.id),
  );

  canDeleteClient = computed(() => {
    // Need to reference otherClients() at the top of this signal to re-run on change
    const otherClients = this.otherClients();
    const client = this.clientForm().value;

    if (this.isPullingCredit()) {
      return false;
    }

    // Client cannot have partial info
    if (client.personalInformation?.ssn && !client.personalInformation.dateOfBirth) {
      return false;
    }

    // If the client to delete doesn't have a gcid, they can be deleted
    if (!client.gcid) {
      return true;
    }

    // If any other clients dont have gcid, MC will throw
    // when creating income dictionary by GCID
    const anyOtherClientsMissingGcid = otherClients.some((client) => !client.gcid);
    if (anyOtherClientsMissingGcid) {
      return false;
    }

    const incomeData = this.incomeStateService.state()?.data ?? {};
    const currentClientIncome = incomeData[client?.id ?? ''] ?? new Map<string, Income>();

    // If current client doesnt have income, allow deletion
    if (currentClientIncome.size === 0) {
      return true;
    } else {
      // Check if at least one other client has income and gcid
      return otherClients.some((otherClient) => {
        const otherClientIncome = incomeData[otherClient?.id ?? ''] ?? new Map<string, Income>();
        return otherClientIncome.size > 0;
      });
    }
  });

  showMenuButton = computed(
    () =>
      !this.isClientSaved() ||
      (this.clients().length > 1 && (this.canDeleteClient() || !this.isPrimaryBorrower())),
  );

  onDeleteClick() {
    this.delete.emit();
  }

  onMakePrimary() {
    this.clientStateService.makePrimary$(this.clientForm().value.id!).subscribe();
  }
}
