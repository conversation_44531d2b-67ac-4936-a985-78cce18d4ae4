import { ComponentFixture, TestBed } from '@angular/core/testing';

import { signal } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { MockBuilder } from 'ng-mocks';
import { NEVER } from 'rxjs';
import { CreditService } from '../../services/credit/credit.service';
import { ClientActionsHandlerService } from '../../services/entity-state/client-state/client-actions-handler.component';
import { ClientFormService } from '../../services/entity-state/client-state/client-form.service';
import { ClientStateService } from '../../services/entity-state/client-state/client-state.service';
import { IncomeStateService } from '../../services/entity-state/income-state/income-state.service';
import { ClientInfoComponent } from './client-info.component';

describe('ClientInfoComponent', () => {
  let component: ClientInfoComponent;
  let fixture: ComponentFixture<ClientInfoComponent>;

  beforeEach(() =>
    MockBuilder(ClientInfoComponent)
      .mock(ClientStateService, { makePrimary$: () => NEVER, isUpdating: signal(false) })
      .mock(IncomeStateService, { state: signal({}) })
      .mock(CreditService, { creditReports: signal({}), isPullingCredit: signal(false) })
      .mock(ClientFormService, {})
      .mock(ClientActionsHandlerService),
  );
  beforeEach(() => {
    fixture = TestBed.createComponent(ClientInfoComponent);
    component = fixture.componentInstance;
    fixture.componentRef.setInput('clientForm', new FormGroup({}));
    fixture.componentRef.setInput('clients', []);
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
