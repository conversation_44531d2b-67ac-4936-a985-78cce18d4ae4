<div class="title-container">
  <p class="rkt-Label-16 rkt-FontWeight--700">
    {{ isPrimaryBorrower() ? 'Primary Client' : 'Co-Client' }}
  </p>
  <app-update-client-button
    [isLoanEditingDisabled]="isLoanEditingDisabled()"
    [clientForm]="clientForm()"
    [clients]="clients()"
    (delete)="clientActionsHandlerService.onDelete(selectedClientFormId()!)"
  />
</div>
<div class="container">
  <app-legal-name
    [isPrimaryBorrower]="isPrimaryBorrower()"
    [personalInfoForm]="personalInfoForm()"
  />
  <app-contact-info
    [contactInfoForm]="contactInfoForm()"
    [isLoanEditingDisabled]="isLoanEditingDisabled()"
    (addPhone)="onAddPhone()"
  />
  <app-marital-status
    [isPrimaryBorrower]="isPrimaryBorrower()"
    [personalInfoForm]="personalInfoForm()"
    [clients]="otherClients()"
    (addSpouse)="addSpouseForThisClient()"
  />
  <app-current-address
    [currentResidenceForm]="currentResidenceForm()"
    [clientsWithForms]="otherClientsWithForms()"
    [showSameAs]="!isPrimaryBorrower()"
  />
  <app-previous-address
    [previousResidenceFormArray]="previousResidenceFormArray()"
    [isLoanEditingDisabled]="isLoanEditingDisabled()"
    (addPreviousAddress)="onAddPreviousAddress()"
  />
  <app-va-eligibility [militaryForm]="militaryForm()" />
</div>
