import { Component, EventEmitter, Output, computed, inject, input, output } from '@angular/core';
import { FormArray, FormGroup } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { Client } from '@rocket-logic/rl-xp-bff-models';
import { ClientActionsHandlerService } from '../../services/entity-state/client-state/client-actions-handler.component';
import { ClientFormService } from '../../services/entity-state/client-state/client-form.service';
import { ClientControls } from '../../services/entity-state/client-state/form-types';
import { toValueChangesSignal } from '../../util/value-changes-signal';
import { ContactInfoComponent } from '../contact-info/contact-info.component';
import { CurrentAddressComponent } from '../current-address/current-address.component';
import { LegalNameComponent } from '../legal-name/legal-name.component';
import { MaritalStatusComponent } from '../marital-status/marital-status.component';
import { PreviousAddressComponent } from '../previous-address/previous-address.component';
import { UpdateClientButtonComponent } from '../update-client-button/update-client-button.component';
import { VaEligibilityComponent } from '../va-eligibility/va-eligibility.component';

@Component({
  selector: 'app-client-info',
  standalone: true,
  host: {
    '[attr.data-synthetic-monitor-id]': 'monitorId()',
  },
  imports: [
    LegalNameComponent,
    ContactInfoComponent,
    MaritalStatusComponent,
    CurrentAddressComponent,
    PreviousAddressComponent,
    VaEligibilityComponent,
    MatButtonModule,
    MatIconModule,
    MatMenuModule,
    UpdateClientButtonComponent,
  ],
  templateUrl: './client-info.component.html',
  styleUrl: './client-info.component.scss',
})
export class ClientInfoComponent {
  @Output() addPhone = new EventEmitter<void>();
  @Output() addPreviousAddress = new EventEmitter<void>();
  private readonly clientFormService = inject(ClientFormService);
  readonly clientActionsHandlerService = inject(ClientActionsHandlerService);
  clientForms = input.required<FormGroup<ClientControls>[]>();
  clientForm = input.required<FormGroup<ClientControls>>();
  clients = input.required<Client[]>();
  selectedClientFormId = input.required<string>();
  isLoanEditingDisabled = input<boolean | undefined>(false);
  spouseAdded = output<void>();

  protected readonly monitorId = computed(() => {
    const borrowerType = this.isPrimaryBorrower() ? 'primary' : 'co-client';

    return `rlxp-client-info-${borrowerType}`;
  });

  /**
   * List of clients excluding the current client
   */
  otherClients = computed(() =>
    this.clients().filter((client) => client.id !== this.clientForm().value.id),
  );

  /**
   * Tuple with other clients and their form object
   */
  otherClientsWithForms = computed(() =>
    this.otherClients().map((client) => {
      const form = this.clientForms().find((control) => control.value.id === client.id);
      return { client, form };
    }),
  );
  isPrimaryBorrower = toValueChangesSignal<boolean>(this.clientForm, 'isPrimaryBorrower');
  personalInfoForm = computed(() => this.clientForm().get('personalInformation') as FormGroup);
  contactInfoForm = computed(() => this.clientForm().get('contactInformation') as FormGroup);
  currentResidenceForm = computed(
    () => this.clientForm().get('residenceInformation.currentResidence') as FormGroup,
  );
  previousResidenceFormArray = computed(
    () => this.clientForm().get('residenceInformation.formerResidences') as FormArray,
  );
  militaryForm = computed(() => this.clientForm().get('military') as FormGroup);

  onAddPhone() {
    this.addPhone.emit();
  }

  onAddPreviousAddress() {
    this.addPreviousAddress.emit();
  }

  addSpouseForThisClient() {
    this.clientFormService.addClient({ spouse: <Client>this.clientForm().value });
    this.spouseAdded.emit();
  }
}
