import { Component, computed, forwardRef, input } from '@angular/core';
import { FormGroup, ReactiveFormsModule } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectChange, MatSelectModule } from '@angular/material/select';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import {
  MilitaryBranch,
  MilitaryComponent,
  MilitaryStatus,
  VADisabilityBenefitsStatus,
} from '@rocket-logic/rl-xp-bff-models';
import { SelectFieldComponent } from '../../_shared/components/select-field/select-field.component';
import { ToggleFieldComponent } from '../../_shared/components/toggle-field/toggle-field.component';
import { RlaFormFieldSuffixComponent } from '../../assistant/components/rla-form-field-suffix/rla-form-field-suffix.component';
import { RlaHighlightDirective } from '../../assistant/directives/rla-highlight.directive';
import {
  MilitaryControls,
  MilitaryServiceGroup,
} from '../../services/entity-state/client-state/form-types';
import { pascalCaseToSentence } from '../../util/formatting-helpers';
import { toValueChangesSignal } from '../../util/value-changes-signal';

@Component({
  selector: 'app-va-eligibility',
  standalone: true,
  templateUrl: './va-eligibility.component.html',
  styleUrl: './va-eligibility.component.scss',
  imports: [
    ReactiveFormsModule,
    MatSlideToggleModule,
    MatSelectModule,
    MatFormFieldModule,
    forwardRef(() => RlaHighlightDirective),
    forwardRef(() => RlaFormFieldSuffixComponent),
    SelectFieldComponent,
    ToggleFieldComponent,
  ],
})
export class VaEligibilityComponent {
  militaryForm = input.required<FormGroup<MilitaryControls>>();

  branchOptions = Object.values(MilitaryBranch).map((branch) => {
    return {
      value: branch,
      display: getMilitaryBranchLabel(branch),
    };
  });

  componentOptions = Object.values(MilitaryComponent).map((component) => ({
    value: component,
    display: getMilitaryComponentLabel(component),
  }));

  militaryStatusOptions = Object.values(MilitaryStatus).map((status) => ({
    value: status,
    display: getMilitaryStatusLabel(status),
  }));

  disabilityOptions = Object.values(VADisabilityBenefitsStatus).map((status) => ({
    value: status,
    display: pascalCaseToSentence(status),
  }));

  hasMilitaryService = toValueChangesSignal<boolean>(this.militaryForm, 'hasMilitaryService');

  getMilitaryServiceForm() {
    return this.militaryForm().get('militaryServices.0') as MilitaryServiceGroup;
  }

  handleMilitaryStatusChange(dischargeStatus: MatSelectChange) {
    if (dischargeStatus?.value !== MilitaryStatus.RetiredDischargedOrSeparatedFromService) {
      if (
        this.getMilitaryServiceForm().value.wasDischargedUnderConditionsOtherThanDishonorable !==
        null
      ) {
        this.getMilitaryServiceForm().controls[
          'wasDischargedUnderConditionsOtherThanDishonorable'
        ].markAsDirty();
        this.getMilitaryServiceForm().controls[
          'wasDischargedUnderConditionsOtherThanDishonorable'
        ].setValue(null);
      }
    }
  }

  militaryStatus = toValueChangesSignal<MilitaryStatus>(
    this.militaryForm,
    'militaryServices.0.status',
  );
  isDischarged = computed(
    () => this.militaryStatus() === MilitaryStatus.RetiredDischargedOrSeparatedFromService,
  );
}

export function getMilitaryStatusLabel(status: MilitaryStatus) {
  return status === MilitaryStatus.RetiredDischargedOrSeparatedFromService
    ? 'Retired, Discharged, Or Separated From Service'
    : pascalCaseToSentence(status);
}

export function getMilitaryComponentLabel(component: MilitaryComponent) {
  return component === MilitaryComponent.ActiveDuty
    ? 'Regular Military'
    : pascalCaseToSentence(component);
}

export function getMilitaryBranchLabel(branch: MilitaryBranch) {
  return branch === MilitaryBranch.NOAA || branch === MilitaryBranch.USPHS
    ? branch
    : pascalCaseToSentence(branch);
}
