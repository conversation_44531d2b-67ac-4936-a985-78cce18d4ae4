import { CommonModule } from '@angular/common';
import { Component, computed, inject, input, output } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { ClientActivationHandlerService } from '../../services/entity-state/client-state/client-activation-handler.service';
import { ClientFormService } from '../../services/entity-state/client-state/client-form.service';
import { ClientStateService } from '../../services/entity-state/client-state/client-state.service';
import { ClientControls } from '../../services/entity-state/client-state/form-types';
import { LoanEditingState } from '../../services/entity-state/loan-state/loan-editing-state.service';
import { ReactivateClientButtonComponent } from '../reactivate-client-button/reactivate-client-button.component';

@Component({
  selector: 'app-add-client-button',
  standalone: true,
  imports: [
    MatButtonModule,
    MatIconModule,
    MatMenuModule,
    ReactivateClientButtonComponent,
    CommonModule,
  ],
  templateUrl: './add-client-button.component.html',
  styleUrl: './add-client-button.component.scss',
})
export class AddClientButtonComponent {
  addClient = output<FormGroup<ClientControls>>();
  isIconOnlyView = input<boolean>(false);

  private readonly clientActivationHandlerService = inject(ClientActivationHandlerService);
  private readonly clientFormService = inject(ClientFormService);
  private readonly clientStateService = inject(ClientStateService);

  deactivatedClients = computed(
    () => this.clientActivationHandlerService.deactivatedClients() ?? [],
  );

  isAddClientDisabled = computed(
    () => this.isLoanEditingDisabled() || !this.clientFormService.canAddNewClient(),
  );

  isLoanEditingDisabled = inject(LoanEditingState).isLoanEditingDisabled;

  clients = computed(() => this.clientStateService.stateValues() ?? []);

  addClientLabel = computed(() => (this.clients().length > 0 ? 'Add Co-Client' : 'Add Client'));

  addClientIcon = computed(() => (this.isIconOnlyView() ? 'add-outlined' : 'add_circle-outlined'));

  addClientIconColor = computed(() => (this.isAddClientDisabled() ? '' : 'primary'));

  onAddClick() {
    if (this.clientFormService.canAddNewClient()) {
      const client = this.clientFormService.addClient();
      this.addClient.emit(client);
    }
  }
}
