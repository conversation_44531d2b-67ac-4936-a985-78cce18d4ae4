@if (deactivatedClients().length) {
  <div class="menu-button-container">
    <button
      [matMenuTriggerFor]="menu"
      [disabled]="isAddClientDisabled()"
      class="rkt-Button rkt-Button--large rkt-Button--tertiary rkt-Button--has-icon"
      [class.icon-only-button]="isIconOnlyView()"
      mat-button
      color="accent"
      [class.rkt-Button--is-disabled]="isAddClientDisabled()"
      data-testid="add-client-menu-trigger"
    >
      <mat-icon class="rkt-Icon" [color]="addClientIconColor()" [svgIcon]="addClientIcon()" />
      @if (!isIconOnlyView()) {
        {{ addClientLabel() }}
      }
    </button>

    <mat-menu #menu xPosition="after" data-testid="add-client-menu">
      @for (client of deactivatedClients(); track client.id) {
        <app-reactivate-client-button [client]="client" />
      }
      <button
        [disabled]="isAddClientDisabled()"
        mat-menu-item
        class="rkt-Menu__item"
        (click)="onAddClick()"
        [class.rkt-Button--is-disabled]="isAddClientDisabled()"
        data-testid="add-co-client-menu-item"
      >
        <mat-icon [svgIcon]="addClientIcon()" />
        Add New Co-Client
      </button>
    </mat-menu>
  </div>
} @else {
  <button
    addButton
    [disabled]="isAddClientDisabled()"
    class="rkt-Button rkt-Button--large rkt-Button--tertiary rkt-Button--has-icon"
    [class.icon-only-button]="isIconOnlyView()"
    mat-button
    color="accent"
    (click)="onAddClick()"
    [class.rkt-Button--is-disabled]="isAddClientDisabled()"
    [attr.data-synthetic-monitor-id]="'add-client'"
    data-testid="add-client-button"
  >
    <mat-icon class="rkt-Icon" [color]="addClientIconColor()" [svgIcon]="addClientIcon()" />
    @if (!isIconOnlyView()) {
      {{ addClientLabel() }}
    }
  </button>
}
