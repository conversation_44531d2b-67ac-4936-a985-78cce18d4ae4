<app-form-section
  titleText="Client(s)"
  icon="group-outlined"
  [formSection]="FormSection.ClientInfo"
>
  <div addButton>
    <app-add-client-button (addClient)="handleAddClient()" />
  </div>

  @if (!isFetching()) {
    @for (clientMap of clientFormService.sortedEntityForms(); track clientMap[0]) {
      <app-client-info
        #clientInfo
        [isLoanEditingDisabled]="isLoanEditingDisabled()"
        [clientForms]="clientFormService.entityValues()"
        [clientForm]="clientMap[1]"
        [clients]="clients()"
        [selectedClientFormId]="clientMap[0]"
        (addPhone)="clientActionsHandlerService.onAddPhone(clientMap[0])"
        (addPreviousAddress)="clientActionsHandlerService.onAddPreviousAddress(clientMap[0])"
        (spouseAdded)="spouseAddedToForm()"
      />
    }

    <app-add-client-button (addClient)="handleAddClient()" />
  } @else {
    <app-client-skeleton />
  }
  @if (!isFetching()) {
    <div section-summary class="row flex-wrap">
      @for (client of clients(); track client) {
        <app-interactible-tile
          [label]="client.isPrimaryBorrower ? 'Primary Client' : 'Co-Client'"
          [content]="client | clientName"
          (tileClick)="openFormSection()"
        ></app-interactible-tile>
      }
    </div>
  } @else {
    <div section-summary class="row flex-wrap">
      <app-tile-skeleton></app-tile-skeleton>
    </div>
  }
  @if (!isFetching()) {
    <div milestone-chip>
      <app-milestone-chip
        [formSection]="FormSection.ClientInfo"
        (chipClick)="openFormSection()"
      ></app-milestone-chip>
    </div>
  }
</app-form-section>
