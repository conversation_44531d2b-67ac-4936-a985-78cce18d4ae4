import { CommonModule } from '@angular/common';
import { Component, computed, ElementRef, inject, viewChild, viewChildren } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatDialog } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import {
  MaritalStatus,
  MilitaryBranch,
  MilitaryComponent,
  MilitaryStatus,
  PhoneNumberType,
  Suffix,
  VADisabilityBenefitsStatus,
} from '@rocket-logic/rl-xp-bff-models';
import { environment } from '../../environments/environment';
import { FormSectionComponent } from '../form-section/form-section.component';
import { MilestoneChipComponent } from '../form-section/milestone-chip/milestone-chip.component';
import { InteractibleTileComponent } from '../interactible-tile/interactible-tile.component';
import { TileSkeletonComponent } from '../interactible-tile/tile-skeleton/tile-skeleton.component';
import { CreditService } from '../services/credit/credit.service';
import { ClientActionsHandlerService } from '../services/entity-state/client-state/client-actions-handler.component';
import { ClientFormService } from '../services/entity-state/client-state/client-form.service';
import { ClientStateService } from '../services/entity-state/client-state/client-state.service';
import { LoanEditingState } from '../services/entity-state/loan-state/loan-editing-state.service';
import { FormNavSectionService, FormSection } from '../services/form-nav/form-nav-section.service';
import { ClientNamePipe } from '../util/client-name.pipe';
import { pascalCaseToSentence } from '../util/formatting-helpers';
import { openFormSection } from '../util/open-form-section';
import { AddClientButtonComponent } from './add-client-button/add-client-button.component';
import { ClientInfoComponent } from './client-info/client-info.component';
import { ClientSkeletonComponent } from './client-skeleton/client-skeleton.component';

@Component({
  selector: 'app-clients-info',
  standalone: true,
  imports: [
    FormSectionComponent,
    MatButtonModule,
    MatIconModule,
    ClientInfoComponent,
    ClientSkeletonComponent,
    InteractibleTileComponent,
    TileSkeletonComponent,
    CommonModule,
    ClientNamePipe,
    MilestoneChipComponent,
    MatMenuModule,
    AddClientButtonComponent,
  ],
  templateUrl: './clients-info.component.html',
  styleUrl: './clients-info.component.scss',
})
export class ClientsInfoComponent {
  clientFormService = inject(ClientFormService);
  clientStateService = inject(ClientStateService);
  creditService = inject(CreditService);
  dialog = inject(MatDialog);
  formNavSectionService = inject(FormNavSectionService);
  readonly FormSection = FormSection;
  clientActionsHandlerService = inject(ClientActionsHandlerService);

  clientInfoRefs = viewChildren(ClientInfoComponent, { read: ElementRef });
  formSectionComponentRef = viewChild.required(FormSectionComponent);
  lastClientInfoRef = computed(() => this.clientInfoRefs()[this.clientInfoRefs().length - 1]);

  isFetching = this.clientStateService.isFetching;
  clients = computed(() => this.clientStateService.stateValues() ?? []);
  isLoanEditingDisabled = inject(LoanEditingState).isLoanEditingDisabled;
  suffixOptions = Object.values(Suffix);
  phoneTypeOptions = Object.values(PhoneNumberType);
  maritalStatusOptions = Object.values(MaritalStatus);
  branchOptions = Object.values(MilitaryBranch).map((branch) => {
    let display: string = '';

    switch (branch) {
      case MilitaryBranch.NOAA:
      case MilitaryBranch.USPHS:
        display = branch;
        break;
      default:
        display = pascalCaseToSentence(branch);
    }

    return { value: branch, display };
  });
  componentOptions = Object.values(MilitaryComponent).map((component) => ({
    value: component,
    display: pascalCaseToSentence(component),
  }));
  militaryStatusOptions = Object.values(MilitaryStatus).map((status) => ({
    value: status,
    display: pascalCaseToSentence(status),
  }));
  disabilityOptions = Object.values(VADisabilityBenefitsStatus).map((status) => ({
    value: status,
    display: pascalCaseToSentence(status),
  }));

  openFormSection() {
    openFormSection(() => this.formSectionComponentRef());
  }

  public scrollToLatestClient() {
    /* using set timeout to wait for the latest client to be rendered,
    normally would use an effect on the clientInfoRefs but this logic
    is simpler than preventing it from scrolling on page loads or other client events which is not desired.
    */
    setTimeout(() => {
      const latestClient = this.lastClientInfoRef().nativeElement;
      latestClient.scrollIntoView({ block: 'start', behavior: environment.scrollBehavior });
    }, 650);
  }

  spouseAddedToForm() {
    this.scrollToLatestClient();
  }

  handleAddClient() {
    this.openFormSection();
    this.scrollToLatestClient();
  }
}
