import { Component, computed, DestroyRef, inject } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { ActivatedRoute, Router } from '@angular/router';
import { filter, map, take } from 'rxjs';
import { TrackClickDirective } from '../../analytics/track-click.directive';
import { ProductFormService } from '../../services/entity-state/product-state/product-form.service';
import { ProductStateService } from '../../services/entity-state/product-state/product-state.service';
import { ProductUpdateHandlerService } from '../../services/entity-state/product-state/product-update-handler.service';
import { PRODUCT_SAVE_TRIGGER } from '../../services/entity-state/product-state/provide-product-state';
import { LoanApplicationStateService } from '../../services/loan-application-state/loan-application-state.service';
import { ProductLoadStatusService } from '../../services/product-load-status.service.ts/product-load-status.service';

@Component({
  selector: 'app-product-save-button',
  standalone: true,
  imports: [MatButtonModule, MatIconModule, TrackClickDirective, MatProgressSpinnerModule],
  templateUrl: './product-save-button.component.html',
  styleUrl: './product-save-button.component.scss',
})
export class ProductSaveButtonComponent {
  readonly productFormService = inject(ProductFormService);
  private readonly productUpdateHandler = inject(ProductUpdateHandlerService);
  private readonly productStateService = inject(ProductStateService);
  private readonly productSaveTrigger = inject(PRODUCT_SAVE_TRIGGER);
  private readonly router = inject(Router);
  private readonly activatedRoute = inject(ActivatedRoute);
  private readonly loanApplicationStateService = inject(LoanApplicationStateService);
  private readonly productLoadStatusService = inject(ProductLoadStatusService);
  private readonly destroyRef = inject(DestroyRef);

  readonly unsavedChanges = this.productUpdateHandler.unsavedChanges;
  readonly isUpdating = this.productStateService.isUpdating;
  readonly isFetching = this.productStateService.isFetching;
  readonly isProductSelected = computed(
    () => this.loanApplicationStateService.state()?.data?.isProductSelected ?? false,
  );
  readonly shouldDisable = computed(
    () =>
      this.isUpdating() ||
      this.isFetching() ||
      (this.isProductSelected() && this.unsavedChanges() === 0) ||
      this.productLoadStatusService.productLoadError() ||
      this.productStateService.stoppingIneligibilities().length > 0,
  );

  onSaveProduct() {
    if (this.shouldDisable()) {
      return;
    }

    if (this.productUpdateHandler.unsavedChanges() === 0) {
      this.productUpdateHandler.updateWithNoChanges();
    } else {
      this.productSaveTrigger.saveChanges();
    }

    if (this.isProductSelected()) {
      this.navigateToPricingTable();
    } else {
      this.productStateService.state$
        .pipe(
          map(({ updating }) => updating),
          filter((updating) => !updating),
          take(1),
          takeUntilDestroyed(this.destroyRef),
        )
        .subscribe(() => this.navigateToPricingTable());
    }
  }

  private navigateToPricingTable() {
    this.router.navigate(['pricing-table'], {
      relativeTo: this.activatedRoute.parent,
    });
  }
}
