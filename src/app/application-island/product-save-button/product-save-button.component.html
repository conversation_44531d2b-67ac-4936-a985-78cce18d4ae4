<button
  data-synthetic-monitor-id="save-product-button"
  class="rkt-Button rkt-Button--large"
  mat-flat-button
  [disabled]="productFormService.productForm.invalid || this.shouldDisable()"
  [class.rkt-Button--is-disabled]="productFormService.productForm.invalid || this.shouldDisable()"
  [class.rkt-Button--is-spinning]="isUpdating()"
  (click)="onSaveProduct()"
  color="primary"
  appTrackClick
  description="Save Product"
>
  @if (isUpdating()) {
    <mat-icon iconPositionStart class="rkt-Icon">
      <mat-spinner
        role="presentation"
        class="rkt-Spinner rkt-Spinner--enterprise"
        diameter="20"
      />
    </mat-icon>
  }
  Save Product
</button>
