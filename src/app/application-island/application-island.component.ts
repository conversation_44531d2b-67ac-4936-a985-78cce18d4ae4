import { CdkPortalOutlet } from '@angular/cdk/portal';
import {
  Component,
  ElementRef,
  OnDestroy,
  OnInit,
  computed,
  contentChildren,
  inject,
} from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import { MatButtonModule } from '@angular/material/button';
import { MatChipsModule } from '@angular/material/chips';
import { MatDividerModule } from '@angular/material/divider';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { RktStackModule } from '@rocketcentral/rocket-design-system-angular';
import {
  RktAlertEnterpriseModule,
  RktLinkEnterpriseModule,
  RktTagEnterpriseModule,
} from '@rocketcentral/rocket-design-system-enterprise-angular';
import { AddressDataService } from '../services/address/address-data.service';
import { CreditService } from '../services/credit/credit.service';
import { isEntityValidationError } from '../services/data-provider/models/update-loan-error';
import { LoanEditingState } from '../services/entity-state/loan-state/loan-editing-state.service';
import { LoanStateService } from '../services/entity-state/loan-state/loan-state.service';
import { ProductStateService } from '../services/entity-state/product-state/product-state.service';
import { ValidationErrorProviderService } from '../services/error/validation-error-provider.service';
import { FormNavSectionService } from '../services/form-nav/form-nav-section.service';
import { LoanApplicationStateService } from '../services/loan-application-state/loan-application-state.service';
import { MessageCenterService } from '../services/message-center/message-center.service';
import { ProductLoadStatusService } from '../services/product-load-status.service.ts/product-load-status.service';
import { ProductSaveFailureListenerService } from '../services/product-save-listener/product-save-failure-listener.service';
import { AppIslandErrorsComponent } from './app-island-errors/app-island-errors.component';
import { EntityStateErrorComponent } from './app-island-errors/entity-state-error/entity-state-error.component';
import { AppIslandIneligibilitiesComponent } from './app-island-ineligibilities/app-island-ineligibilities.component';
import { AppIslandStatusDirective } from './app-island-status.directive';
import { MessageCenterStatusComponent } from './message-center-status/message-center-status.component';
import { ProductLoadErrorsComponent } from './product-load-errors/product-load-errors.component';
import { ProductSaveErrorsComponent } from './product-save-errors/product-save-errors.component';
import { RateLockSuccessAlertComponent } from './rate-lock-success-alert/rate-lock-success-alert.component';

@Component({
  selector: 'app-application-island',
  standalone: true,
  templateUrl: './application-island.component.html',
  styleUrl: './application-island.component.scss',
  imports: [
    RktLinkEnterpriseModule,
    MatChipsModule,
    MatIconModule,
    MatButtonModule,
    MatProgressSpinnerModule,
    RktAlertEnterpriseModule,
    RktStackModule,
    AppIslandErrorsComponent,
    AppIslandIneligibilitiesComponent,
    RktTagEnterpriseModule,
    MatDividerModule,
    MessageCenterStatusComponent,
    RateLockSuccessAlertComponent,
    CdkPortalOutlet,
    ProductLoadErrorsComponent,
    EntityStateErrorComponent,
    ProductSaveErrorsComponent,
  ],
})
export class ApplicationIslandComponent implements OnInit, OnDestroy {
  private loanStateService = inject(LoanStateService);
  private loanEditingState = inject(LoanEditingState);
  private formNavService = inject(FormNavSectionService);
  private elementRef = inject(ElementRef);
  private messageCenterService = inject(MessageCenterService);
  readonly creditService = inject(CreditService);
  readonly productLoadStatusService = inject(ProductLoadStatusService);
  readonly addressDataService = inject(AddressDataService);
  readonly productSaveFailureListener = inject(ProductSaveFailureListenerService);
  readonly isXpCompleted = toSignal(inject(LoanApplicationStateService).isXpCompleted$);

  private readonly productState = inject(ProductStateService);
  readonly ineligibilities = computed(() =>
    this.productState.stoppingIneligibilities().map(({ message }) => message),
  );
  private readonly validationErrorService = inject(ValidationErrorProviderService);
  readonly errors = this.validationErrorService.errors;
  readonly entityStateErrors = computed(() =>
    (this.messageCenterService.stateErrors() ?? []).filter((error) =>
      isEntityValidationError(error.context),
    ),
  );

  readonly statusContent = contentChildren(AppIslandStatusDirective);

  messagesExist = computed(() => {
    return (
      this.messageCenterService.errorCount() > 0 ||
      this.messageCenterService.warningCount() > 0 ||
      this.messageCenterService.infoCount() > 0
    );
  });
  isLoanEditingDisabled = this.loanEditingState.isLoanEditingDisabled;

  isInitialized = toSignal(this.loanStateService.isInitialized$, { requireSync: true });
  lockingError = computed(() =>
    this.messageCenterService.errors().some((error) => error?.shouldLock),
  );

  shouldLock = computed(() => this.isLoanEditingDisabled() || this.lockingError());

  loanDeactivationReason = computed(
    () => this.loanStateService.state()?.data?.loanDeactivationDetails,
  );

  isLoanDeactivated = toSignal(this.loanStateService.isLoanDeactivated$);

  ngOnInit(): void {
    this.formNavService.registerAppIsland(this.elementRef);
  }

  ngOnDestroy(): void {
    this.formNavService.deregisterAppIsland();
  }
}
