<rkt-alert-enterprise variant="warn" [isDismissible]="false">
  <rkt-stack spacing="4" additionalClasses="rkt-Spacing--mb8">
    <h2 rktStackItem class="rkt-Alert__heading">Product Data is Unavailable</h2>

    <p rktStackItem class="rkt-Alert__text">
      Please try again. If the problem persists, contact support and open a ticket.
    </p>
  </rkt-stack>
  <rkt-stack rktAlertActions>
    <div class="rkt-Stack__item--end-column" rktStackItem>
      <button
        class="rkt-Button rkt-Button--secondary rkt-Button--has-icon-left"
        mat-stroked-button
        color="accent"
        [disabled]="productLoadStatusService.isFetchingProductInfo()"
        [class.rkt-Button--is-disabled]="productLoadStatusService.isFetchingProductInfo()"
        [class.rkt-Button--is-spinning]="productLoadStatusService.isFetchingProductInfo()"
        (click)="productLoadStatusService.refreshProductState()"
      >
        @if (productLoadStatusService.isFetchingProductInfo()) {
          <mat-icon class="rkt-Icon">
            <mat-spinner role="presentation" color="accent" class="rkt-Spinner" diameter="16" />
          </mat-icon>
        }
        Try Again
      </button>
    </div>
  </rkt-stack>
</rkt-alert-enterprise>
