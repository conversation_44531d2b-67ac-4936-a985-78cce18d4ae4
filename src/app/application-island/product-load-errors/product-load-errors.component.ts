import { Component, inject } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { RktStackModule } from '@rocketcentral/rocket-design-system-angular';
import { RktAlertEnterpriseModule } from '@rocketcentral/rocket-design-system-enterprise-angular';
import { ProductLoadStatusService } from '../../services/product-load-status.service.ts/product-load-status.service';

@Component({
  selector: 'app-product-load-errors',
  standalone: true,
  imports: [
    RktAlertEnterpriseModule,
    RktStackModule,
    MatIconModule,
    MatProgressSpinnerModule,
    MatButtonModule,
  ],
  templateUrl: './product-load-errors.component.html',
  styleUrl: './product-load-errors.component.scss',
})
export class ProductLoadErrorsComponent {
  readonly productLoadStatusService = inject(ProductLoadStatusService);
}
