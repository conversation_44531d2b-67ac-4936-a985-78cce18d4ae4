<div class="container rkt-Elevation-6" [class.deactivated]="isLoanDeactivated()">
  <div class="app-island-container">
    <div
      class="left-app-island-container"
      [class.deactivated]="isLoanDeactivated()"
      [class.border-transparent]="!(messagesExist() || statusContent().length > 1)"
    >
      @if (isLoanDeactivated()) {
        <div class="flex items-center justify-start w-full">
          <div class="flex gap-4">
            <mat-icon svgIcon="cancel-two_tone" color="warn" class="rkt-Icon app-Icon--error" />
            <div class="flex items-center">
              <span class="rkt-Caption-12 rkt-FontWeight--700 flex items-center">
                Loan {{ loanDeactivationReason()?.deactivationType }}
              </span>
            </div>
          </div>
        </div>
      } @else {
        @if (statusContent().length > 0) {
          @for (content of statusContent(); track content; let last = $last) {
            <ng-template [cdkPortalOutlet]="content" />

            @if (!last) {
              <mat-divider class="mat-divider rkt-VerticalDivider" [vertical]="true" />
            }
          }
        } @else {
          <ng-content select="[appIslandStatus]" />
        }

        @if (messagesExist()) {
          <mat-divider class="mat-divider rkt-VerticalDivider" [vertical]="true" />
          <app-message-center-status />
        }
      }
    </div>
    <div class="action-group-flex-container">
      @if (isInitialized() && !shouldLock()) {
        <ng-content select="[appIslandProgress]" />
      }
      <ng-content select="[appIslandAction]" />
    </div>
  </div>

  @if (isXpCompleted()) {
    <app-rate-lock-success-alert class="rate-lock-success-alert" />
  }
  @if (errors().length > 0) {
    <app-app-island-errors [errors]="errors()" />
  }
  @if (ineligibilities().length > 0) {
    <app-app-island-ineligibilities [ineligibilities]="ineligibilities()" />
  }
  @if (!!productLoadStatusService.productLoadError()) {
    <app-product-load-errors />
  }
  @if ((productSaveFailureListener.productSaveFailure()?.length ?? 0) > 0) {
    <app-product-save-errors [errorMessage]="productSaveFailureListener.productSaveFailure()" />
  }

  @if (entityStateErrors().length) {
    <app-entity-state-error [errors]="entityStateErrors()" />
  }
</div>
