import { Component, input } from '@angular/core';
import { RktAlertEnterpriseModule } from '@rocketcentral/rocket-design-system-enterprise-angular';

@Component({
  selector: 'app-app-island-ineligibilities',
  standalone: true,
  imports: [RktAlertEnterpriseModule],
  templateUrl: './app-island-ineligibilities.component.html',
  styleUrl: './app-island-ineligibilities.component.scss',
})
export class AppIslandIneligibilitiesComponent {
  readonly ineligibilities = input.required<string[]>();
}
