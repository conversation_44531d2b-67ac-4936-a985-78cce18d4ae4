import { DatePipe } from '@angular/common';
import { Component, ElementRef, computed, inject } from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import { RktSkeletonModule } from '@rocketcentral/rocket-design-system-angular';
import { RktAlertEnterpriseModule } from '@rocketcentral/rocket-design-system-enterprise-angular';
import { ProductStateService } from '../../services/entity-state/product-state/product-state.service';
import { RateLockService } from '../../services/rate-lock/rate-lock.service';

@Component({
  selector: 'app-rate-lock-success-alert',
  standalone: true,
  imports: [RktAlertEnterpriseModule, DatePipe, RktSkeletonModule],
  templateUrl: './rate-lock-success-alert.component.html',
  styleUrl: './rate-lock-success-alert.component.scss',
  host: {
    '[class.hidden]': '!rateLockAvailable()',
  },
})
export class RateLockSuccessAlertComponent {
  productStateService = inject(ProductStateService);
  rateLockService = inject(RateLockService);
  elementRef = inject(ElementRef);

  private lockDetails = toSignal(this.rateLockService.rateLockDetails$);
  lockExpiration = computed(() => this.lockDetails()?.expiresOn);
  rate = computed(() => this.productStateService.state()?.data?.rate);
  rateLocked = toSignal(this.rateLockService.isRateLocked$);
  rateLockAvailable = computed(() => this.rate() && this.lockExpiration() && this.rateLocked());
}
