@use '@rocketcentral/rocket-design-system-styles/web/scss/color' as rkt-colors;
@use '@rocketcentral/rocket-design-system-styles/web/scss/color-dark' as rkt-colors-dark;

:host {

  --alert-tag-background: #{rkt-colors.$rkt-orange-50};
  --alert-tag-color: #{rkt-colors.$rkt-orange-900};

  rkt-tag-enterprise {
    display: flex;
    cursor: pointer;

    ::ng-deep {
      .rkt-Tag.rkt-Tag--enterprise {
        padding: 4px 6px;
  
        &.rkt-Tag--caution .rkt-Tag__icon {
          color: var(--rlxp-warning-icon-color);
        }
  
        mat-icon {
          transform: scale(0.84);
        }
      }
    }

    &.app-Tag--alert {
      ::ng-deep {
        .rkt-Tag.rkt-Tag--enterprise {
          background-color: var(--alert-tag-background);
  
          mat-icon.mat-icon.mat-icon-no-color.rkt-Tag__icon {
            color: var(--alert-tag-color);
          }
        }
      }
    }
  }
}

:host-context(.rkt-DarkMode) {
  --alert-tag-background: #{rkt-colors-dark.$rkt-orange-dark-50};
}

.message-group-container {
  display: flex;
  gap: 6px;
}
