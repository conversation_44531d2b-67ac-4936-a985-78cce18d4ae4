<div class="message-group-container">
  @if (numberOfErrors() > 0) {
    <button (click)="openMessageCenter()" matTooltip="Stopper(s)">
      <rkt-tag-enterprise variant="warn" iconName="forbidden" iconPosition="left">
        <span>{{ numberOfErrors() }}</span>
      </rkt-tag-enterprise>
    </button>
  }

  @if (numberOfQfrs() > 0) {
    <button (click)="openMessageCenter()" matTooltip="QFR(s)">
      <rkt-tag-enterprise
        class="app-Tag--alert"
        variant="caution"
        iconName="error-outlined"
        iconPosition="left"
      >
        <span>{{ numberOfQfrs() }}</span>
      </rkt-tag-enterprise>
    </button>
  }

  @if (numberOfWarnings() > 0) {
    <button (click)="openMessageCenter()" matTooltip="Warning(s)">
      <rkt-tag-enterprise
        variant="caution"
        iconName="warning_amber-outlined"
        iconPosition="left"
        class="warning-icon"
      >
        <span>{{ numberOfWarnings() }}</span>
      </rkt-tag-enterprise>
    </button>
  }

  @if (numberOfInfo() > 0) {
    <button (click)="openMessageCenter()" matTooltip="Info">
      <rkt-tag-enterprise
        variant="info"
        iconName="info-outlined"
        iconPosition="left"
        class="info-icon"
      >
        <span>{{ numberOfInfo() }}</span>
      </rkt-tag-enterprise>
    </button>
  }
</div>
