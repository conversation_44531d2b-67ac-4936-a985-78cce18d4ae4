<button
  [matMenuTriggerFor]="menu"
  mat-stroked-button
  class="rkt-Button rkt-Button--secondary rkt-Button--large rkt-Button--has-icon"
>
  Loan Actions
  <mat-icon class="rkt-Icon" fontIcon="arrow_drop_down" iconPositionEnd />
</button>

<mat-menu #menu xPosition="after" class="loan-actions-menu rkt-Elevation-6">
  @if (routeType() !== RouteType.PresentLoan) {
    <app-present-loan-button />
  }
  <app-rate-lock />
  <!-- TODO: Enable rate lock transfer post MVP { -->
  <!-- @if (!isRateLocked()) { -->
  <!--   <app-rate-lock-transfer /> -->
  <!-- } -->
  <app-more-actions />
  <mat-divider class="rkt-HorizontalDivider" />
  <app-deny-button />
  <app-withdraw-button />
  @if (isLoanComplete()) {
    <app-view-history-button />
  }
</mat-menu>
