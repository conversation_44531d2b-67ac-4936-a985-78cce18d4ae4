import { Component, computed, inject } from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import { MatButtonModule } from '@angular/material/button';
import { MatDividerModule } from '@angular/material/divider';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { DenyButtonComponent } from '../../deny-withdraw/deny-button/deny-button.component';
import { WithdrawButtonComponent } from '../../deny-withdraw/withdraw-button/withdraw-button.component';
import { ViewHistoryButtonComponent } from '../../loan-history/view-history-button/view-history-button.component';
import { RateLockComponent } from '../../rate-lock/rate-lock.component';
import { LoanApplicationStateService } from '../../services/loan-application-state/loan-application-state.service';
import { RateLockService } from '../../services/rate-lock/rate-lock.service';
import { RouteDataService } from '../../services/route-data/route-data.service';
import { RouteType } from '../../util/route-type';
import { MoreActionsComponent } from '../more-actions/more-actions.component';
import { PresentLoanButtonComponent } from '../present-loan-button/present-loan-button.component';

@Component({
  selector: 'app-loan-actions-button',
  standalone: true,
  imports: [
    MatButtonModule,
    MatIconModule,
    MatMenuModule,
    DenyButtonComponent,
    WithdrawButtonComponent,
    RateLockComponent,
    MatDividerModule,
    PresentLoanButtonComponent,
    MoreActionsComponent,
    ViewHistoryButtonComponent,
  ],
  templateUrl: './loan-actions-button.component.html',
  styleUrl: './loan-actions-button.component.scss',
})
export class LoanActionsButtonComponent {
  private readonly routeDataService = inject(RouteDataService);
  private readonly loanApplicationStateService = inject(LoanApplicationStateService);

  rateLockService = inject(RateLockService);
  isRateLocked = toSignal(this.rateLockService.isRateLocked$);
  isLoanComplete = toSignal(this.loanApplicationStateService.isXpCompleted$, {
    initialValue: false,
  });

  readonly RouteType = RouteType;
  readonly routeType = computed(() => this.routeDataService.aggregateRouteData().routeType);
}
