import { Component, Input } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { RktStackModule } from '@rocketcentral/rocket-design-system-angular';
import { RktAlertEnterpriseModule } from '@rocketcentral/rocket-design-system-enterprise-angular';

@Component({
  selector: 'app-product-save-errors',
  standalone: true,
  imports: [
    RktAlertEnterpriseModule,
    RktStackModule,
    MatIconModule,
    MatProgressSpinnerModule,
    MatButtonModule,
  ],
  templateUrl: './product-save-errors.component.html',
  styleUrl: './product-save-errors.component.scss',
})
export class ProductSaveErrorsComponent {
  @Input() errorMessage: string | null = null;
}
