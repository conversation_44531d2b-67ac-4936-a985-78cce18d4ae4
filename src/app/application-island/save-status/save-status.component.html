<div class="flex items-center gap-2">
  @if (isUpdating()) {
    <mat-spinner class="rkt-ProgressSpinner rkt-ProgressSpinner--enterprise" diameter="24" />
    <span class="rkt-Caption-12 rkt-FontWeight--500">Saving</span>
  } @else if (saveFailed()) {
    <mat-icon class="rkt-Icon app-Icon--error" svgIcon="error-two_tone" />
    <span class="rkt-Caption-12 rkt-FontWeight--500">
      Save Failed
    </span>
  } @else if (unsavedChanges() > 0) {
    <app-inline-badge>{{unsavedChanges()}}</app-inline-badge>
    <span class="rkt-Caption-12 rkt-FontWeight--500">Unsaved</span>
  } @else {
    <mat-icon class="rkt-Icon app-Icon--success" svgIcon="check_circle-two_tone" />
    <span class="rkt-Caption-12 rkt-Font-Weight--500">Saved</span>
  }
</div>