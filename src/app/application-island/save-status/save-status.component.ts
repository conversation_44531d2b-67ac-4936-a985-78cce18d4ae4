import { Component, computed, input } from '@angular/core';
import { takeUntilDestroyed, toObservable, toSignal } from '@angular/core/rxjs-interop';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { merge, shareReplay, switchMap } from 'rxjs';
import { InlineBadgeComponent } from '../../inline-badge/inline-badge.component';
import {
  EntityStateErrorType,
  EntityStateService,
} from '../../services/entity-state/abstract-entity-state.service';
import { UpdateHandlerService } from '../../services/entity-state/abstract-update-handler.service';

@Component({
  selector: 'app-save-status',
  standalone: true,
  imports: [MatProgressSpinnerModule, MatIconModule, InlineBadgeComponent],
  templateUrl: './save-status.component.html',
  styleUrl: './save-status.component.scss',
})
export class SaveStatusComponent {
  readonly updateHandlers = input.required<UpdateHandlerService[]>();
  readonly stateServices = input.required<EntityStateService<any>[]>();

  readonly validationErrors$ = toObservable(this.updateHandlers).pipe(
    switchMap((handler) => merge(...handler.map((h) => h.validationErrors$))),
    takeUntilDestroyed(),
    shareReplay(1),
  );
  readonly validationErrors = toSignal(this.validationErrors$);
  readonly isUpdating = computed(() =>
    this.stateServices().some((service) => service.isUpdating()),
  );
  readonly saveFailed = computed(
    () =>
      this.stateServices().some(
        (service) => service.state()?.error?.type === EntityStateErrorType.UpdateError,
      ) || (this.validationErrors()?.length ?? 0) > 0,
  );
  readonly unsavedChanges = computed(() =>
    this.updateHandlers().reduce((acc, handler) => acc + handler.unsavedChanges(), 0),
  );
}
