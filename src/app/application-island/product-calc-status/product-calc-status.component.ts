import { Component, computed, inject } from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { EntityStateErrorType } from '../../services/entity-state/abstract-entity-state.service';
import { ProductStateService } from '../../services/entity-state/product-state/product-state.service';
import { ProductUpdateHandlerService } from '../../services/entity-state/product-state/product-update-handler.service';

@Component({
  selector: 'app-product-calc-status',
  standalone: true,
  imports: [MatProgressSpinnerModule, MatIconModule],
  templateUrl: './product-calc-status.component.html',
  styleUrl: './product-calc-status.component.scss',
})
export class ProductCalcStatusComponent {
  private readonly productStateService = inject(ProductStateService);
  private readonly productUpdateHandler = inject(ProductUpdateHandlerService);

  productValidationErrors = toSignal(this.productUpdateHandler.validationErrors$);
  uncalcdChanges = this.productUpdateHandler.uncalcdChanges;
  isCalculating = computed(() => this.productStateService.state()?.calculating ?? false);
  calculationFailed = computed(
    () =>
      this.productStateService.state()?.error?.type === EntityStateErrorType.CalculateError ||
      (this.productValidationErrors()?.length ?? 0 > 0),
  );
}
