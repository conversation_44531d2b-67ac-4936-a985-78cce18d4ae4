<div class="flex items-center relative">
  @if (isCalculating()) {
    <mat-spinner class="rkt-ProgressSpinner rkt-ProgressSpinner--enterprise" diameter="24" />
  } @else if (calculationFailed()) {
    <mat-icon class="rkt-Icon opacity-50" svgIcon="calculate-outlined" />
    <mat-icon class="rkt-Icon app-Icon--error subscript-icon" svgIcon="error-outlined" />
  } @else if (uncalcdChanges() > 0) {
    <mat-icon class="rkt-Icon opacity-50" svgIcon="calculate-outlined" />
  } @else {
    <mat-icon class="rkt-Icon app-Icon--success" svgIcon="calculate-two_tone" />
  }
</div>