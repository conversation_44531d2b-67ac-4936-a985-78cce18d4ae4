import { Component, computed, inject, input } from '@angular/core';
import { ProgressBarComponent } from '../../progress-bar/progress-bar.component';
import { ActiveActionService } from '../../services/active-action.service';
import { FormNavInputService } from '../../services/form-nav/form-nav-input.service';
import { AppIslandAction } from '../app-island-action-button/app-island-action-button.component';

@Component({
  selector: 'app-loan-milestone-progress',
  standalone: true,
  imports: [ProgressBarComponent],
  templateUrl: './loan-milestone-progress.component.html',
  styleUrl: './loan-milestone-progress.component.scss',
})
export class LoanMilestoneProgressComponent {
  activeActionOverride = input<AppIslandAction | null>(null);

  private readonly formInputNavService = inject(FormNavInputService);
  private readonly activeActionService = inject(ActiveActionService);

  activeAction = computed(() => {
    return this.activeActionOverride()
      ? this.activeActionOverride()
      : this.activeActionService.activeAction();
  });
  creditItemsToComplete = computed(() => this.formInputNavService.availableSections().length);
  productItemsToComplete = computed(
    () => this.formInputNavService.availableProductSections().length,
  );
  currentItemsToComplete = computed(() => {
    switch (this.activeAction()!) {
      case AppIslandAction.CreditRequest:
        return this.creditItemsToComplete();
      case AppIslandAction.GetPricing:
        return this.productItemsToComplete();
    }
  });
  creditMilestoneProgress = computed(() => {
    const sectionCount = this.formInputNavService.creditMilestoneOrder().size;
    const availableSectionCount = this.formInputNavService.availableSections().length;
    return this.calculateProgress(sectionCount, availableSectionCount);
  });
  productMilestonesProgress = computed(() => {
    const sectionCount = this.formInputNavService.productMilestonesOrder().size;
    const availableSectionCount = this.formInputNavService.availableProductSections().length;
    return this.calculateProgress(sectionCount, availableSectionCount);
  });
  currentMilestoneProgress = computed(() => {
    switch (this.activeAction()!) {
      case AppIslandAction.CreditRequest:
        return this.creditMilestoneProgress();
      case AppIslandAction.GetPricing:
        return this.productMilestonesProgress();
    }
  });

  navigateActionInputs() {
    this.activeActionService.navigateActionInputs();
  }

  private calculateProgress(totalSections: number, availableSections: number) {
    if (totalSections === 0) {
      return 100;
    }

    return ((totalSections - availableSections) / totalSections) * 100;
  }
}
