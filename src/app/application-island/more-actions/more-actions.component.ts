import { Component, computed, inject } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { environment } from '../../../environments/environment';
import { LoanStateService } from '../../services/entity-state/loan-state/loan-state.service';
import { LoanIdService } from '../../services/loan-id/loan-id.service';
import { UserAuthorizationService } from '../../services/user-authorization/user-authorization.service';

@Component({
  selector: 'app-more-actions',
  standalone: true,
  imports: [MatIconModule, MatButtonModule, MatMenuModule],
  templateUrl: './more-actions.component.html',
  styleUrl: './more-actions.component.scss',
})
export class MoreActionsComponent {
  private readonly userAuthorizationService = inject(UserAuthorizationService);
  private readonly isLoanDeactivated = inject(LoanStateService).isLoanDeactivated;
  private readonly isBankerLicensed = inject(UserAuthorizationService).isBankerLicensed;
  private readonly loanIdService = inject(LoanIdService);

  readonly shouldDisable = computed(
    () =>
      this.isLoanDeactivated() ||
      !this.isBankerLicensed()?.write ||
      this.userAuthorizationService.hasConflictingLoans(),
  );

  moreActions() {
    window.open(
      `${environment.shellDashboardUrl}${this.loanIdService.loanId()}?activeToolbarApp=actions`,
      '_blank',
    );
  }
}
