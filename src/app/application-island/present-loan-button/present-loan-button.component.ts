import { Component, inject } from '@angular/core';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { ActivatedRoute, Router } from '@angular/router';

@Component({
  selector: 'app-present-loan-button',
  standalone: true,
  imports: [MatIconModule, MatMenuModule],
  templateUrl: './present-loan-button.component.html',
  styleUrl: './present-loan-button.component.scss',
})
export class PresentLoanButtonComponent {
  private readonly router = inject(Router);
  private readonly activatedRoute = inject(ActivatedRoute);

  navigateToPresentLoan() {
    this.router.navigate(['present-loan'], { relativeTo: this.activatedRoute.parent });
  }
}
