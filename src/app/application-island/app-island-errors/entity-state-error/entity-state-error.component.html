<rkt-alert-enterprise variant="warn" [isDismissible]="true" (dismissEvent)="dismissAllErrors()">
  <div class="content">
    @for (error of errors(); track error) {
      <div class="rkt-Alert__text">
        @if (isAnticipatedClosingDateError(error.context)) {
          <strong>Invalid Anticipated Closing Date:</strong>
        } @else {
          <strong> {{ error.context.entityStateName | entityName }}:</strong>
        }
        <ul class="pl-2">
          @for (reason of error.context.failures; track reason) {
            <li>{{ reason }}</li>
          }
        </ul>
      </div>
    }
  </div>
</rkt-alert-enterprise>
