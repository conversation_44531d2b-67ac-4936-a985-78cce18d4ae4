import { Component } from '@angular/core';
import { RktAlertEnterpriseModule } from '@rocketcentral/rocket-design-system-enterprise-angular';
import { AutoPricingLinksDirective } from '../../../directives/auto-pricing-links.directive';
import {
    EntityStateErrorContext,
    isAnticipatedClosingDateError,
} from '../../../services/data-provider/models/update-loan-error';
import { EntityStateErrorType } from '../../../services/entity-state/abstract-entity-state.service';
import { EntityNamePipe } from '../../../util/entity-name.pipe';
import { AbstractAppIslandErrors } from '../app-island-errors.component';

@Component({
  selector: 'app-entity-state-error',
  standalone: true,
  imports: [RktAlertEnterpriseModule, EntityNamePipe, AutoPricingLinksDirective],
  templateUrl: './entity-state-error.component.html',
  styleUrl: './entity-state-error.component.scss',
})
export class EntityStateErrorComponent extends AbstractAppIslandErrors<
  {
    type: EntityStateErrorType | undefined;
    onDismiss: () => void;
    context: EntityStateErrorContext;
  }[]
> {
  readonly isAnticipatedClosingDateError = isAnticipatedClosingDateError;
}
