import { Component, Directive, input } from '@angular/core';
import { EnhancedErrorDisplayComponent } from '../../components/enhanced-error-display/enhanced-error-display.component';
import { ValidationErrorDto } from '../../services/error/validation-error-provider.service';

@Directive({
  host: {
    '[class.hidden]': '!errors().length',
  },
})
export abstract class AbstractAppIslandErrors<T extends Array<{ onDismiss: () => void }>> {
  readonly errors = input.required<T>();

  dismissAllErrors() {
    this.errors().forEach((error) => error.onDismiss());
  }
}

@Component({
  selector: 'app-app-island-errors',
  standalone: true,
  imports: [EnhancedErrorDisplayComponent],
  template: `<app-enhanced-error-display [errors]="errors()" />`,
  styleUrl: './app-island-errors.component.scss',
  host: {
    '[class.flex]': 'errors().length',
  },
})
export class AppIslandErrorsComponent extends AbstractAppIslandErrors<ValidationErrorDto[]> {}
