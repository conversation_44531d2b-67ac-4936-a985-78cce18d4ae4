import { Component, Directive, input } from '@angular/core';
import { RktAlertEnterpriseModule } from '@rocketcentral/rocket-design-system-enterprise-angular';
import { FormPathLabelPipe } from '../../form-path-label.pipe';
import { ValidationErrorDto } from '../../services/error/validation-error-provider.service';
import { EntityNamePipe } from '../../util/entity-name.pipe';

@Directive({
  host: {
    '[class.hidden]': '!errors().length',
  },
})
export abstract class AbstractAppIslandErrors<T extends Array<{ onDismiss: () => void }>> {
  readonly errors = input.required<T>();

  dismissAllErrors() {
    this.errors().forEach((error) => error.onDismiss());
  }
}

@Component({
  selector: 'app-app-island-errors',
  standalone: true,
  imports: [RktAlertEnterpriseModule, FormPathLabelPipe, EntityNamePipe],
  templateUrl: './app-island-errors.component.html',
  styleUrl: './app-island-errors.component.scss',
  host: {
    '[class.flex]': 'errors().length',
  },
})
export class AppIslandErrorsComponent extends AbstractAppIslandErrors<ValidationErrorDto[]> {}
