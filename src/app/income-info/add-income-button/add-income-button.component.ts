import { NgClass } from '@angular/common';
import { Component, EventEmitter, Output, computed, inject, input } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { Client } from '@rocket-logic/rl-xp-bff-models';
import { LoanEditingState } from '../../services/entity-state/loan-state/loan-editing-state.service';
import { ClientNamePipe } from '../../util/client-name.pipe';

@Component({
  selector: 'app-add-income-button',
  standalone: true,
  imports: [MatButtonModule, MatIconModule, MatMenuModule, ClientNamePipe, NgClass],
  templateUrl: './add-income-button.component.html',
  styleUrl: './add-income-button.component.scss',
})
export class AddIncomeButtonComponent {
  clients = input.required<Client[]>();
  icon = input<string>('add_circle-outlined');
  color = input<string>('primary');
  isStroked = input<boolean>(false);
  isLarge = input<boolean>(true);
  isDisabled = inject(LoanEditingState).isLoanEditingDisabled;

  @Output() addIncome = new EventEmitter<Client>();

  onClientClick(client: Client) {
    this.addIncome.emit(client);
  }

  getButtonClass = computed(() =>
    this.isStroked() ? 'rkt-Button--secondary' : 'rkt-Button--tertiary',
  );
}
