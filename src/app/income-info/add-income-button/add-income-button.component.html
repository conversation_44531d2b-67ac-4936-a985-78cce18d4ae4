<button
  [disabled]="isDisabled()"
  mat-button
  data-synthetic-monitor-id="addIncome"
  class="rkt-Button rkt-Button--has-icon"
  [class.rkt-Button--large]="isLarge()"
  [ngClass]="getButtonClass()"
  [class.rkt-Button--is-disabled]="isDisabled()"
  color="accent"
  [matMenuTriggerFor]="menu"
  [attr.mat-stroked-button]="isStroked()"
>
  <mat-icon class="rkt-Icon" color="{{ color() }}" svgIcon="{{ icon() }}"></mat-icon>
  Add Income
</button>

<mat-menu #menu="matMenu">
  @for (client of clients(); track client) {
    <button
      mat-menu-item
      (click)="onClientClick(client)"
      [attr.data-synthetic-monitor-id]="'rlxp-add-income-client-' + $index"
    >
      {{ client | clientName }}
    </button>
  }
</mat-menu>
