import { ComponentFixture, TestBed } from '@angular/core/testing';

import { signal } from '@angular/core';
import { FormControl, FormGroup } from '@angular/forms';
import { MockBuilder, MockInstance } from 'ng-mocks';
import { SelectFieldComponent } from '../../_shared/components/select-field/select-field.component';
import { FormattedNumberInputComponent } from '../../question-input/formatted-number-input/formatted-number-input.component';
import { IncomePaymentComponent } from './income-payment.component';

describe('IncomePaymentComponent', () => {
  let component: IncomePaymentComponent;
  let fixture: ComponentFixture<IncomePaymentComponent>;

  beforeEach(() => MockBuilder(IncomePaymentComponent));
  beforeEach(() => {
    MockInstance(FormattedNumberInputComponent, 'matFormField', signal(undefined));
    MockInstance(SelectFieldComponent, 'matFormField', signal(undefined));
    fixture = TestBed.createComponent(IncomePaymentComponent);
    component = fixture.componentInstance;
    fixture.componentRef.setInput(
      'formGroup',
      new FormGroup({ base: new FormGroup({ clientReportedAmount: new FormControl() }) }),
    );
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
