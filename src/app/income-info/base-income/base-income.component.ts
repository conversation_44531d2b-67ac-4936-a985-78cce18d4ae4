import { Component, EventEmitter, Output, inject, input } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { MatSelectModule } from '@angular/material/select';
import { IncomeType } from '@rocket-logic/rl-xp-bff-models';
import { AllIncomeGroup } from '../../services/entity-state/income-state/form-types';
import { SUPPORTED_INCOME_OPTIONS } from '../../services/entity-state/income-state/supported-income';
import { LoanEditingState } from '../../services/entity-state/loan-state/loan-editing-state.service';
import { IncomeDisplayPipe } from '../../util/income-display.pipe';
import { IncomePaymentComponent } from '../income-payment/income-payment.component';
import { IncomeTypeAutocompleteComponent } from '../income-type-autocomplete/income-type-autocomplete.component';
import { StandardEmploymentIncomeComponent } from '../standard-employment-income/standard-employment-income.component';

@Component({
  selector: 'app-base-income',
  standalone: true,
  imports: [
    ReactiveFormsModule,
    MatFormFieldModule,
    MatSelectModule,
    StandardEmploymentIncomeComponent,
    IncomePaymentComponent,
    MatButtonModule,
    MatIconModule,
    MatMenuModule,
    IncomeTypeAutocompleteComponent,
    IncomeDisplayPipe,
  ],
  templateUrl: './base-income.component.html',
  styleUrl: './base-income.component.scss',
})
export class BaseIncomeComponent {
  readonly IncomeType = IncomeType;
  readonly incomeTypeOptions = SUPPORTED_INCOME_OPTIONS;
  formGroup = input.required<AllIncomeGroup>();
  isDisabled = inject(LoanEditingState).isLoanEditingDisabled;

  @Output() delete = new EventEmitter<void>();

  onDeleteClick() {
    this.delete.emit();
  }
}
