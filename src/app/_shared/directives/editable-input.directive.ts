import {
  AfterViewInit,
  computed,
  DestroyRef,
  Directive,
  ElementRef,
  inject,
  input,
  signal,
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { ChangedProperty } from '@rocket-logic/rl-xp-bff-models';
import { map, startWith } from 'rxjs';
import { CONTROL_PROVIDER } from '../tokens/control-provider';

@Directive({
  standalone: true,
  selector: '[appEditableInput]',
})
export class EditableInputDirective implements AfterViewInit {
  private readonly controlProvider = inject(CONTROL_PROVIDER);
  private readonly destroyRef = inject(DestroyRef);
  readonly elementRef = inject(ElementRef);

  readonly changedProperty = input<ChangedProperty>();
  readonly control = computed(() => this.controlProvider.control());
  readonly canBeSaved = signal(false);
  readonly isDisabled = signal(false);

  ngAfterViewInit() {
    this.control()
      .valueChanges.pipe(
        startWith(this.control()),
        map(() => this.control().dirty),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe((isDirty) => this.canBeSaved.set(isDirty && this.control().valid));

    this.control()
      .statusChanges.pipe(
        startWith(this.control().status),
        map((status) => status === 'DISABLED'),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe((isDisabled) => this.isDisabled.set(isDisabled));
  }
}
