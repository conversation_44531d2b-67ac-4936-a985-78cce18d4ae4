import { Component, forwardRef, inject, input, signal, viewChild } from '@angular/core';
import { FormControl, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { ErrorStateMatcher } from '@angular/material/core';
import { MatForm<PERSON>ield, MatFormFieldModule } from '@angular/material/form-field';
import { MatInput, MatInputModule } from '@angular/material/input';
import { NgxMaskDirective, provideNgxMask } from 'ngx-mask';
import { getErrorMessage } from '../../../util/get-error-message';
import { CONTROL_PROVIDER, ControlProvider } from '../../tokens/control-provider';
import { FIELD_VALUE_PREVIEWER, FieldValuePreviewer } from '../../tokens/field-value-previewer';
import {
  MAT_FORM_FIELD_PROVIDER,
  MatFormFieldProvider,
} from '../../tokens/mat-form-field-provider';

@Component({
  selector: 'app-text-field',
  templateUrl: './text-field.component.html',
  styleUrl: './text-field.component.scss',
  standalone: true,
  imports: [MatFormFieldModule, ReactiveFormsModule, MatInputModule, NgxMaskDirective, FormsModule],
  providers: [
    {
      provide: CONTROL_PROVIDER,
      useExisting: forwardRef(() => TextFieldComponent),
    },
    {
      provide: MAT_FORM_FIELD_PROVIDER,
      useExisting: forwardRef(() => TextFieldComponent),
    },
    {
      provide: FIELD_VALUE_PREVIEWER,
      useExisting: forwardRef(() => TextFieldComponent),
    },
  ],
  viewProviders: [provideNgxMask()],
  host: {
    '[class.cursor-pointer]': 'preview()',
  },
})
export class TextFieldComponent
  implements ControlProvider, MatFormFieldProvider, FieldValuePreviewer
{
  private defaultErrorStateMatcher = inject(ErrorStateMatcher);
  matFormField = viewChild(MatFormField);
  control = input.required<FormControl>();
  label = input<string>('');
  customError = input<string | null>(null);
  errorStateMatcher = input<InstanceType<typeof MatInput>['errorStateMatcher']>(
    this.defaultErrorStateMatcher,
  );

  type = input<'text' | 'number' | 'tel'>('text');

  mask = input<InstanceType<typeof NgxMaskDirective>['maskExpression']>(null);
  keepCharacterPositions =
    input<InstanceType<typeof NgxMaskDirective>['keepCharacterPositions']>(null);
  preview = signal<string | null>(null);

  getError() {
    return getErrorMessage(this.control());
  }
}
