import { Component, forwardRef, input, signal } from '@angular/core';
import { FormControl, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { CONTROL_PROVIDER, ControlProvider } from '../../tokens/control-provider';
import { FIELD_VALUE_PREVIEWER, FieldValuePreviewer } from '../../tokens/field-value-previewer';

@Component({
  selector: 'app-toggle-field',
  templateUrl: './toggle-field.component.html',
  standalone: true,
  imports: [MatSlideToggleModule, ReactiveFormsModule, FormsModule],
  providers: [
    {
      provide: CONTROL_PROVIDER,
      useExisting: forwardRef(() => ToggleFieldComponent),
    },
    {
      provide: FIELD_VALUE_PREVIEWER,
      useExisting: forwardRef(() => ToggleFieldComponent),
    },
  ],
})
export class ToggleFieldComponent implements ControlProvider, FieldValuePreviewer {
  control = input.required<FormControl>();
  label = input<string>('');
  preview = signal<any | null>(null);
}
