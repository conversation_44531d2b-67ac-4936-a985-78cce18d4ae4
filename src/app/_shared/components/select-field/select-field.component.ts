import { Component, forwardRef, inject, input, output, signal, viewChild } from '@angular/core';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { ErrorStateMatcher } from '@angular/material/core';
import { FloatLabelType, MatFormField, MatFormFieldModule } from '@angular/material/form-field';
import { MatSelect, MatSelectChange, MatSelectModule } from '@angular/material/select';
import { SelectOption } from '../../../type-ahead-select/type-ahead-select.component';
import { getErrorMessage } from '../../../util/get-error-message';
import { CONTROL_PROVIDER, ControlProvider } from '../../tokens/control-provider';
import { FIELD_VALUE_PREVIEWER, FieldValuePreviewer } from '../../tokens/field-value-previewer';
import {
  MAT_FORM_FIELD_PROVIDER,
  MatFormFieldProvider,
} from '../../tokens/mat-form-field-provider';

@Component({
  selector: 'app-select-field',
  templateUrl: './select-field.component.html',
  styleUrl: './select-field.component.scss',
  standalone: true,
  imports: [MatFormFieldModule, ReactiveFormsModule, MatSelectModule],
  providers: [
    {
      provide: CONTROL_PROVIDER,
      useExisting: forwardRef(() => SelectFieldComponent),
    },
    {
      provide: MAT_FORM_FIELD_PROVIDER,
      useExisting: forwardRef(() => SelectFieldComponent),
    },
    {
      provide: FIELD_VALUE_PREVIEWER,
      useExisting: forwardRef(() => SelectFieldComponent),
    },
  ],
  host: {
    '[class.cursor-pointer]': 'preview()',
  },
})
export class SelectFieldComponent<T>
  implements ControlProvider, MatFormFieldProvider, FieldValuePreviewer
{
  private defaultErrorStateMatcher = inject(ErrorStateMatcher);

  matFormField = viewChild(MatFormField);

  multiple = input(false);
  control = input.required<FormControl>();
  label = input<string>('');
  options = input.required<SelectOption<T>[]>();
  errorStateMatcher = input<InstanceType<typeof MatSelect>['errorStateMatcher']>(
    this.defaultErrorStateMatcher,
  );
  customError = input<string | null>(null);
  customHint = input<string | null>(null);
  syntheticMonitorId = input<string | null>(null);
  optionSyntheticMonitorIdPrefix = input<string | null>(null);
  subscriptSizing = input<InstanceType<typeof MatFormField>['subscriptSizing']>('fixed');
  panelWidth = input<InstanceType<typeof MatSelect>['panelWidth']>('auto');
  nullOption = input<boolean>(true);
  compareWith = input<InstanceType<typeof MatSelect>['compareWith']>((a, b) => a === b);
  placeholder = input<string>('');
  floatLabel = input<FloatLabelType>('auto');

  selectionChange = output<MatSelectChange>();

  getError() {
    return getErrorMessage(this.control());
  }

  preview = signal<string | null>(null);

  toOptionSyntheticMonitorId(option: SelectOption<T>) {
    const prefix = this.optionSyntheticMonitorIdPrefix();
    return prefix ? prefix + option.value : null;
  }
}
