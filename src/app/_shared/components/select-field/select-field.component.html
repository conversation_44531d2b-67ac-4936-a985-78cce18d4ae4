<mat-form-field
  class="rkt-FormField"
  [class.pointer-events-none]="preview()"
  color="accent"
  [floatLabel]="!!preview() ? 'always' : floatLabel()"
  [subscriptSizing]="subscriptSizing()"
>
  <mat-label>{{ label() }}</mat-label>
  <mat-select
    class="rkt-Input"
    [formControl]="control()"
    [class.!h-0]="preview()"
    [class.!w-0]="preview()"
    [class.!opacity-0]="preview()"
    [class.!absolute]="preview()"
    [errorStateMatcher]="errorStateMatcher()"
    [attr.data-synthetic-monitor-id]="syntheticMonitorId()"
    [compareWith]="compareWith()"
    [multiple]="multiple()"
    (selectionChange)="selectionChange.emit($event)"
    [placeholder]="placeholder()"
  >
    @if (nullOption()) {
      <mat-option [value]="null">None</mat-option>
    }
    @for (option of options(); track option.value) {
      <mat-option
        [value]="option.value"
        [attr.data-synthetic-monitor-id]="toOptionSyntheticMonitorId(option)"
        >{{ option.display }}</mat-option
      >
    }
  </mat-select>
  @if (preview(); as preview) {
    <mat-select class="rkt-Input" [value]="preview">
      <mat-option [value]="preview">{{ preview }}</mat-option>
    </mat-select>
  }

  <div class="flex items-center" matSuffix>
    <ng-content select="[form-field-suffix]"></ng-content>
  </div>

  @if (customHint()) {
    <mat-hint>{{ customHint() }}</mat-hint>
  }
  <mat-error>{{ customError() ?? getError() }}</mat-error>
</mat-form-field>
