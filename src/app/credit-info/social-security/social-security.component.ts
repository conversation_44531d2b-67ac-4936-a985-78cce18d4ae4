import { CommonModule } from '@angular/common';
import { Component, computed, effect, inject, input, signal } from '@angular/core';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { NgxMaskDirective } from 'ngx-mask';
import { InputSectionDirective } from '../../form-nav/nav-section/input-section.directive';
import { CreditService } from '../../services/credit/credit.service';
import { LoanEditingState } from '../../services/entity-state/loan-state/loan-editing-state.service';
import { FormInput } from '../../services/form-nav/form-nav-input.service';
import { SsnErrorStateMatcher } from '../../util/error-state-matchers/custom-ssn-error-state-matcher';

@Component({
  selector: 'app-social-security',
  standalone: true,
  imports: [
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    CommonModule,
    InputSectionDirective,
    MatIconModule,
    MatButtonModule,
    NgxMaskDirective,
  ],
  templateUrl: './social-security.component.html',
  styleUrl: './social-security.component.scss',
})
export class SocialSecurityComponent {
  creditService = inject(CreditService);
  isDisabled = inject(LoanEditingState).isLoanEditingDisabled;

  constructor() {
    effect(() => {
      if (this.creditService.isPullingCredit() || this.isDisabled()) {
        this.ssnControl().disable();
      } else {
        this.ssnControl().enable();
      }
    });
  }

  ssnControl = input.required<FormControl>();
  label = input.required<string>();
  errorMessage = input.required<string>();
  ssnErrorStateMatcher = new SsnErrorStateMatcher();

  readonly FormInput = FormInput;
  ssnHidden = signal(false);
  maskActive = computed(() => this.ssnHidden() || this.isDisabled());

  toggleHidden() {
    this.ssnHidden.update((hidden) => !hidden);
  }

  setHidden(hidden: boolean) {
    this.ssnHidden.set(hidden);
  }
}
