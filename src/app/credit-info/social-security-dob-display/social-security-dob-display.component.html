<div class="row">
  <app-formatted-date-input
    [control]="clientForm().controls.personalInformation.controls.dateOfBirth"
    [maximumDate]="today"
    label="Date of Birth"
    appNavInput
    [inputSection]="FormInput.DateOfBirth"
    [attr.data-testid]="clientIndex() ? 'date-of-birth-input-' + clientIndex() : null"
  />
  <app-social-security-group
    [attr.data-testid]="clientIndex() ? 'ssn-input-group-' + clientIndex() : null"
    [clientForm]="clientForm()"
    class="flex-1 min-w-0"
  />
</div>
