import { Component, input } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { InputSectionDirective } from '../../form-nav/nav-section/input-section.directive';
import { FormattedDateInputComponent } from '../../question-input/formatted-date-input/formatted-date-input.component';
import { ClientControls } from '../../services/entity-state/client-state/form-types';
import { FormInput } from '../../services/form-nav/form-nav-input.service';
import { FormSection } from '../../services/form-nav/form-nav-section.service';
import { SocialSecurityGroupComponent } from '../social-security-group/social-security-group.component';

@Component({
  selector: 'app-social-security-dob-display',
  standalone: true,
  imports: [FormattedDateInputComponent, SocialSecurityGroupComponent, InputSectionDirective],
  templateUrl: './social-security-dob-display.component.html',
  styleUrl: './social-security-dob-display.component.scss',
})
export class SocialSecurityDobDisplayComponent {
  clientForm = input.required<FormGroup<ClientControls>>();
  inputSection = input<FormSection>();
  readonly FormInput = FormInput;
  public today = new Date();
  readonly clientIndex = input<number>();
}
