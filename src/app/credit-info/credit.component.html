<app-form-section
  titleText="Credit"
  icon="rl-crm"
  [isCollapsible]="false"
  [formSection]="FormSection.Credit"
  data-testid="credit-form-section"
>
  @if (!clientStateService.isFetching()) {
    @for (
      clientFormEntry of clientFormService.sortedEntityForms();
      track clientFormEntry[0];
      let i = $index
    ) {
      <div [attr.data-testid]="'client-credit-info-container-' + i">
        <div class="client-name-container">
          <span class="rkt-Label-16 rkt-FontWeight--700" [attr.data-testid]="'client-name-' + i">{{
            clientFormEntry[1].value | clientName
          }}</span>
          <rkt-tag-enterprise
            *ngIf="clientFormEntry[1].value.isPrimaryBorrower"
            variant="info"
            iconPosition="left"
            iconName="person_outline-outlined"
            [attr.data-testid]="'primary-client-tag-' + i"
            >Primary Client</rkt-tag-enterprise
          >
        </div>
        <app-social-security-dob-display [clientForm]="clientFormEntry[1]" [clientIndex]="i" />
        @if (isRelocationLead()) {
          <p class="rkt-Label-14 rkt-FontWeight--500">Has International Credit</p>
          <mat-form-field
            class="rkt-FormField"
            color="accent"
            [attr.data-testid]="'international-credit-field-' + i"
          >
            <mat-label>International Credit</mat-label>
            <mat-select
              appNavInput
              class="rkt-Input"
              [attr.data-testid]="'international-credit-select-' + i"
              [inputSection]="ProductMilestones.InternationalCredit"
              [formControl]="internationalCreditControl"
            >
              <mat-option [attr.data-testid]="'international-credit-none-' + i" [value]="null"
                >None</mat-option
              >
              <mat-option [attr.data-testid]="'international-credit-yes-' + i" [value]="true"
                >Yes</mat-option
              >
              <mat-option [attr.data-testid]="'international-credit-no-' + i" [value]="false"
                >No</mat-option
              >
            </mat-select>
          </mat-form-field>
        }
      </div>
    }
  } @else {
    <app-credit-skeleton data-testid="credit-skeleton" />
  }
</app-form-section>
