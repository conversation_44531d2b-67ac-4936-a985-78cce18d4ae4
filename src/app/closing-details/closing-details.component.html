<app-form-section titleText="Closing Details" [isCollapsible]="false" icon="today-outlined">
  @if (productStateService.isFetching()) {
    <div class="skeleton-container">
      <div class="grid grid-cols-5 gap-4 items-center">
        <span rktSkeleton class="form-field-skeleton mb-5 col-span-2"></span>
        <span rktSkeleton class="form-field-skeleton mb-5 col-span-2"></span>
        <span rktSkeleton class="form-field-skeleton mb-5 col-span-2"></span>
      </div>
    </div>
  } @else {
    <div class="grid grid-cols-5 gap-4">
      <app-formatted-date-input
        class="col-span-2"
        label="Anticipated Closing Date"
        [control]="anticipatedClosingDateControl"
        [minimumDate]="today"
      />
      <app-select-field
        class="col-span-2"
        label="Commitment Period (Days)"
        [control]="commitmentPeriodControl"
        [options]="periodOptions()"
        [customHint]="hint()"
      />
      @if (canRollInClosingCosts()) {
        <app-toggle-field
          class="col-span-3"
          [control]="rollInClosingCostsControl"
          label="Roll in FF/UFMIP"
        />
      }
    </div>
  }
</app-form-section>
