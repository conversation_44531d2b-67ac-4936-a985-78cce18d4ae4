import { Component, Signal, computed, inject } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { ReactiveFormsModule } from '@angular/forms';
import { provideNativeDateAdapter } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatIconModule } from '@angular/material/icon';
import { LockStatus, MortgageType } from '@rocket-logic/rl-xp-bff-models';
import { RktSkeletonModule } from '@rocketcentral/rocket-design-system-angular';
import { SelectFieldComponent } from '../_shared/components/select-field/select-field.component';
import { ToggleFieldComponent } from '../_shared/components/toggle-field/toggle-field.component';
import { FormSectionComponent } from '../form-section/form-section.component';
import { FormattedDateInputComponent } from '../question-input/formatted-date-input/formatted-date-input.component';
import { CommitmentPeriodService } from '../services/commitment-period/commitment-period.service';
import { LoanEditingState } from '../services/entity-state/loan-state/loan-editing-state.service';
import { LoanFormService } from '../services/entity-state/loan-state/loan-form.service';
import { ProductFormService } from '../services/entity-state/product-state/product-form.service';
import { ProductStateService } from '../services/entity-state/product-state/product-state.service';
import { RateLockStateService } from '../services/entity-state/rate-lock-state/rate-lock-state.service';
import { SelectOption } from '../type-ahead-select/type-ahead-select.component';
import { toValueChangesSignal } from '../util/value-changes-signal';

const GOVY_PRODUCT_GROUPS = [MortgageType.FHA, MortgageType.VA, MortgageType.USDARuralDevelopment];

@Component({
  selector: 'app-closing-details',
  standalone: true,
  imports: [
    MatIconModule,
    ToggleFieldComponent,
    FormattedDateInputComponent,
    MatDatepickerModule,
    ReactiveFormsModule,
    SelectFieldComponent,
    FormSectionComponent,
    RktSkeletonModule,
  ],
  providers: [provideNativeDateAdapter()],
  templateUrl: './closing-details.component.html',
  styleUrl: './closing-details.component.scss',
})
export class ClosingDetailsComponent {
  private readonly commitmentPeriodService = inject(CommitmentPeriodService);
  private readonly rateLockService = inject(RateLockStateService);
  private readonly loanFormService = inject(LoanFormService);
  readonly productStateService = inject(ProductStateService);
  readonly productFormService = inject(ProductFormService);
  private readonly loanEditStateService = inject(LoanEditingState);
  readonly productForm = this.productFormService.productForm;
  readonly loanForm = this.loanFormService.loanForm;

  constructor() {
    this.rateLockService.nonNullableData$
      .pipe(takeUntilDestroyed())
      .subscribe((rateLockDetails) => {
        if (rateLockDetails.lockStatus === LockStatus.Locked) {
          this.productForm?.controls.closingDetails.controls.commitmentPeriodInDays.disable();
        } else {
          if (!this.loanEditStateService.isLoanEditingDisabled())
            this.productForm?.controls.closingDetails.controls.commitmentPeriodInDays.enable();
        }
      });
  }

  readonly today = new Date();
  readonly anticipatedClosingDateControl = this.loanForm.controls.anticipatedClosingDate;

  readonly commitmentPeriodControl =
    this.productForm?.controls.closingDetails.controls.commitmentPeriodInDays;

  protected readonly productGroup = toValueChangesSignal<MortgageType>(
    this.productForm,
    'productGroup',
  );
  protected readonly canRollInClosingCosts = computed(() =>
    GOVY_PRODUCT_GROUPS.includes(this.productGroup()),
  );

  readonly rollInClosingCostsControl =
    this.productForm?.controls.closingDetails.controls.includeFundingFeesInLoanAmount;

  hint = computed(
    () => `Recommended: ${this.commitmentPeriodService.commitmentPeriods()?.recommended} days`,
  );

  periodOptions: Signal<SelectOption<number>[]> = computed(() =>
    this.commitmentPeriodService.periodLength().map((value) => ({
      value,
      display: value.toString(),
    })),
  );
}
