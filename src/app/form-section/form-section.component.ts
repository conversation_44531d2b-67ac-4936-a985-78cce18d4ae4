import { animate, state, style, transition, trigger } from '@angular/animations';
import { CommonModule } from '@angular/common';
import { Component, computed, ElementRef, inject, input, signal } from '@angular/core';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { FormNavSectionService, FormSection } from '../services/form-nav/form-nav-section.service';
import { SectionToggleButtonComponent } from './section-toggle-button/section-toggle-button.component';
export const SECTION_TOGGLE_DURATION = 350;
@Component({
  selector: 'app-form-section',
  standalone: true,
  imports: [MatCardModule, MatIconModule, SectionToggleButtonComponent, CommonModule],
  templateUrl: './form-section.component.html',
  styleUrl: './form-section.component.scss',
  animations: [
    trigger('expandCollapse', [
      state('expanded', style({ height: '*', overflow: 'hidden' })),
      state('collapsed', style({ height: '0px', overflow: 'hidden' })),
      transition('expanded <=> collapsed', [animate('{{ duration }}ms ease-in-out')], {
        params: { duration: SECTION_TOGGLE_DURATION },
      }),
    ]),
  ],
})
export class FormSectionComponent {
  elementRef = inject(ElementRef);
  formNavSectionService = inject(FormNavSectionService, { optional: true });

  titleText = input.required<string>();
  icon = input<string>();
  isCollapsible = input<boolean>(true);
  sectionToggle = signal<boolean>(true);
  formSection = input<FormSection>();

  isActiveSection = computed(() =>
    this.formNavSectionService
      ? this.formNavSectionService.activeSection() === this.formSection()
      : false,
  );

  primaryExpandCollapseState = computed(() => {
    if (!this.isCollapsible()) {
      return null;
    }

    return {
      value: this.sectionToggle() ? 'expanded' : 'collapsed',
      params: { duration: SECTION_TOGGLE_DURATION },
    };
  });

  summaryExpandCollapseState = computed(() => {
    if (!this.isCollapsible()) {
      return null;
    }

    return {
      value: !this.sectionToggle() ? 'expanded' : 'collapsed',
      params: { duration: SECTION_TOGGLE_DURATION },
    };
  });

  setSectionOpenState(isOpen: boolean) {
    this.sectionToggle.set(isOpen);
  }

  ngOnInit(): void {
    const formSection = this.formSection();
    if (formSection) {
      this.formNavSectionService?.registerSection(
        formSection,
        this.elementRef.nativeElement,
        this.onSectionActivateHook(),
      );
    }
  }

  ngOnDestroy(): void {
    this.formNavSectionService?.deregisterSection(this.elementRef.nativeElement);
  }

  onSectionActivateHook() {
    return () => this.setSectionOpenState(true);
  }
}
