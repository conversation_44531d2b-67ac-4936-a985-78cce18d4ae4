import { ComponentFixture, TestBed } from '@angular/core/testing';

import { signal } from '@angular/core';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { MockBuilder } from 'ng-mocks';
import { FormNavSectionService } from '../services/form-nav/form-nav-section.service';
import { FormSectionComponent } from './form-section.component';

describe('FormSectionComponent', () => {
  let component: FormSectionComponent;
  let fixture: ComponentFixture<FormSectionComponent>;

  beforeEach(() =>
    MockBuilder(FormSectionComponent)
      .mock(FormNavSectionService, {
        activeSection: signal(null),
      })
      .keep(NoopAnimationsModule),
  );
  beforeEach(() => {
    fixture = TestBed.createComponent(FormSectionComponent);
    component = fixture.componentInstance;
    fixture.componentRef.setInput('titleText', 'Test Title');
    fixture.componentRef.setInput('icon', '');
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
