<div id="container">
  <app-formatted-number-input
    id="amount"
    [allowNegative]="allowNegative()"
    [control]="control()"
    [label]="label()"
    [prefix]="prefix()"
    appNavInput
    [shouldRegister]="controlType() !== null"
    [inputSection]="controlType()"
    [isLoading]="isLoading()"
    [subscriptSizing]="subscriptSizing()"
  />

  <app-formatted-number-input
    id="percentage"
    [allowNegative]="allowNegative()"
    [control]="percentageControl"
    suffix="%"
    [maxLength]="3"
    [ariaLabel]="label()"
    [subscriptSizing]="subscriptSizing()"
  />
</div>
