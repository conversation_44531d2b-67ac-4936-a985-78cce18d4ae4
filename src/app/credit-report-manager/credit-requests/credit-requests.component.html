@if (creditService.creditTransferSuccessful()) {
  <rkt-alert-enterprise class="transfer-credit-alert" variant="success">
    <p class="rkt-Alert__text">
      We found and attached an active, transferable credit report for your client(s).
    </p>
  </rkt-alert-enterprise>
} @else if (creditService.creditTransferSuccessful() === false) {
  <rkt-alert-enterprise class="transfer-credit-alert" variant="warn">
    <p class="rkt-Alert__text">
      If a credit report exists, double check the name and address information match the existing
      report before attempting to transfer again. If no credit report exists, pull a new report.
    </p>
  </rkt-alert-enterprise>
} @else if (completedOrFailedCreditOrders().length > 0) {
  @for (failureDetail of creditOrderFailureDetails(); track failureDetail) {
    <rkt-alert-enterprise class="transfer-credit-alert" variant="warn">
      {{ failureDetail }}
    </rkt-alert-enterprise>
  }
}

<div
  class="report-container"
  [class.no-reports]="activeCreditReports().length === 0 || isFetchingReports()"
>
  @if (isFetchingReports()) {
    <mat-spinner
      class="rkt-ProgressSpinner rkt-ProgressSpinner--enterprise"
      diameter="90"
    ></mat-spinner>
  } @else if (activeCreditReports().length === 0) {
    <div class="no-credit-report">
      <mat-icon
        [svgIcon]="darkModeService.isDarkMode() ? 'credit-score-dark' : 'credit-score'"
        class="rkt-Icon"
      />
      <span class="rkt-Label-14 rkt-Color--gray-600">No Active Credit Reports Available</span>
    </div>
  } @else {
    @for (creditReport of activeCreditReports(); track creditReport) {
      <app-credit-report [creditReportData]="creditReport" />
    }
  }
</div>
<mat-accordion class="request-credit-accordion">
  <mat-expansion-panel class="rkt-AccordionPanel rkt-AccordionPanel--enterprise" [expanded]="true">
    <mat-expansion-panel-header>
      <mat-panel-title class="rkt-AccordionPanel__header-title"> Request Credit </mat-panel-title>
    </mat-expansion-panel-header>
    <div class="request-credit-container">
      <app-huge-radio-group
        [splitAt]="undefined"
        [options]="selectedCreditClientService.clientOptions()"
        [formControl]="selectedCreditClientService.selectedClientsControl"
        [attr.data-synthetic-monitor-id]="'credit-request-client'"
      />
      @if (isFirstCreditPullForSelectedClients()) {
        <mat-checkbox
          class="rkt-Checkbox rkt-Checkbox--enterprise"
          [disabled]="
            !selectedCreditClientService.selectedClient() ||
            selectedCreditClientService.selectedClient()?.length === 0
          "
          (change)="clientConsentForCreditPull.set($event.checked)"
          [checked]="clientConsentForCreditPull()"
          [attr.data-synthetic-monitor-id]="'credit-request-consent'"
        >
          The client(s) consented to credit pull.
        </mat-checkbox>
      }
      <div class="action-container">
        <button
          class="rkt-Button rkt-Button--has-icon"
          mat-flat-button
          color="primary"
          [disabled]="shouldDisableCreditPull()"
          [class.rkt-Button--is-disabled]="shouldDisableCreditPull()"
          [class.rkt-Button--is-spinning]="creditPullLoading()"
          (click)="pullCredit()"
        >
          @if (creditPullLoading()) {
            <mat-icon class="rkt-Icon rkt-Spacing--mr8">
              <mat-spinner role="presentation" class="rkt-Spinner" diameter="20"></mat-spinner>
            </mat-icon>
          } @else {
            <mat-icon svgIcon="rl-crm" class="rkt-Icon"></mat-icon>
          }
          @if (creditService.isPullingCredit()) {
            Requesting Credit Report
          } @else if (isFetchingPullType()) {
            Loading Credit Info
          } @else if (pullTypeFailed()) {
            Credit Pull Unavailable
          } @else {
            Pull {{ pullType() | uppercase }} Credit
          }
        </button>
        <button
          class="rkt-Button rkt-Button--secondary rkt-Button--has-icon"
          mat-stroked-button
          color="accent"
          [disabled]="shouldDisableCreditTransfer()"
          [class.rkt-Button--is-disabled]="shouldDisableCreditTransfer()"
          [class.rkt-Button--is-spinning]="transferCreditLoading()"
          (click)="transferCredit()"
        >
          @if (transferCreditLoading()) {
            <mat-icon class="rkt-Icon rkt-Spacing--mr8">
              <mat-spinner role="presentation" class="rkt-Spinner" diameter="20"></mat-spinner>
            </mat-icon>
          }
          @if (creditService.isTransferringCredit()) {
            Checking For Transferable Credit
          } @else {
            Check For Transferable Credit
          }
        </button>
        <div class="help-text-container">
          <mat-icon svgIcon="info-outlined" class="rkt-Color--blue-500"></mat-icon>
          <span class="rkt-Caption-10"
            >Have you reviewed the Pathfinder page on when to re-pull credit for a loan?</span
          >
        </div>
      </div>
    </div>
  </mat-expansion-panel>
</mat-accordion>
