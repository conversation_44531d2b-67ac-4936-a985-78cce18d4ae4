import { CommonModule, DatePipe } from '@angular/common';
import { Component, Signal, computed, inject } from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatTableModule } from '@angular/material/table';
import { ClientCredit, CreditReportOrder, PersonalInfo } from '@rocket-logic/rl-xp-bff-models';
import {
  CreditReport,
  CreditReportOrderStatus,
  CreditReportOrderType,
} from '@rocket-logic/rl-xp-bff-models/dist/credit';
import { RktLinkEnterpriseModule } from '@rocketcentral/rocket-design-system-enterprise-angular';
import { combineLatest, map } from 'rxjs';
import { CreditService } from '../../services/credit/credit.service';
import { ClientStateService } from '../../services/entity-state/client-state/client-state.service';
import { ClientNamePipe } from '../../util/client-name.pipe';
import { CreditReportPipe } from '../../util/credit-report.pipe';
import { getCreditReportUrl } from '../../util/get-url';

@Component({
  selector: 'app-credit-history',
  standalone: true,
  templateUrl: './credit-history.component.html',
  styleUrl: './credit-history.component.scss',
  imports: [
    MatExpansionModule,
    MatTableModule,
    DatePipe,
    RktLinkEnterpriseModule,
    CreditReportPipe,
    MatProgressSpinnerModule,
    ClientNamePipe,
    CommonModule,
  ],
})
export class CreditHistoryComponent {
  creditService = inject(CreditService);
  clientService = inject(ClientStateService);

  readonly reportColumns = ['date', 'report'];
  readonly orderColumns = ['date', 'clients', 'type', 'status'];
  readonly completed = 'Completed';
  readonly failed = 'Failed';

  clients = computed(() => this.clientService.stateValues());

  creditReports: Signal<CreditReport[]> = toSignal(
    this.creditService.creditReports$.pipe(
      map((state) => state.data ?? []),
      map((reports) =>
        reports.map((report: CreditReport) => {
          return { ...report, url: getCreditReportUrl(report) };
        }),
      ),
    ),
    { initialValue: [] },
  );

  inactiveReports = computed(() =>
    this.creditReports().filter((report: CreditReport) => report.isActive === false),
  );

  isFetchingReports = toSignal(
    this.creditService.creditReports$.pipe(map((state) => state.fetching === true)),
  );

  creditOrders: Signal<
    {
      date: string | undefined;
      firstIssuedOn: string | undefined;
      clientInfo: {
        personalInformation: PersonalInfo | undefined;
      }[];
      type: string;
      status: string;
    }[]
  > = toSignal(
    combineLatest([this.creditService.creditOrders$, this.clientService.state$]).pipe(
      map(([state]) => state.data),
      map(
        (orders) =>
          orders
            ?.filter((order) => order.orderStatus !== CreditReportOrderStatus.Processing)
            .map((order) => {
              return {
                date: order.lastUpdatedOn,
                firstIssuedOn: this.creditReports().find(
                  (report: CreditReport) => report.reportId === order.reportId,
                )?.firstIssuedOn,
                clientInfo: this.getClientInfo(order.clients),
                type: this.translateOrderType(order),
                status:
                  order.orderStatus === CreditReportOrderStatus.Complete
                    ? this.completed
                    : this.failed,
              };
            }) ?? [],
      ),
    ),
    { initialValue: [] },
  );
  isFetchingOrders = toSignal(
    this.creditService.creditOrders$.pipe(map((state) => state.fetching === true)),
  );

  public getOrderStatusClass(orderStatus: string) {
    return orderStatus === this.completed
      ? this.completed.toLocaleLowerCase()
      : this.failed.toLocaleLowerCase();
  }

  private getClientInfo(clients: ClientCredit[]) {
    return clients.map((creditClient) => {
      const clientInfo = this.creditService
        .clients()
        ?.find((client) => client.id === creditClient.clientId)?.personalInformation;
      return { personalInformation: clientInfo };
    });
  }

  private translateOrderType(creditOrder: CreditReportOrder): string {
    return creditOrder.orderType === CreditReportOrderType.Pull
      ? creditOrder.creditPullType
      : creditOrder.orderType;
  }
}
