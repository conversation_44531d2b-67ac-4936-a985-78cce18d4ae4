import { Directive, ElementRef, Input, OnInit, inject } from '@angular/core';
import { PricingNavigationService } from '../services/pricing-navigation/pricing-navigation.service';

/**
 * Directive to enhance error messages with clickable links that navigate to pricing fields
 * 
 * Usage:
 * <span appPricingFieldLink="purchasePrice">Purchase Price is required</span>
 * <li appPricingFieldLink="credits.manualLenderPaidCredit">Lender Paid Credit must be positive</li>
 */
@Directive({
  selector: '[appPricingFieldLink]',
  standalone: true
})
export class PricingFieldLinkDirective implements OnInit {
  @Input('appPricingFieldLink') fieldPath!: string;
  @Input() linkText?: string;
  @Input() highlightField = true;
  @Input() focusField = true;

  private readonly elementRef = inject(ElementRef<HTMLElement>);
  private readonly pricingNavService = inject(PricingNavigationService);

  ngOnInit(): void {
    if (!this.fieldPath) {
      console.warn('PricingFieldLinkDirective: fieldPath is required');
      return;
    }

    this.enhanceElement();
  }

  private enhanceElement(): void {
    const element = this.elementRef.nativeElement;
    const anchor = this.pricingNavService.getAnchorInfo(this.fieldPath);
    
    if (!anchor) {
      console.warn(`PricingFieldLinkDirective: No anchor found for field path: ${this.fieldPath}`);
      return;
    }

    // Create clickable link
    const linkElement = this.createLinkElement(anchor);
    
    // Replace the text content with the link
    if (this.linkText) {
      // If specific link text is provided, replace only that text
      this.replaceTextWithLink(element, this.linkText, linkElement);
    } else {
      // If no specific text, make the entire element clickable
      this.makeElementClickable(element, anchor);
    }
  }

  private createLinkElement(anchor: { label: string }): HTMLElement {
    const link = document.createElement('button');
    link.textContent = this.linkText || anchor.label;
    link.className = 'pricing-field-link rkt-Link--inline-12';
    link.style.cssText = `
      background: none;
      border: none;
      padding: 0;
      color: inherit;
      text-decoration: underline;
      cursor: pointer;
      font: inherit;
    `;
    
    link.addEventListener('click', (event) => {
      event.preventDefault();
      event.stopPropagation();
      this.navigateToField();
    });

    return link;
  }

  private replaceTextWithLink(element: HTMLElement, textToReplace: string, linkElement: HTMLElement): void {
    const walker = document.createTreeWalker(
      element,
      NodeFilter.SHOW_TEXT,
      null
    );

    const textNodes: Text[] = [];
    let node;
    while (node = walker.nextNode()) {
      textNodes.push(node as Text);
    }

    for (const textNode of textNodes) {
      const content = textNode.textContent || '';
      if (content.includes(textToReplace)) {
        const parts = content.split(textToReplace);
        const parent = textNode.parentNode!;
        
        // Insert text before
        if (parts[0]) {
          parent.insertBefore(document.createTextNode(parts[0]), textNode);
        }
        
        // Insert link
        parent.insertBefore(linkElement.cloneNode(true), textNode);
        
        // Insert text after
        if (parts[1]) {
          parent.insertBefore(document.createTextNode(parts[1]), textNode);
        }
        
        // Remove original text node
        parent.removeChild(textNode);
        break;
      }
    }
  }

  private makeElementClickable(element: HTMLElement, anchor: { label: string }): void {
    element.style.cursor = 'pointer';
    element.style.textDecoration = 'underline';
    element.title = `Click to navigate to ${anchor.label}`;
    
    element.addEventListener('click', (event) => {
      event.preventDefault();
      event.stopPropagation();
      this.navigateToField();
    });
  }

  private navigateToField(): void {
    const success = this.pricingNavService.navigateToField(this.fieldPath, {
      focus: this.focusField,
      highlight: this.highlightField,
      scrollBehavior: 'smooth'
    });

    if (!success) {
      console.warn(`Failed to navigate to field: ${this.fieldPath}`);
    }
  }
}
