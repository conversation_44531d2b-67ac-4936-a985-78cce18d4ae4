import { Directive, ElementRef, inject, Input, OnInit } from '@angular/core';
import { PricingErrorNavigationService } from '../services/pricing-navigation/pricing-error-navigation.service';

/**
 * Directive that automatically scans text content for pricing field names
 * and makes them clickable navigation links
 *
 * Usage:
 * <div appAutoPricingLinks>Purchase Price is required</div>
 * <ul appAutoPricingLinks>
 *   <li>LTV cannot exceed 100%</li>
 *   <li>Seller Concessions must be positive</li>
 * </ul>
 */
@Directive({
  selector: '[appAutoPricingLinks]',
  standalone: true
})
export class AutoPricingLinksDirective implements OnInit {
  @Input() enableAutoPricingLinks = true;

  private readonly elementRef = inject(ElementRef<HTMLElement>);
  private readonly errorNavService = inject(PricingErrorNavigationService);

  // Field patterns to look for in text content
  private readonly fieldPatterns = [
    { pattern: /Purchase Price/gi, fieldPath: 'purchasePrice' },
    { pattern: /Base Loan Amount/gi, fieldPath: 'baseLoanAmount' },
    { pattern: /Rate.*Base Points/gi, fieldPath: 'baseRate' },
    { pattern: /Buydown Options?/gi, fieldPath: 'buydown' },
    { pattern: /Buydown.*Sources?/gi, fieldPath: 'buydownSources' },
    { pattern: /Opt-Out Reason/gi, fieldPath: 'lenderPaidBuydownOptOutReason' },
    { pattern: /Collected Discount Points/gi, fieldPath: 'collectedDiscountPoints' },
    { pattern: /Down Payment/gi, fieldPath: 'downPayment' },
    { pattern: /Lender Paid Credit/gi, fieldPath: 'credits.manualLenderPaidCredit' },
    { pattern: /Realtor Credits?/gi, fieldPath: 'credits.realtorCredits' },
    { pattern: /Seller Concessions?/gi, fieldPath: 'credits.sellerConcessions' },
    { pattern: /\bLTV\b/gi, fieldPath: 'qualificationDetails.ltv' },
    { pattern: /Insurance Amount/gi, fieldPath: 'insuranceAmount' },
    { pattern: /Escrow Waiver/gi, fieldPath: 'escrowWaiver' },
    { pattern: /Anticipated Closing Date/gi, fieldPath: 'anticipatedClosingDate' },
    { pattern: /Commitment Period/gi, fieldPath: 'commitmentPeriodInDays' },
    // Client fields
    { pattern: /First Name/gi, fieldPath: 'firstName' },
    { pattern: /Last Name/gi, fieldPath: 'lastName' },
    { pattern: /Middle Name/gi, fieldPath: 'middleName' },
  ];

  ngOnInit(): void {
    if (!this.enableAutoPricingLinks) {
      return;
    }

    this.enhanceTextContent();
  }

  private enhanceTextContent(): void {
    const element = this.elementRef.nativeElement;

    // Process all text nodes in the element
    this.processTextNodes(element);
  }

  private processTextNodes(element: HTMLElement): void {
    const walker = document.createTreeWalker(
      element,
      NodeFilter.SHOW_TEXT,
      {
        acceptNode: (node) => {
          // Skip nodes that are already inside links or buttons
          const parent = node.parentElement;
          if (parent && (parent.tagName === 'BUTTON' || parent.tagName === 'A')) {
            return NodeFilter.FILTER_REJECT;
          }
          return NodeFilter.FILTER_ACCEPT;
        }
      }
    );

    const textNodes: Text[] = [];
    let node;
    while (node = walker.nextNode()) {
      textNodes.push(node as Text);
    }

    // Process each text node
    textNodes.forEach(textNode => this.processTextNode(textNode));
  }

  private processTextNode(textNode: Text): void {
    const originalText = textNode.textContent || '';
    let modifiedText = originalText;
    let hasChanges = false;

    // Check each field pattern
    for (const { pattern, fieldPath } of this.fieldPatterns) {
      if (!this.errorNavService.canNavigateToField(fieldPath)) {
        continue;
      }

      const matches = originalText.match(pattern);
      if (matches) {
        for (const match of matches) {
          if (modifiedText.includes(match)) {
            const linkHtml = this.createLinkHtml(match, fieldPath);
            modifiedText = modifiedText.replace(match, linkHtml);
            hasChanges = true;
          }
        }
      }
    }

    // If we made changes, replace the text node with HTML
    if (hasChanges) {
      const wrapper = document.createElement('span');
      wrapper.innerHTML = modifiedText;

      // Replace the text node with the new content
      const parent = textNode.parentNode;
      if (parent) {
        // Insert all child nodes from wrapper
        while (wrapper.firstChild) {
          parent.insertBefore(wrapper.firstChild, textNode);
        }
        parent.removeChild(textNode);
      }
    }
  }

  private createLinkHtml(text: string, fieldPath: string): string {
    const fieldLabel = this.errorNavService.getFieldLabel(fieldPath) || text;

    return `<button
      class="pricing-field-link rkt-Link--inline-12"
      style="background: none; border: none; padding: 0; color: inherit; text-decoration: underline; cursor: pointer; font: inherit; font-weight: 500;"
      onclick="window.navigateToPricingField('${fieldPath}')"
      title="Click to navigate to ${fieldLabel}"
    >${text}</button>`;
  }
}
