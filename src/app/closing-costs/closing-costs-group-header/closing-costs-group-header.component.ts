import { CurrencyPipe } from '@angular/common';
import { Component, input } from '@angular/core';

@Component({
  selector: 'app-closing-costs-group-header',
  standalone: true,
  imports: [CurrencyPipe],
  templateUrl: './closing-costs-group-header.component.html',
  styleUrl: './closing-costs-group-header.component.scss',
})
export class ClosingCostsGroupHeaderComponent {
  readonly name = input.required<string>();
  readonly total = input.required<number>();
  readonly isBold = input<boolean>(false);
}
