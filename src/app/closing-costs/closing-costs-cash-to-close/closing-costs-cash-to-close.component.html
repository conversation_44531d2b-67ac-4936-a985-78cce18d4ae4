<div class="flex flex-col">
  <p class="rkt-Tracked-12 section-title" style="padding-top: 24px">Cash To Close</p>
  <mat-accordion multi>
    @if (totalClosingCosts(); as groupD2I) {
      <mat-expansion-panel
        #panelTotalClosingCosts
        class="rkt-AccordionPanel"
        hideToggle="true"
        expanded="false"
        (click)="panelTotalClosingCosts.close()"
      >
        <mat-expansion-panel-header>
          <app-closing-costs-group-header name="Total Closing Costs (D + I)" [total]="groupD2I" />
        </mat-expansion-panel-header>
      </mat-expansion-panel>
    }

    @if (closingCostsFinanced(); as closingCosts) {
      <mat-expansion-panel
        #panelClosingCostsFinanced
        class="rkt-AccordionPanel"
        hideToggle="true"
        expanded="false"
        (click)="panelClosingCostsFinanced.close()"
      >
        <mat-expansion-panel-header>
          <app-closing-costs-group-header name="Closing Costs Financed" [total]="closingCosts" />
        </mat-expansion-panel-header>
      </mat-expansion-panel>
    }

    @if (downPayment(); as dp) {
      <mat-expansion-panel
        #panelDownPayment
        class="rkt-AccordionPanel"
        hideToggle="true"
        expanded="false"
        (click)="panelDownPayment.close()"
      >
        <mat-expansion-panel-header>
          <app-closing-costs-group-header name="Down Payment" [total]="dp" />
        </mat-expansion-panel-header>
      </mat-expansion-panel>
    }

    @if (deposit(); as d) {
      <mat-expansion-panel
        #panelDeposit
        class="rkt-AccordionPanel"
        hideToggle="true"
        expanded="false"
        (click)="panelDeposit.close()"
      >
        <mat-expansion-panel-header>
          <app-closing-costs-group-header name="Deposit (EMD)" [total]="d" />
        </mat-expansion-panel-header>
      </mat-expansion-panel>
    }

    @if (fundsForBorrower(); as funds) {
      <mat-expansion-panel
        #panelFundsForBorrower
        class="rkt-AccordionPanel"
        hideToggle="true"
        expanded="false"
        (click)="panelFundsForBorrower.close()"
      >
        <mat-expansion-panel-header>
          <app-closing-costs-group-header name="Funds For Borrower" [total]="funds" />
        </mat-expansion-panel-header>
      </mat-expansion-panel>
    }

    @if (sellerCredits(); as sc) {
      <mat-expansion-panel
        #panelSellerCredits
        class="rkt-AccordionPanel"
        hideToggle="true"
        expanded="false"
        (click)="panelSellerCredits.close()"
      >
        <mat-expansion-panel-header>
          <app-closing-costs-group-header name="Seller Credits" [total]="sc" />
        </mat-expansion-panel-header>
      </mat-expansion-panel>
    }

    @if (adjustmentsAndOtherCredits(); as adjustments) {
      <mat-expansion-panel
        #panelGroupAdjustments
        class="rkt-AccordionPanel"
        [hideToggle]="(adjustments?.items?.length ?? 0) < 1"
      >
        <mat-expansion-panel-header>
          <app-closing-costs-group-header
            name="Adjustments And Other Credits"
            [total]="adjustments?.total ?? 0"
          />
        </mat-expansion-panel-header>

        <app-closing-costs-group-content
          class="rkt-AccordionPanel__content"
          [items]="adjustments?.items"
        />
      </mat-expansion-panel>
    }

    @if (estimatedCash(); as cash) {
      <mat-expansion-panel
        #panelEstimatedCash
        class="rkt-AccordionPanel highlight"
        hideToggle="true"
        expanded="false"
        (click)="panelEstimatedCash.close()"
      >
        <mat-expansion-panel-header>
          <app-closing-costs-group-header
            [isBold]="true"
            [name]="estimatedCashLabel() ?? 'Estimated Cash'"
            [total]="cash"
          />
        </mat-expansion-panel-header>
      </mat-expansion-panel>
    }
  </mat-accordion>
</div>
