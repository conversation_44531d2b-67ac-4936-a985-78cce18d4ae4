import { Component, input } from '@angular/core';
import { MatExpansionModule } from '@angular/material/expansion';
import { DetailsOfTransactionGroup } from '@rocket-logic/rl-xp-bff-models';
import { ClosingCostGroup } from '@rocket-logic/rocket-logic-api-models/solutions';
import { ClosingCostsGroupContentComponent } from '../closing-costs-group-content/closing-costs-group-content.component';
import { ClosingCostsGroupHeaderComponent } from '../closing-costs-group-header/closing-costs-group-header.component';

@Component({
  selector: 'app-closing-costs-cash-to-close',
  standalone: true,
  imports: [
    MatExpansionModule,
    ClosingCostsGroupContentComponent,
    ClosingCostsGroupHeaderComponent,
  ],
  templateUrl: './closing-costs-cash-to-close.component.html',
  styleUrls: ['./closing-costs-cash-to-close.component.scss', '../mat-expansion.scss'],
})
export class ClosingCostsCashToCloseComponent {
  readonly totalClosingCosts = input.required<number | undefined>();
  readonly closingCostsFinanced = input.required<number | undefined>();
  readonly downPayment = input.required<number | undefined>();
  readonly deposit = input.required<number | undefined>();
  readonly fundsForBorrower = input.required<number | undefined>();
  readonly sellerCredits = input.required<number | undefined>();
  readonly adjustmentsAndOtherCredits = input.required<
    ClosingCostGroup | DetailsOfTransactionGroup | undefined
  >();
  readonly estimatedCash = input.required<number | undefined>();
  readonly estimatedCashLabel = input.required<string | undefined>();
}
