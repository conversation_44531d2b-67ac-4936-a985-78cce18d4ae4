:host {
  ::ng-deep app-popout {
    background-color: var(--mat-sidenav-content-background-color);
    mat-card {
      background-color: var(--mat-sidenav-content-background-color);
      div.rkt-TableContainer,
      h2 {
        background-color: var(--mat-sidenav-content-background-color);
      }
    }

    mat-button-toggle.mat-button-toggle-checked {
      background-color: var(--rlxp-red-600);
      color: #fff;
    }
  }

  section {
    background-color: var(--mat-sidenav-content-background-color);
    min-width: 35vw;
    min-height: 55vh;
  }

  .section-title {
    padding-bottom: 8px;
  }

  rkt-alert {
    padding-bottom: 16px;
  }
}
