import { Component, computed, DestroyRef, inject, signal, viewChildren } from '@angular/core';
import { takeUntilDestroyed, toSignal } from '@angular/core/rxjs-interop';
import { MatButtonToggleModule } from '@angular/material/button-toggle';
import { MatAccordion, MatExpansionModule } from '@angular/material/expansion';
import { MatIconModule } from '@angular/material/icon';
import { ClosingCostGroup } from '@rocket-logic/rocket-logic-api-models/solutions';
import { RktAlertModule, RktSkeletonModule } from '@rocketcentral/rocket-design-system-angular';
import { PopoutComponent } from '../popout/popout.component';
import { LoanEstimateStateService } from '../services/entity-state/loan-estimate-state/loan-estimate-state.service';
import { ProductStateService } from '../services/entity-state/product-state/product-state.service';
import { LoanDetailsChangedService } from '../services/loan-details-changed/loan-details-changed.service';
import { OverlayService, PopoutContainer } from '../services/overlay/overlay.service';
import { ProductDescriptionPipe } from '../util/pipes/product-description.pipe';
import { ClosingCostsCashToCloseComponent } from './closing-costs-cash-to-close/closing-costs-cash-to-close.component';
import { ClosingCostsLoanCostsComponent } from './closing-costs-loan-costs/closing-costs-loan-costs.component';
import { ClosingCostsOtherCostsComponent } from './closing-costs-other-costs/closing-costs-other-costs.component';

enum ClosingCostsDataSource {
  LoanEstimate,
  ClosingDisclosure,
}

@Component({
  selector: 'app-closing-costs',
  standalone: true,
  imports: [
    MatIconModule,
    MatButtonToggleModule,
    MatExpansionModule,
    RktAlertModule,
    RktSkeletonModule,
    ProductDescriptionPipe,
    PopoutComponent,
    ClosingCostsLoanCostsComponent,
    ClosingCostsOtherCostsComponent,
    ClosingCostsCashToCloseComponent,
    ClosingCostsCashToCloseComponent,
  ],
  providers: [LoanEstimateStateService],
  templateUrl: './closing-costs.component.html',
  styleUrl: './closing-costs.component.scss',
})
export class ClosingCostsComponent extends PopoutContainer {
  ClosingCostsDataSource = ClosingCostsDataSource;
  private readonly destroyRef = inject(DestroyRef);
  private readonly accordions = viewChildren(MatAccordion);
  private readonly productStateService = inject(ProductStateService);
  private readonly loanEstimateStateService = inject(LoanEstimateStateService);
  private readonly overlayService = inject(OverlayService);
  private readonly loanSummary = inject(LoanDetailsChangedService).loanSummary;

  private readonly closingDisclosure = computed(() => this.product()?.closingCostDetailSummary);

  private readonly loanEstimateResponse = toSignal(
    this.loanEstimateStateService.loanEstimate$.pipe(takeUntilDestroyed(this.destroyRef)),
  );

  private readonly loanEstimate = computed(
    () => this.loanEstimateResponse()?.data?.detailsOfTransactionGroups,
  );

  readonly loanEstimateIsLoading = computed(() => this.loanEstimateResponse()?.loading ?? true);

  readonly loanEstimateFailedToLoad = computed(() => {
    const response = this.loanEstimateResponse();
    return response?.loading === false && response?.error != null;
  });

  readonly product = toSignal(this.productStateService.nonNullableData$);

  readonly isLoadingLoanEstimate = computed(() => this.loanEstimate() !== undefined);

  readonly dataSource = signal(ClosingCostsDataSource.ClosingDisclosure);
  readonly groupA = computed(() => this.detailsOfTransaction()?.groupA ?? this.emptyGroup());
  readonly groupB = computed(() => this.detailsOfTransaction()?.groupB ?? this.emptyGroup());
  readonly groupC = computed(() => this.detailsOfTransaction()?.groupC ?? this.emptyGroup());
  readonly totalLoanCosts = computed(() => this.detailsOfTransaction()?.totalLoanCosts ?? 0);

  readonly groupE = computed(() => this.detailsOfTransaction()?.groupE ?? this.emptyGroup());
  readonly groupF = computed(() => this.detailsOfTransaction()?.groupF ?? this.emptyGroup());
  readonly groupG = computed(() => this.detailsOfTransaction()?.groupG ?? this.emptyGroup());
  readonly groupH = computed(() => this.detailsOfTransaction()?.groupH ?? this.emptyGroup());
  readonly totalOtherCosts = computed(
    () => this.detailsOfTransaction()?.totalClientPaidOtherCosts ?? 0,
  );

  readonly totalClosingCosts = computed(
    () => this.detailsOfTransaction()?.totalClientPaidClosingCosts ?? 0,
  );
  readonly closingCostsFinanced = computed(
    () => this.detailsOfTransaction()?.totalFinancedClosingCosts ?? 0,
  );
  readonly downPayment = computed(() => this.detailsOfTransaction()?.totalDownPayment ?? 0);
  readonly deposit = computed(() => this.detailsOfTransaction()?.totalEarnestMoneyDeposit ?? 0);
  readonly fundsForBorrower = computed(
    () => this.detailsOfTransaction()?.totalClientPaidOutsideClosingCosts ?? 0,
  );
  readonly sellerCredits = computed(() => this.detailsOfTransaction()?.totalSellerConcessions ?? 0);
  readonly hasCashToClient = computed(() => {
    if (this.dataSource() === ClosingCostsDataSource.ClosingDisclosure) {
      return this.loanSummary()?.closingInformation?.cashToClientAtClosing ?? 0 > 0;
    } else {
      return this.loanEstimate()?.cashToClient ?? 0 > 0;
    }
  });
  readonly estimatedCash = computed(() => {
    if (this.dataSource() === ClosingCostsDataSource.ClosingDisclosure) {
      const summary = this.loanSummary();
      return this.hasCashToClient()
        ? summary?.closingInformation?.cashToClientAtClosing ?? 0
        : summary?.closingInformation?.cashFromClientAtClosing ?? 0;
    } else {
      return this.hasCashToClient()
        ? this.loanEstimate()?.cashToClient ?? 0
        : this.loanEstimate()?.cashFromClient ?? 0;
    }
  });
  readonly estimatedCashLabel = computed(() =>
    this.hasCashToClient() ? 'Estimated Cash To Client' : 'Estimated Cash From Client',
  );
  readonly adjustmentsAndOtherCredits = computed(
    () => this.detailsOfTransaction()?.adjustmentCreditsAndCharges ?? this.emptyGroup(),
  );

  readonly detailsOfTransaction = computed(() =>
    this.dataSource() === ClosingCostsDataSource.LoanEstimate
      ? this.loanEstimate()
      : this.closingDisclosure(),
  );

  switchDataSource(source: ClosingCostsDataSource): void {
    this.dataSource.set(source);
  }

  closePopout(): void {
    this.overlayService.detachPopout(ClosingCostsComponent);
  }

  private emptyGroup(): ClosingCostGroup {
    return {
      total: 0,
      items: [],
    };
  }
}
