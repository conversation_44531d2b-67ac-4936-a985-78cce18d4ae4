<app-popout
  titleText="Closing Costs - {{ this.product() | productDescription }}"
  (onClose)="closePopout()"
>
  <section>
    <div class="rkt-Spacing--pa16">
      <mat-button-toggle-group hideSingleSelectionIndicator="true">
        <mat-button-toggle
          (click)="switchDataSource(ClosingCostsDataSource.ClosingDisclosure)"
          [checked]="dataSource() === ClosingCostsDataSource.ClosingDisclosure"
          disableRipple="true"
        >
          Closing Disclosure
        </mat-button-toggle>
        <mat-button-toggle
          (click)="switchDataSource(ClosingCostsDataSource.LoanEstimate)"
          [checked]="dataSource() === ClosingCostsDataSource.LoanEstimate"
          disableRipple="true"
        >
          Loan Estimate
        </mat-button-toggle>
      </mat-button-toggle-group>
    </div>

    @if (dataSource() === ClosingCostsDataSource.LoanEstimate && loanEstimateIsLoading()) {
      <rkt-alert variant="info" [isDismissible]="false">
        We're still making your loan estimate. Check back in a few minutes!
      </rkt-alert>
    }

    @if (dataSource() === ClosingCostsDataSource.LoanEstimate && loanEstimateFailedToLoad()) {
      <rkt-alert variant="warn" [isDismissible]="false">
        The loan estimate failed to load. If this continues, please open a support ticket.
      </rkt-alert>
    }

    <div class="flex" style="gap: 1rem; padding-top: 16px">
      <div class="flex flex-col">
        <app-closing-costs-loan-costs
          [originationCharges]="groupA()"
          [servicesClientCannotShopFor]="groupB()"
          [servicesClientCanShopFor]="groupC()"
          [totalLoanCosts]="totalLoanCosts()"
        ></app-closing-costs-loan-costs>
      </div>

      <div class="flex flex-col">
        <app-closing-costs-other-costs
          [taxesAndOtherGovernmentFees]="groupE()"
          [prepaids]="groupF()"
          [initialEscrowPaymentAtClosing]="groupG()"
          [other]="groupH()"
          [totalOtherCosts]="totalOtherCosts()"
        />

        <app-closing-costs-cash-to-close
          [totalClosingCosts]="totalClosingCosts()"
          [closingCostsFinanced]="closingCostsFinanced()"
          [downPayment]="downPayment()"
          [deposit]="deposit()"
          [fundsForBorrower]="fundsForBorrower()"
          [sellerCredits]="sellerCredits()"
          [adjustmentsAndOtherCredits]="adjustmentsAndOtherCredits()"
          [estimatedCash]="estimatedCash()"
          [estimatedCashLabel]="estimatedCashLabel()"
        />
      </div>
    </div>
  </section>
</app-popout>
