import { CurrencyPipe } from '@angular/common';
import { Component, computed, input } from '@angular/core';
import { DetailsOfTransactionLineItem } from '@rocket-logic/rl-xp-bff-models';
import { ClosingCostItem } from '@rocket-logic/rocket-logic-api-models/solutions';

@Component({
  selector: 'app-closing-costs-group-content',
  standalone: true,
  imports: [CurrencyPipe],
  templateUrl: './closing-costs-group-content.component.html',
  styleUrl: './closing-costs-group-content.component.scss',
})
export class ClosingCostsGroupContentComponent {
  readonly items = input.required<ClosingCostItem[] | DetailsOfTransactionLineItem[] | undefined>();
  readonly lacksItems = computed(() => (this.items()?.length ?? 0) < 1);
}
