import { CurrencyPipe } from '@angular/common';
import { Component, input } from '@angular/core';
import { MatExpansionModule } from '@angular/material/expansion';
import { DetailsOfTransactionGroup } from '@rocket-logic/rl-xp-bff-models';
import { ClosingCostGroup } from '@rocket-logic/rocket-logic-api-models/solutions';
import { ClosingCostsGroupContentComponent } from '../closing-costs-group-content/closing-costs-group-content.component';
import { ClosingCostsGroupHeaderComponent } from '../closing-costs-group-header/closing-costs-group-header.component';

@Component({
  selector: 'app-closing-costs-loan-costs',
  standalone: true,
  imports: [
    MatExpansionModule,
    CurrencyPipe,
    ClosingCostsGroupContentComponent,
    ClosingCostsGroupHeaderComponent,
  ],
  templateUrl: './closing-costs-loan-costs.component.html',
  styleUrls: ['./closing-costs-loan-costs.component.scss', '../mat-expansion.scss'],
})
export class ClosingCostsLoanCostsComponent {
  readonly originationCharges = input.required<
    ClosingCostGroup | DetailsOfTransactionGroup | undefined
  >();
  readonly servicesClientCannotShopFor = input.required<
    ClosingCostGroup | DetailsOfTransactionGroup | undefined
  >();
  readonly servicesClientCanShopFor = input.required<
    ClosingCostGroup | DetailsOfTransactionGroup | undefined
  >();
  readonly totalLoanCosts = input.required<number | undefined>();
}
