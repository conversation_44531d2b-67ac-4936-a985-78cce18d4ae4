<div>
  <p class="rkt-Tracked-12 section-title"><PERSON>an <PERSON></p>
  <mat-accordion multi>
    @if (originationCharges(); as groupA) {
      <mat-expansion-panel
        #panelGroupA
        class="rkt-AccordionPanel"
        [hideToggle]="(groupA?.items?.length ?? 0) < 1"
      >
        <mat-expansion-panel-header>
          <app-closing-costs-group-header
            name="A. Origination Charges"
            [total]="groupA?.total ?? 0"
          />
        </mat-expansion-panel-header>

        <app-closing-costs-group-content
          class="rkt-AccordionPanel__content"
          [items]="groupA?.items"
        />
      </mat-expansion-panel>
    }

    @if (servicesClientCannotShopFor(); as groupB) {
      <mat-expansion-panel
        #panelGroupB
        class="rkt-AccordionPanel"
        [hideToggle]="(groupB?.items?.length ?? 0) < 1"
      >
        <mat-expansion-panel-header>
          <app-closing-costs-group-header
            name="B. Services Client Cannot Shop For"
            [total]="groupB?.total ?? 0"
          />
        </mat-expansion-panel-header>

        <app-closing-costs-group-content
          class="rkt-AccordionPanel__content"
          [items]="groupB?.items"
        />
      </mat-expansion-panel>
    }

    @if (servicesClientCanShopFor(); as groupC) {
      <mat-expansion-panel
        #panelGroupC
        class="rkt-AccordionPanel"
        [hideToggle]="(groupC?.items?.length ?? 0) < 1"
      >
        <mat-expansion-panel-header>
          <app-closing-costs-group-header
            name="C. Services Client Can Shop For"
            [total]="groupC?.total ?? 0"
          />
        </mat-expansion-panel-header>

        <app-closing-costs-group-content
          class="rkt-AccordionPanel__content"
          [items]="groupC?.items"
        />
      </mat-expansion-panel>
    }

    @if (totalLoanCosts(); as groupD) {
      <mat-expansion-panel
        #panelGroupD
        hideToggle="true"
        class="highlight rkt-AccordionPanel"
        (click)="panelGroupD.close()"
      >
        <mat-expansion-panel-header>
          <app-closing-costs-group-header
            [isBold]="true"
            name="D. Total Loan Costs"
            [total]="groupD ?? 0"
          />
        </mat-expansion-panel-header>
      </mat-expansion-panel>
    }
  </mat-accordion>
</div>
