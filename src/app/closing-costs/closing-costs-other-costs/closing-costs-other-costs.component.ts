import { CurrencyPipe } from '@angular/common';
import { Component, input } from '@angular/core';
import { MatExpansionModule } from '@angular/material/expansion';
import { DetailsOfTransactionGroup } from '@rocket-logic/rl-xp-bff-models';
import { ClosingCostGroup } from '@rocket-logic/rocket-logic-api-models/solutions';
import { ClosingCostsGroupContentComponent } from '../closing-costs-group-content/closing-costs-group-content.component';
import { ClosingCostsGroupHeaderComponent } from '../closing-costs-group-header/closing-costs-group-header.component';

@Component({
  selector: 'app-closing-costs-other-costs',
  standalone: true,
  imports: [
    MatExpansionModule,
    CurrencyPipe,
    ClosingCostsGroupContentComponent,
    ClosingCostsGroupHeaderComponent,
  ],
  templateUrl: './closing-costs-other-costs.component.html',
  styleUrls: ['./closing-costs-other-costs.component.scss', '../mat-expansion.scss'],
})
export class ClosingCostsOtherCostsComponent {
  readonly taxesAndOtherGovernmentFees = input.required<
    ClosingCostGroup | DetailsOfTransactionGroup | undefined
  >();
  readonly prepaids = input.required<ClosingCostGroup | DetailsOfTransactionGroup | undefined>();
  readonly initialEscrowPaymentAtClosing = input.required<
    ClosingCostGroup | DetailsOfTransactionGroup | undefined
  >();
  readonly other = input.required<ClosingCostGroup | DetailsOfTransactionGroup | undefined>();
  readonly totalOtherCosts = input.required<number | undefined>();
}
