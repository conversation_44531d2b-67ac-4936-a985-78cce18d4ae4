<div class="flex flex-col">
  <p class="rkt-Tracked-12 section-title">Other Costs</p>
  <mat-accordion multi>
    @if (taxesAndOtherGovernmentFees(); as groupE) {
      <mat-expansion-panel
        #panelGroupE
        class="rkt-AccordionPanel"
        [hideToggle]="(groupE?.items?.length ?? 0) < 1"
      >
        <mat-expansion-panel-header>
          <app-closing-costs-group-header
            name="E. Taxes And Other Govt. Fees"
            [total]="groupE?.total ?? 0"
          />
        </mat-expansion-panel-header>

        <app-closing-costs-group-content
          class="rkt-AccordionPanel__content"
          [items]="groupE?.items"
        />
      </mat-expansion-panel>
    }

    @if (prepaids(); as groupF) {
      <mat-expansion-panel
        #panelGroupF
        class="rkt-AccordionPanel"
        [hideToggle]="(groupF?.items?.length ?? 0) < 1"
      >
        <mat-expansion-panel-header>
          <app-closing-costs-group-header name="F. Prepaids" [total]="groupF?.total ?? 0" />
        </mat-expansion-panel-header>

        <app-closing-costs-group-content
          class="rkt-AccordionPanel__content"
          [items]="groupF?.items"
        />
      </mat-expansion-panel>
    }

    @if (initialEscrowPaymentAtClosing(); as groupG) {
      <mat-expansion-panel
        #panelGroupG
        class="rkt-AccordionPanel"
        [hideToggle]="(groupG?.items?.length ?? 0) < 1"
      >
        <mat-expansion-panel-header>
          <app-closing-costs-group-header
            name="G. Initial Escrow Payment At Closing"
            [total]="groupG?.total ?? 0"
          />
        </mat-expansion-panel-header>

        <app-closing-costs-group-content
          class="rkt-AccordionPanel__content"
          [items]="groupG?.items"
        />
      </mat-expansion-panel>
    }

    @if (other(); as groupH) {
      <mat-expansion-panel
        #panelGroupH
        class="rkt-AccordionPanel"
        [hideToggle]="(groupH?.items?.length ?? 0) < 1"
      >
        <mat-expansion-panel-header>
          <app-closing-costs-group-header name="H. Other" [total]="groupH?.total ?? 0" />
        </mat-expansion-panel-header>

        <app-closing-costs-group-content
          class="rkt-AccordionPanel__content"
          [items]="groupH?.items"
        />
      </mat-expansion-panel>
    }

    @if (totalOtherCosts(); as groupI) {
      <mat-expansion-panel
        #panelGroupI
        class="rkt-AccordionPanel highlight"
        hideToggle="true"
        (click)="panelGroupI.close()"
      >
        <mat-expansion-panel-header>
          <app-closing-costs-group-header
            [isBold]="true"
            name="I. Total Other Costs (Client Paid)"
            [total]="groupI ?? 0"
          />
        </mat-expansion-panel-header>
      </mat-expansion-panel>
    }
  </mat-accordion>
</div>
