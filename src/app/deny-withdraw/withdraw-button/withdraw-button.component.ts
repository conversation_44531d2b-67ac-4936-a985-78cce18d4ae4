import { Component, ViewContainerRef, computed, inject } from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import { MatDialog } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { DeactivationType } from '@rocket-logic/rl-xp-bff-models';
import { LoanStateService } from '../../services/entity-state/loan-state/loan-state.service';
import { LoanAccessVerifierReasons } from '../../services/user-authorization/default-access';
import { UserAuthorizationService } from '../../services/user-authorization/user-authorization.service';
import { DenyWithdrawErrorsComponent } from '../deny-withdraw-errors/deny-withdraw-errors.component';
import { DenyWithdrawFormComponent } from '../deny-withdraw-form/deny-withdraw-form.component';
import { MatButtonModule } from '@angular/material/button';

@Component({
  selector: 'app-withdraw-button',
  standalone: true,
  imports: [MatIconModule, MatButtonModule, MatMenuModule],
  templateUrl: './withdraw-button.component.html',
  styleUrl: './withdraw-button.component.scss',
})
export class WithdrawButtonComponent {
  private dialog = inject(MatDialog);
  readonly DeactivationType = DeactivationType;
  private loanStateService = inject(LoanStateService);
  private viewContainerRef = inject(ViewContainerRef);
  isLoanDenied = computed(() => this.loanStateService.state()?.data?.loanDeactivationDetails);
  isDenyWithdrawAllowed = toSignal(inject(UserAuthorizationService).isDenyWithdrawAllowed$);
  isBankerLicensed = toSignal(inject(UserAuthorizationService).isBankerLicensed$);

  openDialog(deactivationType: DeactivationType) {
    if (this.isDenyWithdrawAllowed() === LoanAccessVerifierReasons.FolderReceived) {
      this.dialog.open(DenyWithdrawErrorsComponent, {
        panelClass: 'rkt-Dialog',
        backdropClass: 'rkt-Backdrop',
        viewContainerRef: this.viewContainerRef,
      });
    } else {
      this.dialog.open(DenyWithdrawFormComponent, {
        data: { deactivationType },
        panelClass: 'rkt-Dialog',
        minWidth: '50%',
        minHeight: '50%',
        backdropClass: 'rkt-Backdrop',
        viewContainerRef: this.viewContainerRef,
      });
    }
  }
}
