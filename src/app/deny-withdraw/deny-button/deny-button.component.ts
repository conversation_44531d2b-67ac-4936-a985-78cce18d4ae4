import { Component, ViewContainerRef, computed, inject } from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import { MatDialog } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { DeactivationType } from '@rocket-logic/rl-xp-bff-models';
import { LoanStateService } from '../../services/entity-state/loan-state/loan-state.service';
import { LoanAccessVerifierReasons } from '../../services/user-authorization/default-access';
import { UserAuthorizationService } from '../../services/user-authorization/user-authorization.service';
import { DenyWithdrawErrorsComponent } from '../deny-withdraw-errors/deny-withdraw-errors.component';
import { DenyWithdrawFormComponent } from '../deny-withdraw-form/deny-withdraw-form.component';
import { MatButtonModule } from '@angular/material/button';
@Component({
  selector: 'app-deny-button',
  standalone: true,
  imports: [MatIconModule, MatButtonModule, MatMenuModule],
  templateUrl: './deny-button.component.html',
  styleUrl: './deny-button.component.scss',
})
export class DenyButtonComponent {
  private dialog = inject(MatDialog);
  private userAuthorizationService = inject(UserAuthorizationService);
  readonly DeactivationType = DeactivationType;
  private isLoanDeactivated = toSignal(inject(LoanStateService).isLoanDeactivated$);
  private viewContainerRef = inject(ViewContainerRef);
  isBankerLicensed = toSignal(inject(UserAuthorizationService).isBankerLicensed$);
  isDenyWithdrawAllowed = toSignal(inject(UserAuthorizationService).isDenyWithdrawAllowed$);

  shouldDisable = computed(
    () =>
      this.isLoanDeactivated() ||
      !this.isBankerLicensed()?.write ||
      this.userAuthorizationService.hasConflictingLoans(),
  );

  openDialog(deactivationType: DeactivationType) {
    if (this.isDenyWithdrawAllowed() === LoanAccessVerifierReasons.FolderReceived) {
      this.dialog.open(DenyWithdrawErrorsComponent, {
        panelClass: 'rkt-Dialog',
        backdropClass: 'rkt-Backdrop',
        viewContainerRef: this.viewContainerRef,
      });
    } else {
      this.dialog.open(DenyWithdrawFormComponent, {
        data: { deactivationType },
        panelClass: 'rkt-Dialog',
        minWidth: '50%',
        minHeight: '50%',
        backdropClass: 'rkt-Backdrop',
        viewContainerRef: this.viewContainerRef,
      });
    }
  }
}
