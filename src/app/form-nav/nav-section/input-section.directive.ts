import {
  computed,
  DestroyRef,
  Directive,
  DoCheck,
  effect,
  ElementRef,
  inject,
  input,
  <PERSON><PERSON><PERSON><PERSON>,
  OnInit,
  Renderer2,
} from '@angular/core';
import { takeUntilDestroyed, toObservable } from '@angular/core/rxjs-interop';
import { AbstractControl, NgControl } from '@angular/forms';
import { MAT_EXPANSION_PANEL } from '@angular/material/expansion';
import { MAT_FORM_FIELD } from '@angular/material/form-field';
import { SplunkLoggerService } from '@decision-services/angular-splunk-logger';
import { BehaviorSubject, combineLatest, EMPTY, map, of, startWith, switchMap, tap } from 'rxjs';
import { CONTROL_PROVIDER } from '../../_shared/tokens/control-provider';
import { MAT_FORM_FIELD_PROVIDER } from '../../_shared/tokens/mat-form-field-provider';
import { FormSectionComponent } from '../../form-section/form-section.component';
import {
  FormInput,
  FormNavInputService,
  ProductMilestoneFormInput,
} from '../../services/form-nav/form-nav-input.service';
import { FormNavSectionService } from '../../services/form-nav/form-nav-section.service';
import { SectionMilestoneService } from '../../services/form-nav/section-milestone.service';

export type FormSection = FormInput | ProductMilestoneFormInput;

@Directive({
  selector: '[appNavInput]',
  standalone: true,
})
export class InputSectionDirective implements OnInit, OnDestroy, DoCheck {
  private formNavService = inject(FormNavInputService);
  private formNavSectionService = inject(FormNavSectionService);
  private elementRef = inject(ElementRef);
  private destroyRef = inject(DestroyRef);
  private renderer = inject(Renderer2);
  private sectionMilestoneService = inject(SectionMilestoneService);
  private splunkLoggerService = inject(SplunkLoggerService);

  private formSectionComponentRef = inject(FormSectionComponent, { optional: true });
  private accordionRef = inject(MAT_EXPANSION_PANEL, { optional: true });
  private matFormField = inject(MAT_FORM_FIELD, { optional: true });
  private matFormFieldProvider = inject(MAT_FORM_FIELD_PROVIDER, { optional: true });
  private ngControl = inject(NgControl, { optional: true });
  private controlProvider = inject(CONTROL_PROVIDER, { optional: true });
  private controlProvider$ = this.controlProvider
    ? toObservable(this.controlProvider?.control)
    : of(null);

  private controlSubject = new BehaviorSubject<AbstractControl<any, any> | null | undefined>(
    this.ngControl?.control,
  );
  control$ = this.controlSubject.asObservable();

  inputSection = input<FormSection>();
  shouldRegister = input(true);

  private formSection = this.formSectionComponentRef?.formSection;
  private shouldRegister$ = toObservable(this.shouldRegister);
  private uuid = crypto.randomUUID();

  /**
   * A field is considered active if it is the currently viewed missing milestone.
   */
  isActiveSection = computed(
    () => this.formNavService.activeSection() === this.inputSection() + this.uuid,
  );

  /**
   * Section is considered outstanding if it is part of a milestone that is not yet completed.
   */
  isOutstandingSection = computed(
    () =>
      this.formNavService
        .availableSections()
        .some((section) => section === this.inputSection() + this.uuid) ||
      this.formNavService
        .availableProductSections()
        .some((section) => section === this.inputSection() + this.uuid),
  );

  isActiveFormSection = computed(() => {
    if (this.formSection) {
      this.formNavSectionService.activeSection() === this.formSection();
    }
  });

  constructor() {
    effect(() => {
      const matFormField = this.matFormFieldProvider?.matFormField() ?? this.matFormField;
      if (!matFormField) return;
      if (this.isOutstandingSection()) {
        this.renderer.addClass(matFormField._elementRef.nativeElement, 'active-input-section');
      } else {
        this.renderer.removeClass(matFormField._elementRef.nativeElement, 'active-input-section');
      }
    });
  }

  ngDoCheck(): void {
    if (this.ngControl?.control !== this.controlSubject.value) {
      this.controlSubject.next(this.ngControl?.control);
    }
  }

  ngOnInit(): void {
    this.shouldRegister$
      .pipe(
        switchMap((shouldRegister) => {
          const inputSection = this.inputSection();
          if (shouldRegister && inputSection) {
            this.formNavService.registerSection(
              inputSection,
              this.elementRef.nativeElement,
              this.uuid,
              () => {
                // Expand the form section containing this input
                this.formSectionComponentRef?.setSectionOpenState(true);
                // Expand any expansion panel containing this input
                this.accordionRef?.open();
              },
            );

            const formSection = this.formSectionComponentRef?.formSection();
            if (formSection) {
              this.sectionMilestoneService.registerMilestone(formSection, inputSection);
            }

            return combineLatest([this.controlProvider$, this.control$]).pipe(
              map(([controlProvider, control]) => (controlProvider ? controlProvider : control)),
              switchMap((control) => {
                if (!control) {
                  this.splunkLoggerService.error(
                    'There is no control associated with this directive',
                  );
                  return EMPTY;
                }

                return control.valueChanges.pipe(
                  startWith(control.value),
                  tap(() => {
                    const isValid =
                      control.valid &&
                      control.value != null &&
                      control.value !== '' &&
                      (Array.isArray(control.value) ? control.value.length : true);
                    const isDisabled = control.disabled;

                    this.formNavService.updateAvailableSections(
                      !!isValid,
                      shouldRegister,
                      !!isDisabled,
                      inputSection,
                      this.uuid,
                    );
                  }),
                );
              }),
            );
          } else {
            if (inputSection) {
              this.formNavService.cleanupSections(inputSection, this.elementRef, this.uuid);
            }
            return EMPTY;
          }
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }

  ngOnDestroy(): void {
    const inputSection = this.inputSection();
    if (inputSection) {
      this.formNavService.cleanupSections(inputSection, this.elementRef, this.uuid);
    }
  }
}
