import { Pipe, PipeTransform, inject } from '@angular/core';
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';
import { PricingErrorNavigationService } from '../services/pricing-navigation/pricing-error-navigation.service';

/**
 * Pipe to enhance error messages with clickable links for pricing fields
 * 
 * Usage:
 * {{ errorMessage | pricingErrorLink:fieldPath }}
 * {{ 'Purchase Price is required' | pricingErrorLink:'purchasePrice' }}
 */
@Pipe({
  name: 'pricingErrorLink',
  standalone: true
})
export class PricingErrorLinkPipe implements PipeTransform {
  private readonly sanitizer = inject(DomSanitizer);
  private readonly errorNavService = inject(PricingErrorNavigationService);

  transform(errorMessage: string, fieldPath: string, linkText?: string): SafeHtml {
    if (!errorMessage || !fieldPath) {
      return errorMessage;
    }

    // Check if this field can be navigated to
    if (!this.errorNavService.canNavigateToField(fieldPath)) {
      return errorMessage;
    }

    const fieldLabel = this.errorNavService.getFieldLabel(fieldPath);
    const textToLink = linkText || fieldLabel;

    let enhancedMessage = errorMessage;

    if (textToLink && errorMessage.includes(textToLink)) {
      // Replace the field name with a clickable link
      const linkHtml = `<button 
        class="pricing-field-link rkt-Link--inline-12" 
        style="background: none; border: none; padding: 0; color: inherit; text-decoration: underline; cursor: pointer; font: inherit;"
        onclick="window.navigateToPricingField('${fieldPath}')"
        title="Click to navigate to ${fieldLabel || fieldPath}"
      >${textToLink}</button>`;
      
      enhancedMessage = errorMessage.replace(textToLink, linkHtml);
    } else {
      // Make the entire message clickable
      enhancedMessage = `<span 
        class="pricing-field-link-full" 
        style="cursor: pointer; text-decoration: underline;"
        onclick="window.navigateToPricingField('${fieldPath}')"
        title="Click to navigate to ${fieldLabel || fieldPath}"
      >${errorMessage}</span>`;
    }

    return this.sanitizer.bypassSecurityTrustHtml(enhancedMessage);
  }
}
