<app-table-card titleText="Credits" icon="money-outlined" class="flex-1 w-0">
  <app-table-card-row label="Lender Paid Credit">
    @for (column of dataSource(); track column; let first = $first) {
      <app-table-card-column [class.active-product]="first" [isFetching]="column.isFetching()">
        <b>{{ column.lenderPaidCredit() ?? 0 | currency }}</b>
        @if (column.isEditable()) {
          <app-formatted-number-input
            id="pricing-credits-lender-paid-credit"
            label="Lender Paid Credit"
            prefix="$"
            [control]="
              productFormService.productForm.controls.credits.controls.manualLenderPaidCredit
            "
            [allowNegative]="false"
            customHint="Total Lender Paid Credit: {{ column.lenderPaidCredit() | currency }}"
            subscriptSizing="fixed"
            appEditableInput
          />
        }
      </app-table-card-column>
    }
  </app-table-card-row>

  <app-table-card-row label="Realtor Credits">
    @for (column of dataSource(); track column; let first = $first) {
      <app-table-card-column [class.active-product]="first" [isFetching]="column.isFetching()">
        <b>{{ column.realtorCredits() ?? 0 | currency }}</b>
        @if (column.isEditable()) {
          <app-formatted-number-input
            id="pricing-credits-realtor-credits"
            label="Realtor Credits"
            prefix="$"
            [control]="productFormService.productForm.controls.credits.controls.realtorCredits"
            [allowNegative]="false"
            [subscriptSizing]="'dynamic'"
            appEditableInput
          />
        }
      </app-table-card-column>
    }
  </app-table-card-row>

  <app-table-card-row label="Seller Concessions">
    @for (column of dataSource(); track column; let first = $first) {
      <app-table-card-column [class.active-product]="first" [isFetching]="column.isFetching()">
        <b>{{ column.sellerConcessions() ?? 0 | currency }}</b>
        @if (column.isEditable()) {
          <app-formatted-number-input
            id="pricing-credits-seller-concessions"
            label="Seller Concessions"
            prefix="$"
            [control]="productFormService.productForm.controls.credits.controls.sellerConcessions"
            [allowNegative]="false"
            subscriptSizing="dynamic"
            appEditableInput
          />
        }
      </app-table-card-column>
    }
  </app-table-card-row>
</app-table-card>

@if (diffDataSource()) {
  <app-pricing-diff-column [rows]="diffRows()" [isLoading]="isSnapshotLoading()" />
}
