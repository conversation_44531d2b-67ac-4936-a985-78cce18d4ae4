<app-table-card titleText="Purchase Info" icon="real_estate_agent-outlined" class="flex-1 w-0">
  <app-table-card-row label="Purchase Price">
    @for (column of dataSource(); track column; let first = $first) {
      <app-table-card-column [class.active-product]="first" [isFetching]="column.isFetching()">
        <b>{{ column.purchasePrice() | currency }}</b>
        @if (column.isEditable()) {
          <app-formatted-number-input
            id="pricing-purchase-info-purchase-price"
            [control]="loanForm.loanForm.controls.purchasePrice"
            [changedProperty]="ChangedProperty.PurchasePrice"
            [allowNegative]="false"
            prefix="$"
            label="Purchase Price"
            subscriptSizing="dynamic"
            appEditableInput
          />
        }
      </app-table-card-column>
    }
  </app-table-card-row>

  @if (hasAdjustedSalesAmount()) {
    <app-table-card-row label="Adjusted Sales Amount">
      @for (column of dataSource(); track column; let first = $first) {
        <app-table-card-column [class.active-product]="first" [isFetching]="column.isFetching()">
          <b>{{
            column.adjustedSalesAmount() ? (column.adjustedSalesAmount() | currency) : '--'
          }}</b>
        </app-table-card-column>
      }
    </app-table-card-row>
  }

  <app-table-card-row label="Base Loan Amount">
    @for (column of dataSource(); track column; let first = $first) {
      <app-table-card-column [class.active-product]="first" [isFetching]="column.isFetching()">
        <b>{{ column.baseLoanAmount() | currency }}</b>
        @if (column.isEditable()) {
          <app-formatted-number-input
            id="pricing-purchase-info-base-loan-amount"
            [changedProperty]="ChangedProperty.BaseLoanAmount"
            [control]="baseLoanAmountForm"
            [allowNegative]="false"
            prefix="$"
            label="Base Loan Amount"
            subscriptSizing="dynamic"
            appEditableInput
          />
        }
      </app-table-card-column>
    }
  </app-table-card-row>

  <app-table-card-row label="Total Loan Amount">
    @for (column of dataSource(); track column; let first = $first) {
      <app-table-card-column [class.active-product]="first" [isFetching]="column.isFetching()">
        <span>{{ column.totalLoanAmount() | currency }}</span>
        @if (first) {
          <button
            rktLinkEnterprise
            class="rkt-Link--inline-12"
            (click)="openPopupHandler.openPopup(CountyLimitsComponent, $event.target)"
          >
            View County Limits
          </button>
        }
      </app-table-card-column>
    }
  </app-table-card-row>

  <app-table-card-row label="Total Monthly Payment">
    @for (column of dataSource(); track column; let first = $first) {
      <app-table-card-column [class.active-product]="first" [isFetching]="column.isFetching()">
        <span>{{ column.totalMonthlyPayment() | currency }}</span>
        <button
          rktLinkEnterprise
          class="rkt-Link--inline-12"
          (click)="
            openPopupHandler.openPopup(
              MonthlyPaymentComponent,
              $event.target,
              false,
              column.productData
            )
          "
        >
          View Breakdown
        </button>
      </app-table-card-column>
    }
  </app-table-card-row>

  <app-table-card-row label="Rate / Base Points">
    @for (column of dataSource(); track column; let first = $first) {
      <app-table-card-column [class.active-product]="first" [isFetching]="column.isFetching()">
        <b>
          {{ column.rateBasePoints().rate | percent: '1.1-3' }} /
          {{ column.rateBasePoints().basePoints }}
        </b>
        @if (column.isEditable()) {
          <app-select-field
            id="pricing-purchase-info-rate-base-points"
            label="Rate/Base Points"
            [nullOption]="false"
            [options]="availableProductsService.basePriceOptions()"
            [control]="productForm.controls.baseRate"
            subscriptSizing="dynamic"
            appEditableInput
          />
        }
      </app-table-card-column>
    }
  </app-table-card-row>

  <app-table-card-row label="Buydown Option">
    @for (column of dataSource(); track column; let first = $first; let count = $count) {
      <app-table-card-column [class.active-product]="first" [isFetching]="column.isFetching()">
        <div class="whitespace-nowrap flex-1 text-ellipsis overflow-hidden w-0">
          @if ((column.buydownOption() ?? 0) > 0) {
            <b>{{ column.buydownOption() | buydown }}</b>
            (Total Savings: {{ column.buydownTable()?.totalBuydownAmount | currency }})
          } @else {
            <b>None</b>
          }
        </div>

        @if ((column.buydownOption() ?? 0) > 0) {
          <button
            rktLinkEnterprise
            class="rkt-Link--inline-12"
            (click)="
              $event.stopPropagation();
              openPopupHandler.openPopup(
                SavingsBreakdownComponent,
                $event.target,
                false,
                column.buydownTable
              )
            "
          >
            View Savings
          </button>
        }

        @if (column.isEditable()) {
          <div
            class="grid gap-2"
            [class.grid-cols-2]="count === 1"
            [class.grid-cols-1]="count > 1"
            input
          >
            <app-select-field
              id="pricing-purchase-info-buydown-options"
              label="Buydown Options"
              [nullOption]="false"
              [options]="buydownOptions"
              [control]="productForm.controls.buydown"
              [customHint]="buydownTableProvider.totalSavingsHint()"
              [class.col-span-2]="count === 1 && !needsSources()"
              subscriptSizing="fixed"
              appEditableInput
            />

            @if (needsSources()) {
              <app-select-field
                id="pricing-purchase-info-buydown-sources"
                label="Buydown Fund Sources"
                [nullOption]="false"
                [multiple]="true"
                [options]="buydownSourceOptions()"
                [control]="productForm.controls.buydownSources"
                subscriptSizing="fixed"
                appEditableInput
              />
            }

            @if (needsOptOut()) {
              <app-select-field
                id="pricing-purchase-info-buydown-opt-out-reason"
                label="Opt-Out Reason"
                [nullOption]="true"
                [options]="buydownOptOutOptions"
                [control]="productForm.controls.lenderPaidBuydownOptOutReason"
                [class.col-span-2]="count === 1"
                subscriptSizing="fixed"
                appEditableInput
              />
            }
          </div>
        }
      </app-table-card-column>
    }
  </app-table-card-row>

  <app-table-card-row label="Collected / Required Points">
    @for (column of dataSource(); track column; let first = $first) {
      <app-table-card-column [class.active-product]="first" [isFetching]="column.isFetching()">
        <b>
          {{ column.discountPoints().collectedPoints }} /
          {{ column.discountPoints().requiredPoints }}
        </b>
        <button
          rktLinkEnterprise
          class="rkt-Link--inline-12"
          (click)="
            $event.stopPropagation();
            openPopupHandler.openPopup(
              PricingAdjustmentComponent,
              $event.target,
              false,
              column.productData
            )
          "
        >
          {{ first ? 'View Adjustments' : 'Adjustments' }}
        </button>

        @if (column.isEditable()) {
          <app-formatted-number-input
            id="pricing-purchase-info-collected-discount-points"
            label="Collected Discount Points"
            [decimalLimit]="3"
            [control]="productForm.controls.collectedDiscountPoints"
            [allowNegative]="false"
            [customHint]="discountPointsHint()"
            subscriptSizing="dynamic"
            appEditableInput
          />
        }
      </app-table-card-column>
    }
  </app-table-card-row>

  <app-table-card-row label="Collected Discount Points Amt.">
    @for (column of dataSource(); track column; let first = $first) {
      <app-table-card-column [class.active-product]="first" [isFetching]="column.isFetching()">
        {{ column.collectedPointsAmount() | currency }}
      </app-table-card-column>
    }
  </app-table-card-row>

  <app-table-card-row label="APR">
    @for (column of dataSource(); track column; let first = $first) {
      <app-table-card-column [class.active-product]="first" [isFetching]="column.isFetching()">
        {{ column.apr() | percent: '1.1-3' }}
        <mat-icon
          (mouseenter)="
            openPopupHandler.openPopup(AprTooltipComponent, $event.target, false, {
              maxApr: column.maxApr,
              aprCushion: column.aprCushion,
            })
          "
          (mouseleave)="openPopupHandler.detachPopout(AprTooltipComponent)"
          svgIcon="info-outlined"
        ></mat-icon>
      </app-table-card-column>
    }
  </app-table-card-row>

  <app-table-card-row label="Estimated Cash From Client">
    @for (column of dataSource(); track column; let first = $first) {
      <app-table-card-column [class.active-product]="first" [isFetching]="column.isFetching()">
        {{ column.cashFromClient() | currency }}
      </app-table-card-column>
    }
  </app-table-card-row>

  <app-table-card-row label="Down Payment">
    @for (column of dataSource(); track column; let first = $first) {
      <app-table-card-column [class.active-product]="first" [isFetching]="column.isFetching()">
        <b
          >{{ column.downPayment().amount | currency }} /
          {{ column.downPayment().percentage | percent: '1.1-3' }}</b
        >
        @if (column.isEditable()) {
          <app-calculated-input
            id="pricing-purchase-info-down-payment"
            [changedProperty]="ChangedProperty.DownPaymentAmount"
            [total]="purchasePrice()"
            prefix="$"
            label="Down Payment"
            [allowNegative]="false"
            [control]="downpaymentControl"
            subscriptSizing="dynamic"
            appEditableInput
          />
        }
      </app-table-card-column>
    }
  </app-table-card-row>

  <app-table-card-row label="Closing Costs">
    @for (column of dataSource(); track column; let first = $first) {
      <app-table-card-column [class.active-product]="first" [isFetching]="column.isFetching()">
        {{ column.closingCosts() | currency }}
        @if (first) {
          <button
            ignoreHighlight
            mat-icon-button
            class="rkt-ButtonIcon"
            (click)="openPopupHandler.openPopup(ClosingCostsComponent, null, true)"
          >
            <mat-icon>open_in_new</mat-icon>
          </button>
        }
      </app-table-card-column>
    }
  </app-table-card-row>
</app-table-card>

@if (diffDataSource()) {
  <app-pricing-diff-column [rows]="diffRows()" [isLoading]="isSnapshotLoading()" />
}
