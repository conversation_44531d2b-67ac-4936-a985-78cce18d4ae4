<app-table-card titleText="Escrow" icon="receipt_long-outlined" class="flex-1 w-0">
  <app-table-card-row label="Monthly Escrow">
    @for (column of dataSource(); track column; let first = $first) {
      <app-table-card-column [class.active-product]="first" [isFetching]="column.isFetching()">
        <span>{{ column.monthlyEscrow() | currency }}</span>
      </app-table-card-column>
    }
  </app-table-card-row>
  <app-table-card-row label="Property Tax">
    @for (column of dataSource(); track column; let first = $first) {
      <app-table-card-column [class.active-product]="first" [isFetching]="column.isFetching()">
        <span>{{ (column.taxPayment() | paymentAmount) ?? 'None' }}</span>
        <a rktLinkEnterprise class="rkt-Link--inline-12" [href]="pqUrl()">Edit</a>
      </app-table-card-column>
    }
  </app-table-card-row>
  <app-table-card-row label="Insurance">
    @for (column of dataSource(); track column; let first = $first) {
      <app-table-card-column [class.active-product]="first" [isFetching]="column.isFetching()">
        <b>{{ column.insurancePayment() | paymentAmount }}</b>

        @if (column.isEditable()) {
          <app-formatted-number-input
            id="pricing-escrow-insurance-amount"
            label="Annual Insurance Amount"
            [control]="insuranceAmountControl"
            [allowNegative]="false"
            prefix="$"
            appEditableInput
            subscriptSizing="dynamic"
          />
        }
      </app-table-card-column>
    }
  </app-table-card-row>
  <app-table-card-row label="Waiver">
    @for (column of dataSource(); track column; let first = $first) {
      <app-table-card-column [class.active-product]="first" [isFetching]="column.isFetching()">
        <b>{{ (column.escrowWaiver() | pascalCaseSplit) || 'None' }}</b>

        @if (column.isEditable()) {
          <app-select-field
            id="pricing-escrow-waiver"
            appEditableInput
            label="Escrow Waiver"
            [control]="escrowWaiverControl"
            [options]="escrowWaiverOptions"
            placeholder="None"
            floatLabel="always"
            subscriptSizing="dynamic"
          />
        }
      </app-table-card-column>
    }
  </app-table-card-row>
</app-table-card>

@if (diffDataSource()) {
  <app-pricing-diff-column [rows]="diffRows()" [isLoading]="isSnapshotLoading()" />
}
