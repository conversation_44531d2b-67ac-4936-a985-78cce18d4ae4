<app-table-card titleText="Qualifying Info" icon="verified_user-outlined" class="flex-1 w-0">
  <app-table-card-row label="LTV">
    @for (column of dataSource(); track column; let first = $first) {
      <app-table-card-column [class.active-product]="first" [isFetching]="column.isFetching()">
        <b>{{ column.ltv() | percent: '1.1-2' }}</b>

        @if (column.isEditable()) {
          <app-formatted-number-input
            id="pricing-qualifying-info-ltv"
            [changedProperty]="ChangedProperty.Ltv"
            [allowNegative]="false"
            [control]="ltvControl"
            suffix="%"
            class="flex-1"
            label="LTV"
            subscriptSizing="fixed"
            appNavInput
            appEditableInput
          />
        }
      </app-table-card-column>
    }
  </app-table-card-row>
  <app-table-card-row label="CLTV">
    @for (column of dataSource(); track column; let first = $first) {
      <app-table-card-column [class.active-product]="first" [isFetching]="column.isFetching()">
        {{ column.cltv() | percent: '1.1-2' }}
      </app-table-card-column>
    }
  </app-table-card-row>
  <app-table-card-row label="DTI">
    @for (column of dataSource(); track column; let first = $first) {
      <app-table-card-column [class.active-product]="first" [isFetching]="column.isFetching()">
        <span>{{ column.dti() | percent: '1.1-2' }}</span>
        <mat-icon
          (mouseenter)="
            openPopupHandler.openPopup(DtiTooltipComponent, $event.target, false, {
              totalDti: column.totalDti,
              housingDti: column.housingDti,
            })
          "
          (mouseleave)="openPopupHandler.detachPopout(DtiTooltipComponent)"
          svgIcon="info-outlined"
        ></mat-icon>
      </app-table-card-column>
    }
  </app-table-card-row>
  <app-table-card-row label="Reggie Results">
    @for (column of dataSource(); track column; let first = $first) {
      <app-table-card-column [class.active-product]="first" [isFetching]="column.isFetching()">
        <div class="flex items-center">
          <button
            rktLinkEnterprise
            class="rkt-Caption-12"
            (click)="openInReggie(column.reggieLink()!)"
          >
            <span> View Results </span>
          </button>
        </div>
        <button (click)="openInReggie(column.reggieLink()!)">
          <mat-icon svgIcon="open_in_new-outlined" />
        </button>
      </app-table-card-column>
    }
  </app-table-card-row>
</app-table-card>

@if (diffDataSource()) {
  <app-pricing-diff-column [rows]="diffRows()" [isLoading]="isSnapshotLoading()" />
}
