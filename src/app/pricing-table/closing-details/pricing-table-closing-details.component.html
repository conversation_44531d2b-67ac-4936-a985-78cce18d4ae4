<app-table-card titleText="Closing Details" icon="today-outlined" class="flex-1 w-0">
  <app-table-card-row label="Anticipated Closing Date">
    @for (column of dataSource(); track column; let first = $first) {
      <app-table-card-column [class.active-product]="first" [isFetching]="column.isFetching()">
        <b>{{ (column.acd() | date: 'longDate') ?? 'None' }}</b>
        @if (column.isEditable()) {
          <app-formatted-date-input
            id="pricing-closing-details-anticipated-closing-date"
            label="Anticipated Closing Date"
            [control]="acdControl"
            subscriptSizing="dynamic"
            appEditableInput
            [minimumDate]="today"
          />
        }
      </app-table-card-column>
    }
  </app-table-card-row>
  <app-table-card-row label="Commitment Period">
    @for (column of dataSource(); track column; let first = $first) {
      <app-table-card-column [class.active-product]="first" [isFetching]="column.isFetching()">
        <div class="whitespace-nowrap flex-1 text-ellipsis overflow-hidden w-0">
          <b>{{ column.commitmentPeriod().days }} Days</b> (Expiration Date:
          {{ column.commitmentPeriod().date | date: 'MM/dd/yyyy' }})
        </div>
        @if (column.isEditable()) {
          <app-select-field
            id="pricing-closing-details-commitment-period"
            label="Commitment Period"
            [control]="
              productFormService.productForm.controls.closingDetails.controls.commitmentPeriodInDays
            "
            [options]="periodOptions()"
            [customHint]="hint()"
            subscriptSizing="fixed"
            appEditableInput
          />
        }
      </app-table-card-column>
    }
  </app-table-card-row>
</app-table-card>

@if (diffDataSource()) {
  <app-pricing-diff-column [rows]="diffRows()" [isLoading]="isSnapshotLoading()" />
}
