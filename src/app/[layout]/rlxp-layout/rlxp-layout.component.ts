import { AfterViewInit, Component, DestroyRef, ElementRef, inject, viewChild } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { Router, Scroll } from '@angular/router';
import { filter } from 'rxjs';

@Component({
  // eslint-disable-next-line @angular-eslint/component-selector
  selector: 'app-rlxp-layout',
  templateUrl: './rlxp-layout.component.html',
  styleUrls: ['./rlxp-layout.component.scss'],
  standalone: true,
  imports: [],
})
export class RlxpLayoutComponent implements AfterViewInit {
  private readonly router = inject(Router);
  private readonly destroyRef = inject(DestroyRef);
  private readonly scrollContainer = viewChild.required<ElementRef>('scrollContainer');

  ngAfterViewInit() {
    this.router.events
      .pipe(
        filter((event) => event instanceof Scroll),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe(() => {
        this.scrollContainer().nativeElement.scrollTo(0, 0);
      });
  }
}
