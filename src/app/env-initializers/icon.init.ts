import { inject } from '@angular/core';
import { MatIconRegistry } from '@angular/material/icon';
import { DomSanitizer } from '@angular/platform-browser';

export const iconInitFn = () => {
  const iconRegistry = inject(MatIconRegistry);
  const domSanitizer = inject(DomSanitizer);
  const handler = createRegistryHandler(iconRegistry, domSanitizer);
  handler('rl-icon-dark', 'assets/rl-icon-dark.svg');
  handler('rl-icon', 'assets/rl-icon.svg');
  handler('rl-withdraw', 'assets/withdraw.svg');
  handler('rl-deny', 'assets/deny.svg');
  handler('rl-crm', 'assets/CRM-icon.svg');
  handler('rl-assistant-circled', 'assets/assistant-circled.svg');
  handler('rl-assistant-sparkle', 'assets/assistant-sparkle.svg');
  handler('rl-assistant-sparkle-dark', 'assets/assistant-sparkle.svg');
  handler('link_off', 'assets/link_off.svg');
  handler('rl-loan-summary', 'assets/loan-summary.svg');
  handler('rl-loan-summary-dark', 'assets/loan-summary-dark.svg');
  handler('rl-income', 'assets/income.svg');
  handler('rl-assets', 'assets/assets.svg');
  handler('rl-reo', 'assets/reo.svg');
  handler('findings-history', 'assets/findings-history.svg');
  handler('findings-history-dark', 'assets/findings-history-dark.svg');
  handler('rl-loan-search', 'assets/loan-search.svg');
  handler('rl-empty-notifications', 'assets/empty-notifications.svg');
  handler('rl-empty-notifications-dark', 'assets/empty-notifications-dark.svg');
  handler('rl-block', 'assets/block.svg');
  handler('forbidden', 'assets/forbidden.svg');
  handler('credit-score', 'assets/credit-score.svg');
  handler('credit-score-dark', 'assets/credit-score-dark.svg');
  handler('dotted-check-circle', 'assets/dotted-check-circle.svg');
  handler('rotate_90_degrees_cw-outlined', 'assets/rotate_90_degrees_cw-outlined.svg');
};

const createRegistryHandler = (registry: MatIconRegistry, sanitizer: DomSanitizer) => {
  return (iconName: string, iconUrl: string) => {
    registry.addSvgIcon(iconName, sanitizer.bypassSecurityTrustResourceUrl(iconUrl));
  };
};
