import { Injectable, inject } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { AssetType } from '@rocket-logic/rl-xp-bff-models';
import { EMPTY, switchMap } from 'rxjs';
import { AssetFormService } from '../services/entity-state/asset-state/asset-form.service';
import { AssetStateService } from '../services/entity-state/asset-state/asset-state.service';
import { LumpSumDialogComponent } from './lump-sum-dialog/lump-sum-dialog.component';

@Injectable()
export class ResetLumpSumService {
  readonly assetsFormService = inject(AssetFormService);
  readonly assetStateService = inject(AssetStateService);
  private readonly dialog = inject(MatDialog);

  lumpSumFormGroup = this.assetsFormService.lumpSumForm;

  handleLumpSumOnAddAsset(value: AssetType) {
    if (!this.assetStateService.lumpSum()) {
      this.assetsFormService.addAsset(value);
      this.resetLumpSumFormGroup();
      return;
    }

    const dialogRef = this.dialog.open(LumpSumDialogComponent, {
      panelClass: 'rkt-Dialog',
      minWidth: '50%',
      minHeight: '50%',
      backdropClass: 'rkt-Backdrop',
    });

    dialogRef
      .afterClosed()
      .pipe(
        switchMap((result) => {
          if (result) {
            this.resetLumpSumFormGroup();
            return this.assetStateService.deleteLumpSumAsset$();
          } else {
            return EMPTY;
          }
        }),
      )
      .subscribe(() => {
        this.assetsFormService.addAsset(value);
      });
  }

  private resetLumpSumFormGroup() {
    this.lumpSumFormGroup?.reset({
      assetValue: null,
      assetType: AssetType.BorrowerEstimatedTotalAssets,
    });
  }
}
