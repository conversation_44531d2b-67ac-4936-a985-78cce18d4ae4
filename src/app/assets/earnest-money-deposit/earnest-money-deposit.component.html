<div [class.emd-container]="hasBorder()">
  @if (isInPricing() || isReadonly()) {
    <div class="header">
      <span class="rkt-Label-16 rkt-FontWeight--700">Earnest Money Deposit</span>
      <button
        mat-icon-button
        [matMenuTriggerFor]="assetOptions"
        class="rkt-ButtonIcon"
        [disabled]="isReadonly()"
        [class.rkt-ButtonIcon--is-disabled]="isReadonly()"
        data-testid="asset-options-menu-trigger"
      >
        <mat-icon class="rkt-Icon" color="accent">more_vert</mat-icon>
      </button>
      <mat-menu #assetOptions class="rkt-Menu" data-testid="asset-options-menu">
        <button
          [disabled]="isReadonly()"
          mat-menu-item
          class="rkt-Menu__item"
          (click)="onDeleteClick()"
          data-testid="delete-asset-button"
        >
          <mat-icon class="rkt-Icon" svgIcon="delete-outlined" />
          <span class="rkt-Menu__item-text">Delete</span>
        </button>
      </mat-menu>
    </div>
  }

  @if (!assetStateService.isFetching()) {
    <div class="grid-container" [formGroup]="assetFormGroup()">
      <app-formatted-number-input
        class="asset-value"
        [control]="assetFormGroup().controls.assetValue"
        [allowNegative]="false"
        prefix="$"
        [label]="assetValueLabel()"
      />
      <app-client-select [label]="assetOwnersLabel()" class="clients" [control]="ownersControl()" />
    </div>
  }
</div>
