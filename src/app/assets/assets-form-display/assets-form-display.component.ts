import { Component, inject, input } from '@angular/core';
import { FormGroup, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { AssetType } from '@rocket-logic/rl-xp-bff-models';
import { AssetFormService } from '../../services/entity-state/asset-state/asset-form.service';
import { AssetStateService } from '../../services/entity-state/asset-state/asset-state.service';
import { FormSection } from '../../services/form-nav/form-nav-section.service';
import { AccountAssetComponent } from '../account-asset/account-asset.component';
import { AssetBucket } from '../assets.component';
import { BaseAssetComponent } from '../base-asset/base-asset.component';
import { GiftAssetComponent } from '../gift-asset/gift-asset.component';
import { SubsidyAssetComponent } from '../subsidy-asset/subsidy-asset.component';

@Component({
  selector: 'app-assets-form-display',
  standalone: true,
  imports: [
    MatFormFieldModule,
    MatInputModule,
    ReactiveFormsModule,
    MatButtonModule,
    MatIconModule,
    MatSelectModule,
    MatSlideToggleModule,
    AccountAssetComponent,
    GiftAssetComponent,
    BaseAssetComponent,
    SubsidyAssetComponent,
    MatCardModule,
    MatExpansionModule,
  ],
  templateUrl: './assets-form-display.component.html',
  styleUrl: './assets-form-display.component.scss',
})
export class AssetsFormDisplayComponent {
  assetsFormService = inject(AssetFormService);
  assetStateService = inject(AssetStateService);

  clientFormMap = input.required<Map<string, FormGroup>>();

  readonly AssetBucket = AssetBucket;
  readonly AssetType = AssetType;
  readonly FormSection = FormSection;

  onDeleteClick(assetKey: string) {
    this.assetsFormService.deleteAsset(assetKey);
  }

  mapAssetBucket(assetType: AssetType) {
    switch (assetType) {
      case AssetType.CheckingAccount:
      case AssetType.SavingsAccount:
      case AssetType.MoneyMarket:
      case AssetType.CertificateOfDeposit:
      case AssetType._401K:
      case AssetType.IndividualRetirementArrangement:
      case AssetType._403B:
      case AssetType._457Plan:
      case AssetType.ThriftSavingsPlan:
      case AssetType.SimplifiedEmployeePension:
      case AssetType.Annuity:
      case AssetType.Keogh:
      case AssetType.BrokerageAccount:
      case AssetType.MutualFund:
      case AssetType._529CollegeSavingsPlan:
      case AssetType.LifeInsurance:
      case AssetType.TrustAccount:
      case AssetType.PledgedAssetAccount:
        return AssetBucket.Account;
      case AssetType.GiftOfCash:
      case AssetType.GiftOfEquity:
        return AssetBucket.Gift;
      case AssetType.Grant:
        return AssetBucket.Subsidy;
      default:
        return AssetBucket.Base;
    }
  }
}
