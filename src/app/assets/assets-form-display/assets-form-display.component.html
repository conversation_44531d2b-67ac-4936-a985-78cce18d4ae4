@for (assetData of clientFormMap(); track assetData[0]) {
  @if (assetData[1].value.assetType !== AssetType.BorrowerEstimatedTotalAssets) {
    <section>
      @switch (mapAssetBucket(assetData[1].value.assetType)) {
        @case (AssetBucket.Account) {
          <app-account-asset
            (deleteFormEmitter)="onDeleteClick(assetData[0])"
            [formGroup]="assetData[1]"
          />
        }
        @case (AssetBucket.Gift) {
          <app-gift-asset
            (deleteFormEmitter)="onDeleteClick(assetData[0])"
            [formGroup]="assetData[1]"
          />
        }
        @case (AssetBucket.Subsidy) {
          <app-subsidy-asset
            [formGroup]="assetData[1]"
            (deleteFormEmitter)="onDeleteClick(assetData[0])"
          />
        }
        @default {
          <app-base-asset
            [formGroup]="assetData[1]"
            (deleteFormEmitter)="onDeleteClick(assetData[0])"
          />
        }
      }
    </section>
  }
}
