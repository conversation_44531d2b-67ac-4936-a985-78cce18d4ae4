import { <PERSON><PERSON><PERSON>cyPipe } from '@angular/common';
import { Component, computed, inject, viewChild } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { FormSectionComponent } from '../form-section/form-section.component';
import { InteractibleTileComponent } from '../interactible-tile/interactible-tile.component';
import { TileSkeletonComponent } from '../interactible-tile/tile-skeleton/tile-skeleton.component';
import { AssetFormService } from '../services/entity-state/asset-state/asset-form.service';
import { AssetStateService } from '../services/entity-state/asset-state/asset-state.service';
import { FormNavSectionService, FormSection } from '../services/form-nav/form-nav-section.service';
import { openFormSection } from '../util/open-form-section';
import { PascalCaseSplitPipe } from '../util/pascal-case-split.pipe';
import { AssetLandingFormComponent } from './asset-landing-form/asset-landing-form.component';
import { AssetsFormDisplayComponent } from './assets-form-display/assets-form-display.component';
import { AssetsSkeletonComponent } from './assets-skeleton/assets-skeleton.component';
import { EarnestMoneyDepositComponent } from './earnest-money-deposit/earnest-money-deposit.component';

export enum AssetBucket {
  Base = 'Base',
  Account = 'Account',
  Gift = 'Gift',
  Subsidy = 'Subsidy',
}

@Component({
  selector: 'app-assets',
  standalone: true,
  imports: [
    FormSectionComponent,
    MatFormFieldModule,
    MatInputModule,
    ReactiveFormsModule,
    MatButtonModule,
    MatIconModule,
    MatSelectModule,
    MatSlideToggleModule,
    MatCardModule,
    MatExpansionModule,
    PascalCaseSplitPipe,
    InteractibleTileComponent,
    TileSkeletonComponent,
    CurrencyPipe,
    AssetsFormDisplayComponent,
    AssetsSkeletonComponent,
    AssetLandingFormComponent,
    EarnestMoneyDepositComponent,
  ],
  templateUrl: './assets.component.html',
  styleUrl: './assets.component.scss',
})
export class AssetsComponent {
  readonly assetsFormService = inject(AssetFormService);
  readonly assetStateService = inject(AssetStateService);
  readonly formNavSectionService = inject(FormNavSectionService);

  readonly formSectionComponentRef = viewChild.required(FormSectionComponent);
  readonly earnestMoneyFormGroup = this.assetsFormService.emdForm;
  readonly hasAssets = computed(() => this.assetsFormService.entityValues().length > 0);

  readonly FormSection = FormSection;

  openFormSection() {
    openFormSection(() => this.formSectionComponentRef());
  }
}
