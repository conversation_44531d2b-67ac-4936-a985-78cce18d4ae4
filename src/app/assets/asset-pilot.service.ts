import { Injectable, inject } from '@angular/core';
import { filter, map, switchMap } from 'rxjs';
import { AuthorizationService, Pilot } from '../services/authorization/authorization.service';
import { LoanStateService } from '../services/entity-state/loan-state/loan-state.service';
import { LoanIdService } from '../services/loan-id/loan-id.service';

@Injectable()
export class AssetPilotService {
  private loanStateService = inject(LoanStateService);
  private loanIdService = inject(LoanIdService);
  private authService = inject(AuthorizationService);

  rocketLogicAssetDecision$ = this.loanStateService.isInitialized$.pipe(
    filter((isInit) => isInit),
    switchMap(() => this.loanIdService.nonNullableLoanId$),
    switchMap((loanNumber) => {
      return this.authService.getLoanAccessV4(loanNumber, [Pilot.RocketLogicAssets]);
    }),
    map((response) => {
      return response[Pilot.RocketLogicAssets];
    }),
  );

  isInAssetsPilot$ = this.rocketLogicAssetDecision$.pipe(
    map((decision) => decision.accessDecision),
  );
  documentBackedAssetTypes$ = this.rocketLogicAssetDecision$.pipe(
    map((decision) => decision.features),
  );
}
