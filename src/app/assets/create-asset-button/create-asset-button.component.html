<button
  [disabled]="isDisabled()"
  [matMenuTriggerFor]="assetTypes"
  [class.rkt-Button--is-disabled]="isDisabled()"
  mat-button
  aria-label="Open Asset Types Menu"
  class="rkt-Button rkt-Button--has-icon"
  [class.rkt-Button--large]="isLarge()"
  color="accent"
  [ngClass]="getButtonClass()"
  [attr.mat-stroked-button]="isStroked()"
  data-testid="add-asset-button"
>
  <mat-icon class="rkt-Icon" color="{{ color() }}" svgIcon="{{ icon() }}" />
  {{ buttonLabel() }}
</button>
<mat-menu #assetTypes="matMenu" class="rkt-menu">
  @for (mainMenuItem of choices(); track mainMenuItem) {
    @if (mainMenuItem.options.length > 1) {
      <button
        [matMenuTriggerFor]="assetMenuItem"
        mat-menu-item
        class="rkt-Menu__item"
        [attr.data-synthetic-monitor-id]="'rlxp-create-category-' + $index"
      >
        <span class="rkt-Menu__item-text"> {{ mainMenuItem.category }}</span>
      </button>
      <mat-menu #assetMenuItem>
        <button
          [attr.data-synthetic-monitor-id]="'rlxp-create-asset-' + assetChoice.assetType"
          (click)="createAsset(assetChoice.assetType)"
          *ngFor="let assetChoice of mainMenuItem.options"
          class="rkt-Menu__item"
          mat-menu-item
        >
          <span class="rkt-Menu__item-text">{{ assetChoice.display }}</span>
        </button>
      </mat-menu>
    }
    @if (mainMenuItem.options.length === 1) {
      <button
        (click)="createAsset(mainMenuItem.options[0].assetType)"
        [attr.data-synthetic-monitor-id]="'rlxp-create-asset-' + mainMenuItem.options[0].assetType"
        class="rkt-Menu__item"
        mat-menu-item
      >
        <span class="rkt-Menu__item-text">{{ mainMenuItem.options[0].display }}</span>
      </button>
    }
  }
</mat-menu>
