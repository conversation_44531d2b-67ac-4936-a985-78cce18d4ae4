import { Component, computed, inject } from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { FormattedNumberInputComponent } from '../../question-input/formatted-number-input/formatted-number-input.component';
import { ActiveActionService } from '../../services/active-action.service';
import { AssetFormService } from '../../services/entity-state/asset-state/asset-form.service';
import { SchwabService } from '../../services/schwab/schwab.service';
import { AssetPilotService } from '../asset-pilot.service';
import { CreateAssetButtonComponent } from '../create-asset-button/create-asset-button.component';
import { ResetLumpSumService } from '../reset-lump-sum.service.component';
import { SchwabAssetsImportComponent } from '../schwab-assets-import/schwab-assets-import.component';

@Component({
  selector: 'app-asset-landing-form',
  standalone: true,
  imports: [
    CreateAssetButtonComponent,
    FormattedNumberInputComponent,
    SchwabAssetsImportComponent,
    ReactiveFormsModule,
  ],
  templateUrl: './asset-landing-form.component.html',
  styleUrl: './asset-landing-form.component.scss',
})
export class AssetLandingFormComponent {
  private readonly schwabService = inject(SchwabService);
  private readonly assetPilotService = inject(AssetPilotService);
  private readonly activeActionService = inject(ActiveActionService);
  readonly assetsFormService = inject(AssetFormService);
  readonly resetLumpSumService = inject(ResetLumpSumService);

  lumpSumFormGroup = this.assetsFormService.lumpSumForm;

  isSchwab = toSignal(this.schwabService.isSchwab$);
  isInAssetsPilot = toSignal(this.assetPilotService.isInAssetsPilot$);
  areCreditMilestoneComplete = computed(
    () => this.activeActionService.creditItemsToComplete() === 0,
  );

  lumpSumAssetValueControl = this.lumpSumFormGroup.get('assetValue') as FormControl;
}
