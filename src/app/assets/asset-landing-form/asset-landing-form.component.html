<div class="flex flex-col" [class.gap-3]="isSchwab()">
  <div class="row">
    <app-create-asset-button
      [buttonLabel]="
        assetsFormService.entityValues().length ? 'Add Additional Assets' : 'Add Assets'
      "
      (createTypeSelected)="resetLumpSumService.handleLumpSumOnAddAsset($event)"
    />
    @if (!assetsFormService.entityValues().length && isInAssetsPilot()) {
      <span>or</span>

      <ng-container [formGroup]="lumpSumFormGroup">
        <app-formatted-number-input
          class="asset-value"
          [control]="lumpSumAssetValueControl"
          [allowNegative]="false"
          prefix="$"
          label="Lump Sum Amount"
          subscriptSizing="dynamic"
        />
      </ng-container>
    }
  </div>

  @if (isSchwab() && areCreditMilestoneComplete()) {
    <app-schwab-assets-import />
  }
</div>
