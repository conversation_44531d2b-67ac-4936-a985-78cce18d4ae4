<app-form-section
  titleText="Asset(s)"
  icon="rl-assets"
  [isCollapsible]="assetsFormService.entityValues().length > 0"
  [formSection]="FormSection.Assets"
>
  @if (!assetStateService.isFetching()) {
    @if (!earnestMoneyFormGroup.controls.noEmd.value) {
      <app-earnest-money-deposit [hasBorder]="hasAssets()" [isReadonly]="true" />
    }
    <app-assets-form-display [clientFormMap]="assetsFormService.entityFormMap()" />
    <app-asset-landing-form />
  } @else {
    <app-assets-skeleton />
  }
  @if (!assetStateService.isFetching()) {
    <div section-summary class="row flex-wrap">
      @for (asset of assetsFormService.entityValues(); track asset) {
        <app-interactible-tile
          [label]="asset.value.assetType | pascalCaseSplit"
          [content]="asset.value.assetValue | currency"
          (tileClick)="openFormSection()"
        ></app-interactible-tile>
      }
    </div>
  } @else {
    <div section-summary class="row flex-wrap mt-2">
      <app-tile-skeleton />
    </div>
  }
</app-form-section>
