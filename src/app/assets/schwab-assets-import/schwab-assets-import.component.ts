import { Component, computed, effect, inject, signal, viewChild } from '@angular/core';
import { takeUntilDestroyed, toObservable, toSignal } from '@angular/core/rxjs-interop';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { MatButton, MatButtonModule } from '@angular/material/button';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { debounceTime, pairwise, switchMap, take } from 'rxjs';
import { AssetDataPullService } from '../../services/asset/asset-data-pull.service';
import { LoanEditingState } from '../../services/entity-state/loan-state/loan-editing-state.service';
import { LoanStateService } from '../../services/entity-state/loan-state/loan-state.service';
import { LeadService } from '../../services/lead/lead.service';
import { SchwabService } from '../../services/schwab/schwab.service';

@Component({
  selector: 'app-schwab-assets-import',
  standalone: true,
  imports: [
    MatButtonModule,
    MatIconModule,
    MatCheckboxModule,
    MatProgressSpinnerModule,
    ReactiveFormsModule,
  ],
  templateUrl: './schwab-assets-import.component.html',
  styleUrl: './schwab-assets-import.component.scss',
})
export class SchwabAssetsImportComponent {
  private loanStateService = inject(LoanStateService);
  private loanEditingState  = inject(LoanEditingState);
  private schwabService = inject(SchwabService);
  private assetDataPullState$ = toObservable(inject(AssetDataPullService).assetDataPullState);
  private hasSchwabConsent$ = inject(LeadService).hasSchwabConsent$;
  assetDataPullButton = viewChild<MatButton>('#schwabDataPullButton');

  checkboxFormControl = new FormControl<boolean>(false);
  checkboxFormValue = toSignal(this.checkboxFormControl.valueChanges);

  isLoading = signal<boolean>(false);
  isDisabled = computed(
    () =>
      !this.checkboxFormValue() ||
      this.isLoading() ||
      this.loanEditingState.isLoanEditingDisabled(),
  );

  constructor() {
    effect(() => {
      if (this.isDisabled() && this.assetDataPullButton()) {
        this.assetDataPullButton()!.disabled = true;
      }
    });

    effect(() => {
      if (this.loanEditingState.isLoanEditingDisabled()) {
        this.checkboxFormControl.disable();
      }
    });

    this.assetDataPullState$.pipe(pairwise(), takeUntilDestroyed()).subscribe(([prev, next]) => {
      if (prev?.status == null && next?.status != null) {
        this.isLoading.set(false);
      }
    });

    this.hasSchwabConsent$
      .pipe(take(1))
      .subscribe((value) => this.checkboxFormControl.setValue(value));

    this.checkboxFormControl.valueChanges
      .pipe(
        debounceTime(500),
        switchMap((checked) =>
          this.loanStateService.setConsentToObtainSchwabAssets$(checked ?? false),
        ),
        takeUntilDestroyed(),
      )
      .subscribe();
  }

  onSchwabAssetsImport() {
    this.isLoading.set(true);
    this.schwabService.startSchwabAssetImport$.subscribe();
  }
}
