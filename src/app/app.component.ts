import { Component, inject } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { ActivationStart, EventType, Router, RouterModule } from '@angular/router';
import { LargestContentPaintTrackerService } from '@rocket-logic/ngx-web-vitals';
import { AnalyticsBrowser } from '@segment/analytics-next';
import { distinctUntilChanged, filter, map, startWith, withLatestFrom } from 'rxjs';
import { DarkModeService } from './services/dark-mode/dark-mode.service';
import { PricingGlobalNavigationService } from './services/pricing-navigation/pricing-global-navigation.service';

@Component({
  selector: 'app-root',
  standalone: true,
  imports: [RouterModule],
  providers: [],
  templateUrl: './app.component.html',
  styleUrl: './app.component.scss',
})
export class AppComponent {
  // Injected to initialize darkmode for the entire application
  private readonly darkModeService = inject(DarkModeService);
  private readonly lcpTracker = inject(LargestContentPaintTrackerService);
  private readonly analytics = inject(AnalyticsBrowser, { optional: true });
  private readonly router = inject(Router);
  private readonly pricingGlobalNav = inject(PricingGlobalNavigationService, { optional: true });
  private readonly loanId$ = this.router.events.pipe(
    filter((event): event is ActivationStart => event.type === EventType.ActivationStart),
    map((event) => event.snapshot.paramMap.get('loanId')),
    startWith(null),
    distinctUntilChanged(),
    takeUntilDestroyed(),
  );

  constructor() {
    this.lcpTracker.init(document.body);

    // Initialize pricing navigation global functions after a delay to ensure all services are ready
    setTimeout(() => {
      try {
        if (this.pricingGlobalNav) {
          this.pricingGlobalNav.initialize();
        }
      } catch (error) {
        console.warn('Failed to initialize pricing navigation:', error);
      }
    }, 100);

    if (this.analytics) {
      this.lcpTracker.finalLCP$
        .pipe(withLatestFrom(this.loanId$), takeUntilDestroyed())
        .subscribe(([lcpEntry, loanNumber]) => {
          this.analytics?.track('Largest Content Paint', {
            lcpTime: lcpEntry.lcpTime,
            loanNumber,
          });
        });
    }
  }
}
