import { ContentObserver } from '@angular/cdk/observers';
import {
  DestroyRef,
  Directive,
  ElementRef,
  inject,
  InjectionToken,
  input,
  NgZone,
  signal,
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { filter, map, startWith, switchMap, timer } from 'rxjs';

export const HIGHLIGHT_TIME = new InjectionToken<number>('HIGHLIGHT_TIME');
const IGNORE_HIGHLIGHT_ATTRIBUTE = 'ignoreHighlight';

@Directive({
  selector: '[appHighlightOnChange]',
  standalone: true,
  host: {
    '[class.highlight]': 'highlight()',
  },
})
export class HighlightOnChangeDirective {
  private readonly contentObserver = inject(ContentObserver);
  private readonly elementRef = inject<ElementRef<HTMLElement>>(ElementRef);
  private readonly destroyRef = inject(DestroyRef);
  private readonly ngZone = inject(NgZone);

  readonly highlightTime = input<number>(inject(HIGHLIGHT_TIME));

  private readonly highlight = signal(false);

  ngAfterContentInit() {
    const createHighlightTimer = () =>
      timer(this.highlightTime()).pipe(
        map(() => false),
        startWith(true),
      );

    this.contentObserver
      .observe(this.elementRef)
      .pipe(
        filter(
          (records) =>
            !records.some(
              (record) =>
                record.target.nodeType === Node.ELEMENT_NODE &&
                ((record.target as Element).hasAttribute(IGNORE_HIGHLIGHT_ATTRIBUTE) ||
                  record.target.parentElement?.hasAttribute(IGNORE_HIGHLIGHT_ATTRIBUTE)),
            ),
        ),
        switchMap(() => createHighlightTimer()),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe((value) => {
        // Content observer events trigger outside the angular zone
        this.ngZone.run(() => this.highlight.set(value));
      });
  }
}
